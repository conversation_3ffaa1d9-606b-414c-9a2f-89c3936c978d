<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <!-- 继承自spring boot的配置 -->

    <!-- 应用名称，defaultValue设置为项目名称，如果不设置则刚启动阶段的几行日志会生成一个IS_UNDEFINED.log文件 -->
    <springProperty scope="context" name="appName" source="spring.application.name" defaultValue="app"/>

    <!-- 日志文件名称:应用名称.log -->
    <property name="LOG_FILE" value="log/${appName}.log"/>
    <!-- 归档后日志压缩文件大小限制 -->
    <property name="LOG_FILE_MAX_SIZE" value="10MB"/>
    <!-- 存放多少天的日志文件，默认1年 -->
    <property name="LOG_FILE_MAX_HISTORY" value="365"/>

    <!-- 日志格式增加代码行 -->
    <property name="CONSOLE_LOG_PATTERN" value="%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}%4L){cyan} %clr(:){faint} %m%X{requestUuid}%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />
    <property name="FILE_LOG_PATTERN" value="%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39}%4L : %m%X{requestUuid}%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />

    <!-- 默认配置：https://github.com/spring-projects/spring-boot/blob/v2.1.6.RELEASE/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/logback/base.xml -->
    <include resource="org/springframework/boot/logging/logback/base.xml"/>

    <!-- 忽略swagger的解析参数异常日志 -->
    <logger name="io.swagger.models.parameters.AbstractSerializableParameter" level="error" />

    <!-- 错误日志单独输出 -->
    <property name="ERROR_LOG_FILE" value="log/${appName}-error.log"/>
    <appender name="FILE_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${ERROR_LOG_FILE}</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <cleanHistoryOnStart>${LOG_FILE_CLEAN_HISTORY_ON_START:-false}</cleanHistoryOnStart>
            <fileNamePattern>${ROLLING_FILE_NAME_PATTERN:-${ERROR_LOG_FILE}.%d{yyyy-MM-dd}.%i.gz}</fileNamePattern>
            <maxFileSize>${LOG_FILE_MAX_SIZE:-10MB}</maxFileSize>
            <maxHistory>${LOG_FILE_MAX_HISTORY:-7}</maxHistory>
            <totalSizeCap>${LOG_FILE_TOTAL_SIZE_CAP:-0}</totalSizeCap>
        </rollingPolicy>
        <filter class="com.pugwoo.admin.utils.LevelFilter">
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="FILE_ERROR" />
    </root>

</configuration>