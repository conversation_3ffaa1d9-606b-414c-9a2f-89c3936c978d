[v-cloak] {display: none;}

* {
	font-family: "Helvetica Neue", Helvetica, "PingFang SC",
		"Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
	margin: 0; padding: 0;
}

#app {margin: 10px}

/*一些常用css*/
.pw-block-center {display: block; margin: auto;} /*块元素居中*/

/*修改element2.x主题*/
.el-table {color: #000; font-size: 16px;} /*表格字体和颜色*/
.el-table thead {color: #1f2d3d} /*表头字体颜色*/
.el-table th, .el-table thead tr {background-color: #eef1f6} /*表头背景*/
.el-table tr td {padding: 5px 0} /*减少表行间距*/
.el-menu {border-right: 0px} /*解决弹框白边问题*/
.el-pagination.is-background .el-pager li {margin: 0 1px;} /*分页间距*/
.el-form-item__label {color:#000} /*表单label字体*/
.el-button--danger {background-color: #ff4949;border-color: #ff4949;} /*按钮颜色*/
.el-button--primary {background-color: #20a0ff;border-color: #20a0ff;} /*按钮颜色*/
.el-input__inner {color:#000} /*输入框字体颜色*/
.el-button--default {color:#000} /*默认按钮字体颜色 */
.el-form-item {margin-bottom: 5px;} /*按钮下间距*/
.el-form--inline .el-form-item {margin-right: 5px;} /*按钮右间距*/
.el-button+.el-button {margin-left: 5px;} /*按钮左间距*/
.el-table td, .el-table th {padding: 6px 0;} /*表头行间距*/
.el-form-item__error {padding-top: 1px;background-color: #fff;z-index:10;} /*修改间距后form表单错误信息显示*/
.el-switch {height: 40px;} /*修复switch在表单中没有对齐的问题*/
.el-menu-item.is-active{background-color: #eff1f2 !important} /*要配合el-menu background-color来设置*/

/*滚动条*/
::-webkit-scrollbar {width: 5px;}
::-webkit-scrollbar-track {background-color: transparent;} /* 滚动条的滑轨背景颜色 */
::-webkit-scrollbar-thumb {background-color: rgba(0, 195, 195, 0.5);border-radius: 5px;} /* 滑块颜色 */
::-webkit-scrollbar-button {display: none;} /* 滑轨两头的监听按钮颜色 */