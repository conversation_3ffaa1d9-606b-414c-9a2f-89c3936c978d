/*!
 * vue-resource v1.5.3
 * https://github.com/pagekit/vue-resource
 * Released under the MIT License.
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.VueResource=e()}(this,function(){"use strict";function s(t){this.state=2,this.value=void 0,this.deferred=[];var e=this;try{t(function(t){e.resolve(t)},function(t){e.reject(t)})}catch(t){e.reject(t)}}s.reject=function(n){return new s(function(t,e){e(n)})},s.resolve=function(n){return new s(function(t,e){t(n)})},s.all=function(i){return new s(function(n,t){var o=0,r=[];0===i.length&&n(r);for(var e=0;e<i.length;e+=1)s.resolve(i[e]).then(function(e){return function(t){r[e]=t,(o+=1)===i.length&&n(r)}}(e),t)})},s.race=function(o){return new s(function(t,e){for(var n=0;n<o.length;n+=1)s.resolve(o[n]).then(t,e)})};var t=s.prototype;function a(t,e){t instanceof Promise?this.promise=t:this.promise=new Promise(t.bind(e)),this.context=e}t.resolve=function(t){var e=this;if(2===e.state){if(t===e)throw new TypeError("Promise settled with itself.");var n=!1;try{var o=t&&t.then;if(null!==t&&"object"==typeof t&&"function"==typeof o)return void o.call(t,function(t){n||e.resolve(t),n=!0},function(t){n||e.reject(t),n=!0})}catch(t){return void(n||e.reject(t))}e.state=0,e.value=t,e.notify()}},t.reject=function(t){var e=this;if(2===e.state){if(t===e)throw new TypeError("Promise settled with itself.");e.state=1,e.value=t,e.notify()}},t.notify=function(){var i=this;o(function(){if(2!==i.state)for(;i.deferred.length;){var t=i.deferred.shift(),e=t[0],n=t[1],o=t[2],r=t[3];try{0===i.state?o("function"==typeof e?e.call(void 0,i.value):i.value):1===i.state&&("function"==typeof n?o(n.call(void 0,i.value)):r(i.value))}catch(t){r(t)}}},void 0)},t.then=function(n,o){var r=this;return new s(function(t,e){r.deferred.push([n,o,t,e]),r.notify()})},t.catch=function(t){return this.then(void 0,t)},"undefined"==typeof Promise&&(window.Promise=s),a.all=function(t,e){return new a(Promise.all(t),e)},a.resolve=function(t,e){return new a(Promise.resolve(t),e)},a.reject=function(t,e){return new a(Promise.reject(t),e)},a.race=function(t,e){return new a(Promise.race(t),e)};t=a.prototype;t.bind=function(t){return this.context=t,this},t.then=function(t,e){return t&&t.bind&&this.context&&(t=t.bind(this.context)),e&&e.bind&&this.context&&(e=e.bind(this.context)),new a(this.promise.then(t,e),this.context)},t.catch=function(t){return t&&t.bind&&this.context&&(t=t.bind(this.context)),new a(this.promise.catch(t),this.context)},t.finally=function(e){return this.then(function(t){return e.call(this),t},function(t){return e.call(this),Promise.reject(t)})};var o,r={}.hasOwnProperty,i=[].slice,u=!1,c="undefined"!=typeof window;function f(t){return t?t.replace(/^\s*|\s*$/g,""):""}function h(t){return t?t.toLowerCase():""}var p=Array.isArray;function d(t){return"string"==typeof t}function l(t){return"function"==typeof t}function m(t){return null!==t&&"object"==typeof t}function v(t){return m(t)&&Object.getPrototypeOf(t)==Object.prototype}function y(t,e,n){t=a.resolve(t);return arguments.length<2?t:t.then(e,n)}function b(t,e,n){return l(n=n||{})&&(n=n.call(e)),T(t.bind({$vm:e,$options:n}),t,{$options:n})}function g(t,e){var n,o;if(p(t))for(n=0;n<t.length;n++)e.call(t[n],t[n],n);else if(m(t))for(o in t)r.call(t,o)&&e.call(t[o],t[o],o);return t}var w=Object.assign||function(e){return i.call(arguments,1).forEach(function(t){j(e,t)}),e};function T(e){return i.call(arguments,1).forEach(function(t){j(e,t,!0)}),e}function j(t,e,n){for(var o in e)n&&(v(e[o])||p(e[o]))?(v(e[o])&&!v(t[o])&&(t[o]={}),p(e[o])&&!p(t[o])&&(t[o]=[]),j(t[o],e[o],n)):void 0!==e[o]&&(t[o]=e[o])}function x(t,e,n){var o,s,u,t=(o=t,s=["+","#",".","/",";","?","&"],{vars:u=[],expand:function(i){return o.replace(/\{([^{}]+)\}|([^{}]+)/g,function(t,e,n){if(e){var o=null,r=[];if(-1!==s.indexOf(e.charAt(0))&&(o=e.charAt(0),e=e.substr(1)),e.split(/,/g).forEach(function(t){t=/([^:*]*)(?::(\d+)|(\*))?/.exec(t);r.push.apply(r,function(t,e,n,o){var r=t[n],i=[];{var s;E(r)&&""!==r?"string"==typeof r||"number"==typeof r||"boolean"==typeof r?(r=r.toString(),o&&"*"!==o&&(r=r.substring(0,parseInt(o,10))),i.push(O(e,r,P(e)?n:null))):"*"===o?Array.isArray(r)?r.filter(E).forEach(function(t){i.push(O(e,t,P(e)?n:null))}):Object.keys(r).forEach(function(t){E(r[t])&&i.push(O(e,r[t],t))}):(s=[],Array.isArray(r)?r.filter(E).forEach(function(t){s.push(O(e,t))}):Object.keys(r).forEach(function(t){E(r[t])&&(s.push(encodeURIComponent(t)),s.push(O(e,r[t].toString())))}),P(e)?i.push(encodeURIComponent(n)+"="+s.join(",")):0!==s.length&&i.push(s.join(","))):";"===e?i.push(encodeURIComponent(n)):""!==r||"&"!==e&&"?"!==e?""===r&&i.push(""):i.push(encodeURIComponent(n)+"=")}return i}(i,o,t[1],t[2]||t[3])),u.push(t[1])}),o&&"+"!==o){e=",";return"?"===o?e="&":"#"!==o&&(e=o),(0!==r.length?o:"")+r.join(e)}return r.join(",")}return C(n)})}}),e=t.expand(e);return n&&n.push.apply(n,t.vars),e}function E(t){return null!=t}function P(t){return";"===t||"&"===t||"?"===t}function O(t,e,n){return e=("+"===t||"#"===t?C:encodeURIComponent)(e),n?encodeURIComponent(n)+"="+e:e}function C(t){return t.split(/(%[0-9A-Fa-f]{2})/g).map(function(t){return t=!/%[0-9A-Fa-f]/.test(t)?encodeURI(t):t}).join("")}function $(t,e){var r,i=this||{},n=t;return d(t)&&(n={url:t,params:e}),n=T({},$.options,i.$options,n),$.transforms.forEach(function(t){var e,n,o;l(t=d(t)?$.transform[t]:t)&&(e=t,n=r,o=i.$vm,r=function(t){return e.call(o,t,n)})}),r(n)}function U(r){return new a(function(n){function t(t){var e=t.type,t=0;"load"===e?t=200:"error"===e&&(t=500),n(r.respondWith(o.responseText,{status:t}))}var o=new XDomainRequest;r.abort=function(){return o.abort()},o.open(r.method,r.getUrl()),r.timeout&&(o.timeout=r.timeout),o.onload=t,o.onabort=t,o.onerror=t,o.ontimeout=t,o.onprogress=function(){},o.send(r.getBody())})}$.options={url:"",root:null,params:{}},$.transform={template:function(e){var t=[],n=x(e.url,e.params,t);return t.forEach(function(t){delete e.params[t]}),n},query:function(t,e){var n=Object.keys($.options.params),o={},e=e(t);return g(t.params,function(t,e){-1===n.indexOf(e)&&(o[e]=t)}),(o=$.params(o))&&(e+=(-1==e.indexOf("?")?"?":"&")+o),e},root:function(t,e){var n=e(t);return d(t.root)&&!/^(https?:)?\//.test(n)&&(e=t.root,t="/",n=(e&&void 0===t?e.replace(/\s+$/,""):e&&t?e.replace(new RegExp("["+t+"]+$"),""):e)+"/"+n),n}},$.transforms=["template","query","root"],$.params=function(t){var e=[],n=encodeURIComponent;return e.add=function(t,e){null===(e=l(e)?e():e)&&(e=""),this.push(n(t)+"="+n(e))},function n(o,t,r){var i,s=p(t),u=v(t);g(t,function(t,e){i=m(t)||p(t),r&&(e=r+"["+(u||i?e:"")+"]"),!r&&s?o.add(t.name,t.value):i?n(o,t,e):o.add(e,t)})}(e,t),e.join("&").replace(/%20/g,"+")},$.parse=function(t){var e=document.createElement("a");return document.documentMode&&(e.href=t,t=e.href),e.href=t,{href:e.href,protocol:e.protocol?e.protocol.replace(/:$/,""):"",port:e.port,host:e.host,hostname:e.hostname,pathname:"/"===e.pathname.charAt(0)?e.pathname:"/"+e.pathname,search:e.search?e.search.replace(/^\?/,""):"",hash:e.hash?e.hash.replace(/^#/,""):""}};var R=c&&"withCredentials"in new XMLHttpRequest;function e(s){return new a(function(n){var o,t=s.jsonp||"callback",r=s.jsonpCallback||"_jsonp"+Math.random().toString(36).substr(2),i=null,e=function(t){var e=t.type,t=0;"load"===e&&null!==i?t=200:"error"===e&&(t=500),t&&window[r]&&(delete window[r],document.body.removeChild(o)),n(s.respondWith(i,{status:t}))};window[r]=function(t){i=JSON.stringify(t)},s.abort=function(){e({type:"abort"})},s.params[t]=r,s.timeout&&setTimeout(s.abort,s.timeout),(o=document.createElement("script")).src=s.getUrl(),o.type="text/javascript",o.async=!0,o.onload=e,o.onerror=e,document.body.appendChild(o)})}function n(r){return new a(function(n){function t(t){var e=r.respondWith("response"in o?o.response:o.responseText,{status:1223===o.status?204:o.status,statusText:1223===o.status?"No Content":f(o.statusText)});g(f(o.getAllResponseHeaders()).split("\n"),function(t){e.headers.append(t.slice(0,t.indexOf(":")),t.slice(t.indexOf(":")+1))}),n(e)}var o=new XMLHttpRequest;r.abort=function(){return o.abort()},o.open(r.method,r.getUrl(),!0),r.timeout&&(o.timeout=r.timeout),r.responseType&&"responseType"in o&&(o.responseType=r.responseType),(r.withCredentials||r.credentials)&&(o.withCredentials=!0),r.crossOrigin||r.headers.set("X-Requested-With","XMLHttpRequest"),l(r.progress)&&"GET"===r.method&&o.addEventListener("progress",r.progress),l(r.downloadProgress)&&o.addEventListener("progress",r.downloadProgress),l(r.progress)&&/^(POST|PUT)$/i.test(r.method)&&o.upload.addEventListener("progress",r.progress),l(r.uploadProgress)&&o.upload&&o.upload.addEventListener("progress",r.uploadProgress),r.headers.forEach(function(t,e){o.setRequestHeader(e,t)}),o.onload=t,o.onabort=t,o.onerror=t,o.ontimeout=t,o.send(r.getBody())})}function A(s){var u=require("got");return new a(function(e){var n,t=s.getUrl(),o=s.getBody(),r=s.method,i={};s.headers.forEach(function(t,e){i[e]=t}),u(t,{body:o,method:r,headers:i}).then(n=function(t){var n=s.respondWith(t.body,{status:t.statusCode,statusText:f(t.statusMessage)});g(t.headers,function(t,e){n.headers.set(e,t)}),e(n)},function(t){return n(t.response)})})}function S(r){var o=[k],i=[];function t(t){for(;o.length;){var n=o.pop();if(l(n)){var e=function(){var o=void 0,e=void 0;if(m(o=n.call(r,t,function(t){return e=t})||e))return{v:new a(function(t,n){i.forEach(function(e){o=y(o,function(t){return e.call(r,t)||t},n)}),y(o,t,n)},r)};l(o)&&i.unshift(o)}();if("object"==typeof e)return e.v}else e="Invalid interceptor of type "+typeof n+", must be a function","undefined"!=typeof console&&u&&console.warn("[VueResource warn]: "+e)}}return m(r)||(r=null),t.use=function(t){o.push(t)},t}function k(t){return(t.client||(c?n:A))(t)}var I=function(){function t(t){var n=this;this.map={},g(t,function(t,e){return n.append(e,t)})}var e=t.prototype;return e.has=function(t){return null!==L(this.map,t)},e.get=function(t){t=this.map[L(this.map,t)];return t?t.join():null},e.getAll=function(t){return this.map[L(this.map,t)]||[]},e.set=function(t,e){this.map[function(t){if(/[^a-z0-9\-#$%&'*+.^_`|~]/i.test(t))throw new TypeError("Invalid character in header field name");return f(t)}(L(this.map,t)||t)]=[f(e)]},e.append=function(t,e){var n=this.map[L(this.map,t)];n?n.push(f(e)):this.set(t,e)},e.delete=function(t){delete this.map[L(this.map,t)]},e.deleteAll=function(){this.map={}},e.forEach=function(n,o){var r=this;g(this.map,function(t,e){g(t,function(t){return n.call(o,t,e,r)})})},t}();function L(t,n){return Object.keys(t).reduce(function(t,e){return h(n)===h(e)?e:t},null)}var q=function(){function t(t,e){var n,o=e.url,r=e.headers,i=e.status,e=e.statusText;this.url=o,this.ok=200<=i&&i<300,this.status=i||0,this.statusText=e||"",this.headers=new I(r),d(this.body=t)?this.bodyText=t:(r=t,"undefined"!=typeof Blob&&r instanceof Blob&&(this.bodyBlob=t,0!==(r=t).type.indexOf("text")&&-1===r.type.indexOf("json")||(this.bodyText=(n=t,new a(function(t){var e=new FileReader;e.readAsText(n),e.onload=function(){t(e.result)}})))))}var e=t.prototype;return e.blob=function(){return y(this.bodyBlob)},e.text=function(){return y(this.bodyText)},e.json=function(){return y(this.text(),function(t){return JSON.parse(t)})},t}();Object.defineProperty(q.prototype,"data",{get:function(){return this.body},set:function(t){this.body=t}});var H=function(){function t(t){this.body=null,this.params={},w(this,t,{method:(t=t.method||"GET")?t.toUpperCase():""}),this.headers instanceof I||(this.headers=new I(this.headers))}var e=t.prototype;return e.getUrl=function(){return $(this)},e.getBody=function(){return this.body},e.respondWith=function(t,e){return new q(t,w(e||{},{url:this.getUrl()}))},t}(),t={"Content-Type":"application/json;charset=utf-8"};function B(t){var e=this||{},n=S(e.$vm);return function(n){i.call(arguments,1).forEach(function(t){for(var e in t)void 0===n[e]&&(n[e]=t[e])})}(t||{},e.$options,B.options),B.interceptors.forEach(function(t){l(t=d(t)?B.interceptor[t]:t)&&n.use(t)}),n(new H(t)).then(function(t){return t.ok?t:a.reject(t)},function(t){var e;return t instanceof Error&&(e=t,"undefined"!=typeof console&&console.error(e)),a.reject(t)})}function M(n,o,t,r){var i=this||{},s={};return g(t=w({},M.actions,t),function(t,e){t=T({url:n,params:w({},o)},r,t),s[e]=function(){return(i.$http||B)(function(t,e){var n,o=w({},t),r={};switch(e.length){case 2:r=e[0],n=e[1];break;case 1:/^(POST|PUT|PATCH)$/i.test(o.method)?n=e[0]:r=e[0];break;case 0:break;default:throw"Expected up to 2 arguments [params, body], got "+e.length+" arguments"}return o.body=n,o.params=w({},o.params,r),o}(t,arguments))}}),s}function N(n){var t,e;N.installed||(e=(t=n).config,t=t.nextTick,o=t,u=e.debug||!e.silent,n.url=$,n.http=B,n.resource=M,n.Promise=a,Object.defineProperties(n.prototype,{$url:{get:function(){return b(n.url,this,this.$options.url)}},$http:{get:function(){return b(n.http,this,this.$options.http)}},$resource:{get:function(){return n.resource.bind(this)}},$promise:{get:function(){var e=this;return function(t){return new n.Promise(t,e)}}}}))}return B.options={},B.headers={put:t,post:t,patch:t,delete:t,common:{Accept:"application/json, text/plain, */*"},custom:{}},B.interceptor={before:function(t){l(t.before)&&t.before.call(this,t)},method:function(t){t.emulateHTTP&&/^(PUT|PATCH|DELETE)$/i.test(t.method)&&(t.headers.set("X-HTTP-Method-Override",t.method),t.method="POST")},jsonp:function(t){"JSONP"==t.method&&(t.client=e)},json:function(t){var e=t.headers.get("Content-Type")||"";return m(t.body)&&0===e.indexOf("application/json")&&(t.body=JSON.stringify(t.body)),function(o){return o.bodyText?y(o.text(),function(t){var e,n;if(0===(o.headers.get("Content-Type")||"").indexOf("application/json")||(n=(e=t).match(/^\s*(\[|\{)/))&&{"[":/]\s*$/,"{":/}\s*$/}[n[1]].test(e))try{o.body=JSON.parse(t)}catch(t){o.body=null}else o.body=t;return o}):o}},form:function(t){var e;e=t.body,"undefined"!=typeof FormData&&e instanceof FormData?t.headers.delete("Content-Type"):m(t.body)&&t.emulateJSON&&(t.body=$.params(t.body),t.headers.set("Content-Type","application/x-www-form-urlencoded"))},header:function(n){g(w({},B.headers.common,n.crossOrigin?{}:B.headers.custom,B.headers[h(n.method)]),function(t,e){n.headers.has(e)||n.headers.set(e,t)})},cors:function(t){var e,n;c&&(e=$.parse(location.href),(n=$.parse(t.getUrl())).protocol===e.protocol&&n.host===e.host||(t.crossOrigin=!0,t.emulateHTTP=!1,R||(t.client=U)))}},B.interceptors=["before","method","jsonp","json","form","header","cors"],["get","delete","head","jsonp"].forEach(function(n){B[n]=function(t,e){return this(w(e||{},{url:t,method:n}))}}),["post","put","patch"].forEach(function(o){B[o]=function(t,e,n){return this(w(n||{},{url:t,method:o,body:e}))}}),M.actions={get:{method:"GET"},save:{method:"POST"},query:{method:"GET"},update:{method:"PUT"},remove:{method:"DELETE"},delete:{method:"DELETE"}},"undefined"!=typeof window&&window.Vue&&!window.Vue.resource&&window.Vue.use(N),N});