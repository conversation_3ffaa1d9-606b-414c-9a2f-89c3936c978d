Vue.directive('draggable', {
	bind : function(el, binding, vnode) {

		var dlg = el.getElementsByClassName("el-dialog")[0];
		var title = el.getElementsByClassName("el-dialog__title")[0];
		title.style.userSelect="none";
		title.style["-ms-user-select"] = "none";
		title.style["-moz-user-select"] = "none";
		title.style.cursor="default";

		dlg.offsetX = 0;
		dlg.offsetY = 0;

		var move = function(e){
			dlg.style.marginLeft = '0px';
			dlg.style.marginTop = '0px';
			dlg.style.left = (e.pageX - dlg.offsetX) + 'px';
			dlg.style.top = (e.pageY - dlg.offsetY) + 'px';
		}

		var up = function() {
			removeEventListener('mousemove', move);
			removeEventListener('mouseup', up);
		}

		var down = function(e){
			dlg.offsetX = (e.pageX - dlg.offsetLeft);
			dlg.offsetY = (e.pageY - dlg.offsetTop );

			addEventListener('mousemove', move);
			addEventListener('mouseup', up);
		}

		var header = el.getElementsByClassName("el-dialog__header")[0];
		header.addEventListener('mousedown', down);
    }
})

var Utils = (function(){
	function copy(obj) {
        return JSON.parse(JSON.stringify(obj));
	}
	
	function uuid() {
	    function s4() {
		    return Math.floor((1 + Math.random()) * 0x10000).toString(16).substring(1);
		}
		return s4() + s4() + s4() + s4() + s4() + s4() + s4() + s4();
	}
	
	function clear() {
	    for(var i=0; i<arguments.length; i++)
	        for(var key in arguments[i])
	            if(arguments[i].hasOwnProperty(key))
	                arguments[i][key] = null
	}
	
	/**请不要extend vue封装的复杂对象*/
	function extend(){
	    for(var i=1; i<arguments.length; i++)
	        for(var key in arguments[i])
	            if(arguments[i].hasOwnProperty(key)) { 
	                if (typeof arguments[0][key] === 'object'
	                    && typeof arguments[i][key] === 'object')
	             				extend(arguments[0][key], arguments[i][key]);
	                else
	                   arguments[0][key] = arguments[i][key]
	             }
	    return arguments[0];
	}
	
	return {
		copy: copy,
		uuid: uuid,
		clear: clear,
		extend: extend
	}
})();

var Message = (function(){
	var vm = new Vue();
	
	function success(msg) {
		if(!msg) msg = "操作成功";
		vm.$message({
	        showClose: true,
	        message: msg,
	        type: 'success'
	    });
	}
	
	function info(msg) {
		vm.$message({
	        showClose: true,
	        message: msg,
	        type: 'info'
	    });
	}
	
	function warning(msg) {
		vm.$message({
	        showClose: true,
	        message: msg,
	        type: 'warning'
	    });
	}
	
	function error(msg) {
		if(!msg) msg = "出错啦!";
		vm.$message({
	        showClose: true,
	        message: msg,
	        type: 'error',
	        duration: 0 // pin message
	    });
	}
	
	function confirm(msg, success, cancel) {
		if(!msg) msg = "注意!";
		
		vm.$confirm(msg, '提示', {
	        confirmButtonText: '确定',
	        cancelButtonText: '取消',
	        type: 'warning'
	    }).then(function() {
	    	if(success) success();
	    }).catch(function() {
	        if(cancel) cancel();
	    });
	}

	function _alert(msg, callback) {
		if(!msg) msg="注意!";
		vm.$alert(msg, '提示', {
			confirmButtonText: '确定',
			callback: callback
		});
	}
	
	return {
		success: success,
		warning: warning,
		info: info,
		error: error,
		confirm: confirm,
		"alert": _alert
	}
})();

var Form = (function(){

	/*这里之所以这么写，是因为不想出现必填的红色星号*/
    function notBlankValidator(msg) {
        return {
            validator: function(rule, value, callback) {
                if (!(value && (value + '').trim())) {
                    return callback(new Error(msg));
                }
                callback();
            },
            trigger: 'blur',
        }
    }
    
    function regexValidator(regex, msg, required) {
    	return {
            validator: function(rule, value, callback) {
            	if(!required && !value) {
            		callback()
            		return
            	}
                if ((required && !value) || !(new RegExp(regex).test(value))) {
                    return callback(new Error(msg));
                }
                callback();
            },
            trigger: 'blur',
    	}
    }
    
    /**
     * 清楚表单错误提示
     */
    function clearError(vue, formName) {
        if(vue.$refs[formName] && vue.$refs[formName].resetFields)
        	vue.$refs[formName].resetFields()
    }
    
    /**
	 * 验证表单，如果验证成功，调用success回调，否则调用error回调
	 */
    function validate(vue, refFormName, success, error) {
        vue.$refs[refFormName].validate(function(valid) {
            if (!valid) {
            	if(error) error();
                return false;
            }
            if(success) success();
        });
	}

    function phoneValidator(required) {
    	return regexValidator('^1\\d{10}$', '请输入正确的手机号', required)
    }
    function telValidator(required) {
    	return regexValidator('^\\d{3,4}-\\d{7,8}(-\\d+)?$', '请输入正确的固话号码(区号-固话号[可选-分号])', required)
    }
    function emailValidator(required) {
      return Form.regexValidator('^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,6}$',
        "请输入正确的E-mail", required)
    }
    
	return {
        validate: validate,
        clearError: clearError,
        notBlankValidator: notBlankValidator,
        regexValidator: regexValidator,
        phoneValidator: phoneValidator,
        telValidator: telValidator,
        emailValidator: emailValidator
	}
})();

// ==========  vue resource : http client  ==========
var Resource = (function() {

	// 预处理参数，vue resource对数组的处理不正常，导致dateRange传参有问题
	function preHandle(param) {
		var p = {}
		for(var key in param) {
			var val = param[key]
			if(val && (typeof val === 'object')) {
				var tmp = JSON.stringify(val)
				if(tmp.length > 0) {
				  if(tmp[0]=='"'&&tmp[tmp.length-1]=='"' || tmp[0]=="'"&&tmp[tmp.length-1]=="'")
				    tmp = tmp.substring(1, tmp.length-1)
				}
				p[key] = tmp
			} else {
				p[key] = val
			}
		}
		return p
	}

	function jsonToQueryString(json) {
		if(!json) return "";
	    return '?' + 
	        Object.keys(json).map(function(key) {
	            return encodeURIComponent(key) + '=' +
	                encodeURIComponent(json[key]);
	        }).join('&');
	}
	
	function download(url, param) {
		param = param || {};
		param = preHandle(param)
		window.location = url + jsonToQueryString(param)
	}

	function getTimeout1() {
		return setTimeout(function(){Message.info('已发出请求，接口有点慢，请稍等')}, 3000)
	}
	function getTimeout2(url) {
		return setTimeout(function(){Message.warning('接口'+ url +'实在太慢了，请联系开发人员优化')}, 12000)
	}

	function handleSuccess(resp, success, error, s1, s2) {
		if (s1) clearTimeout(s1)
		if (s2) clearTimeout(s2)
		resp = resp.body;
		if(resp.code == 0) {
			if(success) {success(resp)} else {Message.success(resp.msg || resp.message)}
		} else if (resp.code == 1) { // not login
			window.location.reload(true)
		} else {
			error ? error(resp) : Message.error(resp.msg || resp.message);
		}
	}

	function handleNetworkError(s1, s2, networkErrorCallback) {
		if (s1) clearTimeout(s1)
		if (s2) clearTimeout(s2)
		if (networkErrorCallback) {
			networkErrorCallback()
		} else {
			Message.error("网络错误，请稍后再试");
		}
	}

	function post(url, param, success, error, finallyCallback, otherErrorCallback, disableTimeoutTips) {
		param = param || {};
		param = preHandle(param)
		var s1, s2
		if (!disableTimeoutTips) {
			s1 = getTimeout1()
			s2 = getTimeout2(url)
		}

		Vue.http.post(url, param, {emulateJSON:true}).then(function(resp){
			try {
				handleSuccess(resp, success, error, s1, s2)
			} finally {
				if (finallyCallback) finallyCallback()
			}
  		}, function() {
			try {
				handleNetworkError(s1, s2, otherErrorCallback)
			} finally {
				if (finallyCallback) finallyCallback()
			}
  		})
	}

	function postJson(url, body, success, error, finallyCallback, otherErrorCallback, disableTimeoutTips) {
		var s1, s2
		if (!disableTimeoutTips) {
			s1 = getTimeout1()
			s2 = getTimeout2(url)
		}
		Vue.http.post(url, body, {headers:{'Content-Type':'application/json;'}}).then(function(resp){
			try {
				handleSuccess(resp, success, error, s1, s2)
			} finally {
				if (finallyCallback) finallyCallback()
			}
		}, function(error) {
			try {
				handleNetworkError(s1, s2, otherErrorCallback)
			} finally {
				if (finallyCallback) finallyCallback()
			}
		})
	}
	
	function get(url, param, success, error, finallyCallback, otherErrorCallback, disableTimeoutTips) {
		param = param || {};
		param = preHandle(param)
		var s1, s2
		if (!disableTimeoutTips) {
			s1 = getTimeout1()
			s2 = getTimeout2(url)
		}
		Vue.http.get(url, {params: param}).then(function(resp){
			try {
				if (s1) clearTimeout(s1)
				if (s2) clearTimeout(s2)
				resp = resp.body;
				if(resp.code === 0) {
					if(success) success(resp); // 这里不需要弹框提示成功
				} else if (resp.code === 1) {
					window.location.reload(true)
				} else {
					error ? error(resp) : Message.error(resp.msg || resp.message);
				}
			} finally {
				if (finallyCallback) finallyCallback()
			}
		}, function(error){
			try {
				handleNetworkError(s1, s2, otherErrorCallback)
			} finally {
				if (finallyCallback) finallyCallback()
			}
		})
	}

	function getUrlParam(name) {
        return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.search) || [null, ''])[1].replace(/\+/g, '%20')) || null;
	}
	
	return {
		download: download,
		post: post, // 使用queryString
		postJson: postJson,
		get: get,
        getUrlParam: getUrlParam
	}
})();
