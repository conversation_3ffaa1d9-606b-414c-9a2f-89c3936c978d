#set($layout="no_layout.vm")
<html lang="en">
<head>
    <title>管理后台登录</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="renderer" content="webkit">
    <style>
        body { background-color: #e9edf3; }
        .login-box {width:80%;max-width:800px;min-width:300px;height:550px; margin:auto;position:fixed;top:0;bottom:0;left:0;right:0;}
    </style>

  <link rel="stylesheet" href="${_resourceContextPath_}/admin_static/css/elementui/index.css?v=2.15.14">
  <link rel="stylesheet" href="${_resourceContextPath_}/admin_static/css/pw-common.css?v=2.0.13">
  
</head>
<body>
    <div id="app" v-cloak>
      <el-card class="login-box">
        <el-row type="flex" justify="center" style="margin: 30px">
		    <img src="${_resourceContextPath_}/admin_static/img/login-logo.png" style="width: 80%;"/>
        </el-row>
		<el-form label-position="right" label-width="80px" :model="form" :rules="rules" ref="form_vld" style="margin: auto; margin-top:35px;">
		  <el-form-item label="用户名" prop="username" style="margin-left:20%">
		      <el-input v-model="form.username" placeholder="请输入用户名" style="width: 60%"></el-input>
		  </el-form-item>
		  <el-form-item label="密码" prop="password" style="margin-left:20%">
		      <el-input v-model="form.password" placeholder="请输入密码" type="password" style="width: 60%"></el-input>
		  </el-form-item>
		  <el-form-item label="验证码" prop="captcha" style="margin-left:20%" v-if="isShowCaptcha">
              <el-row>
                 <el-col :span="6">
                   <el-input v-model="form.captcha" placeholder="请输入验证码" ></el-input>
                 </el-col>
                 <el-col :span="6">
                   <img height="40px" :src="captcha" @click="changeImg" style="cursor:hand;" v-if="delayShow"/>
                 </el-col>
              </el-row>
		  </el-form-item>
		  <el-row type="flex" justify="center">
		      <el-button type="success" size="large" @click.native="submit('form_vld')" style="width:20%;margin-top:30px">登 录</el-button>
          </el-row>
		</el-form>
	  </el-card>
    </div>
</body>

<script src="${_resourceContextPath_}/admin_static/js/vue.min.js"></script>
<script src="${_resourceContextPath_}/admin_static/js/elementui/index.js?v=2.15.14"></script>
<script src="${_resourceContextPath_}/admin_static/js/vue-resource.min.js"></script>

  <script src="${_resourceContextPath_}/admin_static/js/common-utils.min.js?v=2.0.13"></script>
  
<script>
var vm = new Vue({
    el: '#app',
    data: {
    	form: {
    		r: Math.random()
    	},
    	rules: {
    		username: Form.notBlankValidator('用户名不能为空'),
    		password: Form.notBlankValidator('密码不能为空'),
    		captcha: Form.notBlankValidator('验证码不能为空')
    	},
        isShowCaptcha: false,
        delayShow: false // fix captcha load twice
    },
    watch: {
        "form.username": function(val) {
            this.needCaptcha()
        }
    },
    computed: {
    	captcha: function() {
    		return "${_contextPath_}/admin_login/captcha?r=" + this.form.r
    	}
    },
    mounted() {
        var that = this
        setTimeout(function(){that.delayShow = true}, 100)
    },
    methods: {
        needCaptcha: function() {
            var that = this
            if (this.form.username) {
                Resource.get("${_contextPath_}/admin_login/is_need_captcha",
                        {username:this.form.username}, function(resp) {
                    that.isShowCaptcha = resp.data
                })
            }
        },
    	changeImg: function() {
    		this.form.r = Math.random()
    	},
    	submit: function(formName) {
    		var that = this
    		Form.validate(this, formName, function(){
                Resource.post("${_contextPath_}/admin_login/login", that.form, function(resp) {
                    var redirect = Resource.getUrlParam('redirect')
                    if(redirect) redirect = decodeURIComponent(redirect)
                    redirect ? location.href = redirect : location.reload()
                }, function(resp) {
                    that.needCaptcha()
                    if(resp.msg) Message.error(resp.msg)
                    if(resp.code == 4 || resp.code == 5) { // 4 == 密码错误，5 ==验证码错误
                        that.form.captcha = ''
                        that.changeImg()
                    }
                })
            })
    	}
    }
})
</script>
</html>