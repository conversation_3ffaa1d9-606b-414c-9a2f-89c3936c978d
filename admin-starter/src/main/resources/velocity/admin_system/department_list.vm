#set($page_title='部门列表')

<style>
  .custom-tree-node {flex: 1;display: flex;align-items: center;justify-content: space-between;font-size: 16px;padding-right: 8px;height: 100%;  }
  .card-title {text-align: center;margin-bottom: 6px;font-size: 16px;font-weight: bold}
</style>

<div id="app" v-cloak>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-row><el-input v-model="filterText" placeholder="输入关键字进行过滤，拖拽节点可排序"></el-input></el-row>
      <el-tree :data="treeData" node-key="id" ref="tree" :props="treeProps"
        default-expand-all :expand-on-click-node="false" :filter-node-method="filterNode"
        draggable @node-drop="saveTreeBtnType='danger'" highlight-current>
		    <span class="custom-tree-node" slot-scope="scope" @click="edit(scope.data, scope.node)">
		      <span>{{scope.node.label}} ({{scope.data.userCount}}人)</span>
          <span>
		        <i class="el-icon-circle-plus-outline" @click="append(scope.data)"></i>
            <i class="el-icon-delete" @click="remove(scope.node, scope.data)"></i>
          </span>
		    </span>
      </el-tree>
    </el-col>
    <el-col :span="12">
      <el-row>
        <el-button :type="saveTreeBtnType" :disabled="saveTreeBtnType=='info'" @click="saveTree">保存树形结构</el-button>
        <el-button type="success" @click="append(null)">新建根节点</el-button>
      </el-row>
      <el-card>
        <el-row class="card-title"><span>编辑部门信息</span></el-row>
        <el-form :model="editForm" label-position="right" label-width="80px" ref="editForm" :rules="rules">
          <el-form-item label="* 部门名称" prop="name">
            <el-input v-model="editForm.name" placeholder="部门名称" :disabled="!editData"></el-input>
          </el-form-item>
          <el-form-item label="部门描述" prop="description">
            <el-input v-model="editForm.description" placeholder="部门描述" :disabled="!editData"></el-input>
          </el-form-item>
        </el-form>
        <el-row style="text-align: center; margin-top: 20px">
          <el-tooltip class="item" effeck="dark" content="修改将直接保存到数据库" placement="bottom" v-if="editForm.id">
            <el-button type="primary" @click="doEdit" :disabled="!editData">修改</el-button>
          </el-tooltip>
          <el-button type="primary" @click="doEdit" :disabled="!editData" v-else>修改</el-button>
        </el-row>
      </el-card>
      <el-card v-show="editForm.id!='' && editForm.id!=null">
        <el-row class="card-title"><span>部门成员</span></el-row>
        <div v-if="!departmentUserList">部门成员加载中...</div>
        <div v-else>
          <div v-show="departmentUserList.length==0">该部门没有成员</div>
          <div v-show="departmentUserList.length>0">
            <el-tag v-for="tag in departmentUserList" style="margin-right: 10px;font-size: 16px" size="medium">
              {{tag.userName}}<span v-show="tag.realName!=null&&tag.realName!=''">({{tag.realName}})</span>
            </el-tag>
          </div>
          <div v-show="departmentChildrenUserList.length>0">
            <div style="text-align: center; margin: 10px 0 10px 0">======子部门成员======</div>
            <div>
              <el-tag v-for="tag in departmentChildrenUserList" style="margin-right: 10px;font-size: 16px" size="medium">
                {{tag.userName}}<span v-show="tag.realName!=null&&tag.realName!=''">({{tag.realName}})</span>
              </el-tag>
            </div>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
  <el-dialog :title="dialogTitleAppend" :visible.sync="dialogVisibleAppend" top="0" :close-on-click-modal="false">
    <el-form :model="addForm" label-position="right" label-width="80px" :rules="rules" ref="addForm">
      <el-form-item label="* 部门名称" prop="name">
        <el-input v-model="addForm.name" placeholder="部门名称"></el-input>
      </el-form-item>
      <el-form-item label="部门描述" prop="description">
        <el-input v-model="addForm.description" placeholder="部门描述"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="dialogVisibleAppend=false">取 消</el-button>
      <el-button type="primary" @click="doAppend">保 存</el-button>
    </div>
  </el-dialog>
</div>

<script>
  var defaultEditForm = {id: '', name: '', description: '', webkey: ''};
  var vm = new Vue({
    el: '#app',
    data: {
      treeData: null,
      treeProps: {children: 'children', label: 'name'},
      editForm: Utils.copy(defaultEditForm),
      editData: null,
      rules: {name: Form.notBlankValidator('部门名称不能为空')},
      addForm: {name: '', description: ''},
      curAppend: {},
      dialogVisibleAppend: false,
      dialogTitleAppend: '',
      saveTreeBtnType: 'info',
      filterText: '',
      departmentUserList: [],
      departmentChildrenUserList:[],
    },
    created: function () {
      this.getData();
    },
    watch: {
      filterText: function (val) {
        this.$refs.tree.filter(val);
      }
    },
    methods: {
      getData: function () {
        var that = this;
        Resource.get('${_contextPath_}/admin_user/department/get_tree', {}, function (resp) {
          that.treeData = resp.data;
        })
      },
      resetEdit: function () {
        Form.clearError(this, 'editForm')
        this.editData = null;
      },
      edit: function (data, node) {
        var that = this;
        this.resetEdit();
        this.editForm = Utils.copy(data);
        this.editData = data;
        if (data.id != null) {
          this.departmentUserList = null;
          this.departmentChildrenUserList = [];
          Resource.get('${_contextPath_}/admin_user/department/get_users',{
              departmentId: that.editForm.id
            },function (resp) {
              if (resp.data.departmentId == that.editForm.id) {
                that.departmentUserList = resp.data.userList;
                that.departmentChildrenUserList = resp.data.childrenUserList;
              }
          })
        }
      },
      doEdit: function () {
        var that = this;
        Form.validate(this, 'editForm', function () {
          Utils.extend(that.editData, that.editForm);
          if (that.editForm.id) {
            Resource.post('${_contextPath_}/admin_user/department/save_node', that.editForm, function (resp) {
              Message.success("保存成功！");
            },function (resp) {
              Message.error(resp.msg);
            })
          } else {
            Message.success("临时保存成功，记得最后【保存树形结构】")
          }
        })
      },
      append: function (data) {
        Form.clearError(this, 'addForm');
        if (data) {
          this.curAppend = data;
          this.dialogTitleAppend = "新增子部门 - 上级部门为 " + data.name;
        }else {
          this.curAppend = this.treeData;
          this.dialogTitleAppend = "新增顶级部门";
        }
        this.dialogVisibleAppend = true;
      },
      doAppend: function () {
        var that = this;
        Form.validate(this, 'addForm', function () {
          var newChild = Utils.copy(that.addForm);
          newChild.id = null;
          newChild.children = [];
          newChild.webkey = Utils.uuid();
          if (that.curAppend == that.treeData) {
            that.treeData.push(newChild);
          } else {
            if (!that.curAppend.children) {
              that.$set(that.curAppend, 'children', []);
            }
            that.curAppend.children.push(newChild);
          }
          that.dialogVisibleAppend = false;
          that.saveTreeBtnType = 'danger';
        })
      },
      saveTree: function () {
        var that = this;
        var editWebkey = this.editForm.webkey;
        var editId = this.editForm.id;
        Resource.post('${_contextPath_}/admin_user/department/save_tree', {
          treeData: JSON.stringify(this.treeData)
        }, function () {
          Resource.get('${_contextPath_}/admin_user/department/get_tree', {}, function (resp) {
            that.treeData = resp.data;
            Message.success('保存成功');
            that.saveTreeBtnType = 'info';
            if(editWebkey || editId) {
              var data = that.findData(editId, editWebkey) // 保持编辑块有效
              data && data.id ? that.edit(data) : that.resetEdit()
            }
          })
        }, function (resp) {
          Message.error(resp.msg);
        })
      },
      findData: function(id, webkey) { // 找到任意一个id或webkey相同即可
        function _find(datas, id, webkey) {
          for(var i = 0; i < datas.length; i++) {
            if(datas[i].id == id || datas[i].webkey == webkey) return datas[i]
            if(datas[i].children) {
              var tmp = _find(datas[i].children, id, webkey)
              if(tmp) return tmp
            }
          }
          return null
        }
        return _find(this.treeData, id, webkey)
      },
      remove: function (node, data) {
        var that = this;
        var msg = '确定要删除部门【' + data.name + '】吗？';
        if (data.children && data.children.length > 0) {
          msg += '将删除其下级所有的部门！！';
        }
        msg += '删除会重置该部门下所有用户的部门信息';
        Message.confirm(msg, function () {
          var parent = node.parent;
          var children = parent.data.children || parent.data;
          var index;
          if (data.id) {
            index = children.findIndex(function(d) {return d.id === data.id});
          } else {
            index = children.findIndex(function(d) {return d.webkey === data.webkey});
          }
          children.splice(index, 1);
          that.resetEdit();
          that.saveTreeBtnType = 'danger';
        })
      },
      filterNode: function (value, data) {
        if (!value) {return true;}
        return data.name.indexOf(value) !== -1;
      }
    }
  })

</script>