#set($page_title='用户列表')

<style>
  .button-department {padding-left: 6px;padding-right: 6px;width: 100;}
  .custom-tree-node {flex: 1;display: flex;align-items: center;justify-content: space-between;font-size: 14px;padding-right: 8px;
    height: 100% /*修复官方slot不是100%高导致点击编辑偶发没反应的问题*/;
  }
  .demo-table-expand {font-size: 0;}
  .demo-table-expand label {width: 90px;color: #99a9bf;}
  .demo-table-expand .el-form-item {margin-right: 0;margin-bottom: 0; width: 50%;}
</style>

<div id="app" v-cloak>
  <el-form :inline="true" @keyup.native.enter="getData">
    <el-form-item><el-input v-model="queryForm.keyword" placeholder="用户名、真实姓名、手机号" style="width: 200px"></el-input>
    </el-form-item>
    <el-form-item>
      <el-cascader :options="editDept.allDepts" :props="editDept.cascaderProps" 
          v-model="queryForm.departmentIds" change-on-select placeholder="部门" style="width: 200px"></el-cascader>
    </el-form-item>
    <el-form-item>
      <el-date-picker v-model="queryForm.dateRange" type="datetimerange" :default-time="['00:00:00','23:59:59']"
           align="right" start-placeholder="注册开始日期" end-placeholder="注册结束日期" style="width: 300px">
      </el-date-picker>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
      <el-button @click="resetQuery">重置</el-button>
    </el-form-item>
    <el-form-item>
      <el-button type="success" @click="handleAddOrEdit(true)">新增用户</el-button>
    </el-form-item>
  </el-form>

  <el-table :data="tableData" border stripe v-loading.body="tableLoading" v-sticky-header>
    <el-table-column type="expand">
      <template slot-scope="props">
        <el-form label-position="left" inline class="demo-table-expand">
          <el-form-item label="注册时间"><span>{{ props.row.createTime }}</span></el-form-item>
          <el-form-item label="公司"><span>{{ props.row.company }}</span></el-form-item>
          <el-form-item label="备注"><span>{{ props.row.remark }}</span></el-form-item>
          <el-form-item label="个性签名"><span>{{ props.row.signature }}</span></el-form-item>
        </el-form>
      </template>
    </el-table-column>
    <el-table-column label="登录名" min-width="160">
      <template slot-scope="scope">
        <el-tag type="danger" v-if="scope.row.disabled">已禁用</el-tag>
        <el-tag v-if="scope.row.isAdmin">管理员</el-tag>
        <span>{{scope.row.userName}}</span>
      </template>
    </el-table-column>
    <el-table-column prop="realName" label="真实姓名" min-width="100"></el-table-column>
    <el-table-column prop="phone" label="手机号" min-width="130"></el-table-column>
    <el-table-column prop="email" label="Email" min-width="160"></el-table-column>
    <el-table-column label="部门" min-width="120">
      <template slot-scope="scope">
        <el-button size="small" class="button-department" type="primary" plain v-if="!scope.row.departmentName"
                   @click="handleEditDept(scope.row)">点击设置</el-button>
        <el-button size="small" class="button-department" plain v-else
                   @click="handleEditDept(scope.row)">{{scope.row.departmentName}}</el-button>
      </template>
    </el-table-column>
    <el-table-column prop="position" label="职位" min-width="100"></el-table-column>
    <el-table-column label="操作" min-width="160">
      <template slot-scope="scope">
        <el-button-group>
          <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
          <el-button type="primary" size="small" @click="handleEditRoles(scope.row)"><i class="el-icon-edit"></i>角色</el-button>
        </el-button-group>
      </template>
    </el-table-column>
  </el-table>

  <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
       :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
  </el-pagination>

  <el-dialog :title="dialogTitle" :visible.sync="showDialog" v-draggable top="10px" :close-on-click-modal="false">
    <el-form :model="addEditForm" label-position="right" label-width="80px" :rules="rules" ref="addEditForm">
      <el-form-item label="* 用户名" prop="userName">
        <el-input v-model="addEditForm.userName" placeholder="用户名"></el-input>
      </el-form-item>
      <el-form-item label="登录密码" prop="password">
        <el-input v-model="addEditForm.password" placeholder="登录密码"></el-input>
      </el-form-item>
      <el-row>
          <el-col :span="6" :xs="{span:12}">
            <el-form-item label="管理员" prop="isAdmin">
              <el-tooltip content="管理员可以操作admin自带页面(com.pugwoo.admin下页面接口)，但不包括业务页面接口权限">
               <el-switch v-model="addEditForm.isAdmin" active-color="#13ce66" inactive-color="#eee"></el-switch>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="6" :xs="{span:12}">
             <el-form-item label="禁用" prop="disabled">
                <el-switch v-model="addEditForm.disabled" active-color="#ff4949" 
                           inactive-color="#eee" @change="disable"></el-switch>
             </el-form-item>
          </el-col>
      </el-row>
      <el-form-item label="真实姓名" prop="realName">
        <el-input v-model="addEditForm.realName" placeholder="真实姓名"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="addEditForm.phone" placeholder="手机号"></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="addEditForm.email" placeholder="邮箱"></el-input>
      </el-form-item>
      <el-form-item label="公司" prop="company">
        <el-input v-model="addEditForm.company" placeholder="公司"></el-input>
      </el-form-item>
      <el-form-item label="职位" prop="position">
        <el-input v-model="addEditForm.position" placeholder="职位"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="addEditForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="danger" @click="handleDelete" v-if="addEditForm.id">删除该用户</el-button>
      <el-button @click="showDialog = false">取 消</el-button>
      <el-button type="primary" @click="doAddOrEdit">确 定</el-button>
    </div>
  </el-dialog>
  
  <el-dialog title="编辑用户角色" :visible.sync="editRoles.dialogVisible" top="10px" :close-on-click-modal="false">
    <div v-show="!editRoles.allRoles">加载中...</div>
    <div v-show="editRoles.allRoles">
      <el-checkbox-group v-model="editRoles.form.userRoles">
        <el-checkbox v-for="role in editRoles.allRoles" :label="role.roleId">
          {{role.roleName}}
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div slot="footer">
      <el-button @click="editRoles.dialogVisible=false">取 消</el-button>
      <el-button type="primary" @click="doUserRoleSave">保 存</el-button>
    </div>
  </el-dialog>

  <el-dialog title="编辑部门" :visible.sync="editDept.dialogVisible" top="10px" :close-on-click-modal="false">
      <el-tree :data="editDept.allDepts" :props="editDept.treeProps"
               node-key="id" default-expand-all :expand-on-click-node="false">
        <span class="custom-tree-node" slot-scope="scope">
          <el-radio v-model="editDept.form.departmentId" :label="scope.data.id">{{scope.node.label}}</el-radio>
        </span>
      </el-tree>
    <div slot="footer">
      <el-button @click="editDept.dialogVisible=false">取 消</el-button>
      <el-button type="primary" @click="doSaveUserDept">保 存
      </el-button>
    </div>
  </el-dialog>

</div>

<script>
var defaultQueryForm = {page: 1, pageSize: 10}
var defaultAddForm = {}
var vm = new Vue({
  el: '#app',
  data: {
    queryForm: Utils.copy(defaultQueryForm),
    addEditForm: Utils.copy(defaultAddForm),
    total: 0,
    tableData: [],
    tableLoading: false,
    showDialog: false,
    dialogTitle: '',
    rules: {
      userName: Form.notBlankValidator('用户名不能为空'),
      phone: Form.phoneValidator(false)
    },
    editRoles: {
      form: {
        userId: '',
        userRoles: []
      },
      allRoles: [],
      dialogVisible: false
    },
    editDept: {
      form: {
        userId: '',
        departmentId: '',
      },
      allDepts: [],
      treeProps: {children: 'children', label: 'name'},
      cascaderProps: {children: 'children', label: 'name', value: 'id'},
      dialogVisible: false
    }
  },
  created: function () {
    this.getData();
    this.getDeptTreeData();
  },
  methods: {
    getData: function () {
      var that = this;
      that.tableLoading = true;
      Resource.get("${_contextPath_}/admin_user/get_page", this.queryForm, function (resp) {
        that.tableData = resp.data.data
        that.total = resp.data.total
        that.tableLoading = false
      });
    },
    pageChange: function (page) {
      this.queryForm.page = page;
      this.getData();
    },
    resetQuery: function() {
    	this.queryForm = Utils.copy(defaultQueryForm)
    	this.getData()
    },
    handleDelete: function () {
      var that = this
      Message.confirm("确定要删除用户" + this.addEditForm.userName + "吗?", function () {
        Resource.post("${_contextPath_}/admin_user/delete", {userId: that.addEditForm.id}, function () {
        	that.showDialog = false
        	Message.success("删除成功，列表已刷新")
          that.getData()
        })
      })
    },
    handleAddOrEdit: function (isAdd, row) {
      this.showDialog = true
      this.dialogTitle = isAdd ? '新增用户' : ('编辑用户' + row.userName)
      Form.clearError(this, 'addEditForm')
      this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
    },
    doAddOrEdit: function () {
      var that = this
      var isEdit = this.addEditForm.id ? true : false
      Form.validate(this, 'addEditForm', function () {
        Resource.post("${_contextPath_}/admin_user/add_or_edit", that.addEditForm, function (resp) {
          Message.success(isEdit ? "修改成功" : "新增成功");
          isEdit ? (that.showDialog = false) : (that.addEditForm = Utils.copy(defaultAddForm))
          that.getData()
        });
      });
    },
    handleEditRoles: function (row) {
      var that = this;
      this.editRoles.dialogVisible = true;
      this.editRoles.allRoles = null;
      this.editRoles.form = {userId: row.id, userRoles:[]}
      Resource.get('$!{_contextPath_}/admin_user_role/roleListByUserId', {userId: row.id}, function (resp) {
          if (row.id == that.editRoles.form.userId) {
              that.editRoles.allRoles = resp.data.allRoleList;
              that.editRoles.form.userRoles = resp.data.userRoleIds;
          }
      })
    },
    doUserRoleSave: function () {
      var that = this;
      Resource.post('$!{_contextPath_}/admin_user_role/saveUserRoles', that.editRoles.form, function (resp) {
        Message.success('保存角色列表成功');
        that.editRoles.dialogVisible = false;
        that.getData();
      })
    },
    handleEditDept: function (row) {
      this.editDept.dialogVisible = true
      var departmentName = row.departmentName || '无'
      this.editDept.form = {userId: row.id, departmentId: row.departmentId}
    },
    doSaveUserDept: function () {
      var that = this
      Resource.post('${_contextPath_}/admin_user/department/save_user_department', this.editDept.form, function (resp) {
        that.editDept.dialogVisible = false
        Message.success("保存成功")
        that.getData()
      })
    },
    getDeptTreeData: function () {
      var that = this
      Resource.get('${_contextPath_}/admin_user/department/get_tree', {}, function (resp) {
        that.editDept.allDepts = resp.data
      })
    },
    disable: function(val) {
    	if(val) Message.warning('禁用用户将清除该用户的所有登录态')
    }
  }
})
</script>