#set($page_title='系统慢Web请求列表')

<style>
  .log-exception-table-expand label {width: 120px;color: #99a9bf;}
  .log-exception-table-expand .el-form-item {width: 100%;}
</style>

<div id="app" v-cloak>
  <el-form :inline="true" @keyup.native.enter="getData">
    <el-form-item>
      <el-date-picker v-model="queryForm.dateRange" type="datetimerange"
        start-placeholder="SlowWeb开始日期" end-placeholder="SlowWeb结束日期"
        :default-time="['00:00:00', '23:59:59']" align="right">
      </el-date-picker>
      <el-checkbox v-model="queryForm.onlyUnRead" border>只看未读</el-checkbox>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="getData">查询</el-button>
      <el-button @click="resetQuery">重置</el-button>
      <el-tooltip content="将对所有未读记录标记为已读" placement="bottom">
        <el-button type="success" @click="doReadAll">全部标为已读</el-button>
      </el-tooltip>
    </el-form-item>
  </el-form>
  <el-table :data="tableData" border stripe v-loading.body="tableLoading" @expand-change="expandChange">
    <el-table-column type="expand">
      <template slot-scope="props">
        <el-form :data="props" class="log-exception-table-expand">
          <el-row>
            <el-col :span="6"><el-form-item label="用户id">{{props.row.userId}}</el-form-item></el-col>
            <el-col :span="6"><el-form-item label="用户名">{{props.row.userName}}</el-form-item></el-col>
            <el-col :span="8"><el-form-item label="客户端IP">{{props.row.ip}}</el-form-item></el-col>
          </el-row>
          <el-form-item label="Slow Web Url">{{props.row.requestMethod}} : {{props.row.url}}</el-form-item>
          <el-form-item label="Referer">{{props.row.referer}}</el-form-item>
          <el-form-item label="请求响应耗时"><span style="font-size: 20px">{{props.row.requestTime}}</span></el-form-item>
        </el-form>
      </template>
    </el-table-column>
    <el-table-column prop="isRead" label="状态" width="80">
      <template slot-scope="scope">
        <el-button type="success" size="small" v-if="scope.row.isRead" disabled>已读</el-button>
        <el-tooltip content="标记为已读" placement="bottom" v-else>
          <el-button type="danger" size="small"  @click="doRead(scope.row)">未读</el-button>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column prop="createTime" label="Slow Web 时间" width="180"></el-table-column>
    <el-table-column prop="updateTime" label="查看时间" width="180"></el-table-column>
    <el-table-column prop="requestTime" label="请求耗时(ms)" width="120" ></el-table-column>
    <el-table-column prop="url" label="异常连接"></el-table-column>
  </el-table>
  <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
                 :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper"
                 background>
  </el-pagination>
</div>

<script>
  var defaultQueryForm = {page: 1, pageSize: 10, onlyUnRead: false};
  var vm = new Vue({
    el: '#app',
    data: {
      queryForm: Utils.copy(defaultQueryForm),
      total: 0,
      tableData: [],
      tableLoading: false
    },
    created: function () {
      this.getData()
    },
    watch: {
    	"queryForm.onlyUnRead": function(){
    	  this.queryForm.page = 1;
    	  this.getData()}
    },
    methods: {
      getData: function () {
        var that = this;
        that.tableLoading = true;
        Resource.get("${_contextPath_}/admin_log/slowweb/get_page", this.queryForm, function (resp) {
          that.tableData = resp.data.data
          that.total = resp.data.total
          that.tableLoading = false
        });
      },
      pageChange: function (page) {
        this.queryForm.page = page;
        this.getData();
      },
      resetQuery: function() {
        this.queryForm = Utils.copy(defaultQueryForm);
        this.getData();
      },
      doRead: function (row, msg) {
        var that = this;
        Resource.post("${_contextPath_}/admin_log/slowweb/read", {
          logSlowWebId: row.logSlowWebId
        }, function (resp) {
          Message.success(msg ? msg : "标记为已读");
          row.isRead = true;
        });
      },
      doReadAll: function () {
        var that = this;
        Resource.post("${_contextPath_}/admin_log/slowweb/readAll", {}, function (resp) {
          Message.success("已将所有的记录标记为已读");
          that.getData();
        });
      },
      expandChange: function (row, expandedRows) {
        if (row.isRead == false) {
          this.doRead(row, "已自动标记为已读")
        }
      }
    }
  })
</script>