#set($page_title='系统异常列表')

<style>
  .log-exception-table-expand label {width: 100px;color: #99a9bf;}
  .log-exception-table-expand .el-form-item {width: 100%;}
</style>

<div id="app" v-cloak>
  <el-form :inline="true" @keyup.native.enter="getData">
    <el-form-item>
      <el-date-picker v-model="queryForm.dateRange" type="datetimerange"
           align="right" start-placeholder="异常开始日期" end-placeholder="异常结束日期"
           :default-time="['00:00:00', '23:59:59']">
      </el-date-picker>
      <el-checkbox v-model="queryForm.onlyUnRead" border>只看未读</el-checkbox>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="getData">查询</el-button>
      <el-button @click="resetQuery">重置</el-button>
      <el-tooltip content="将对所有未读记录标记为已读" placement="bottom">
        <el-button type="success" @click="doReadAll">全部标为已读</el-button>
      </el-tooltip>
    </el-form-item>
    <el-form-item>
      <span v-show="notifyException">当发生异常时会通知管理员，请配置好管理员邮箱，支持企业微信机器人，每天最多发送{{maxExceptionNotifyPerDay}}条</span>
    </el-form-item>
  </el-form>
   
  <el-table :data="tableData" border stripe v-loading.body="tableLoading" @expand-change="expandChange">
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form :data="props" class="log-exception-table-expand">
            <el-row>
              <el-col :span="6"><el-form-item label="用户id">{{props.row.userId}}</el-form-item></el-col>
              <el-col :span="6"><el-form-item label="用户名">{{props.row.userName}}</el-form-item></el-col>
              <el-col :span="8"><el-form-item label="客户端IP">{{props.row.ip}}</el-form-item></el-col>
            </el-row>
            <el-form-item label="异常方法">{{props.row.classMethod}}</el-form-item>
            <el-form-item label="异常url">{{props.row.requestMethod}} : {{props.row.url}}</el-form-item>
            <el-form-item label="Referer">{{props.row.referer}}</el-form-item>
            <el-form-item label="请求参数">{{props.row.params}}</el-form-item>
            <el-form-item label="异常类型">{{props.row.type}}</el-form-item>
            <el-form-item label="异常信息">{{props.row.msg}}</el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column prop="isRead" label="状态" width="80">
        <template slot-scope="scope">
          <el-button type="success" size="small" v-if="scope.row.isRead" disabled>已读</el-button>
          <el-tooltip content="标记为已读" placement="bottom" v-else>
              <el-button type="danger" size="small"  @click="doRead(scope.row)">未读</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="异常时间" width="110">
        <template slot-scope="scope">
          <div>{{scope.row.createTime.split(" ")[0]}}</div>
          <div>{{scope.row.createTime.split(" ")[1]}}</div>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="查看时间" width="110">
        <template slot-scope="scope">
          <div>{{scope.row.updateTime.split(" ")[0]}}</div>
          <div>{{scope.row.updateTime.split(" ")[1]}}</div>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="异常类型" ></el-table-column>
      <el-table-column prop="url" label="异常连接" min-width="200"></el-table-column>
    </el-table>
    
  <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
     :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
  </el-pagination>
</div>

<script>
var defaultQueryForm = {page: 1, pageSize: 10, onlyUnRead: false}
var vm = new Vue({
  el: '#app',
  data: {
    queryForm: Utils.copy(defaultQueryForm),
    total: 0,
    tableData: [],
    tableLoading: false,
    notifyException: null, maxExceptionNotifyPerDay: null
  },
  created: function () {
    this.getData()
  },
  watch: {
  	"queryForm.onlyUnRead": function(){
  	  this.queryForm.page = 1;
  	  this.getData()
  	}
  },
  methods: {
    getData: function () {
      var that = this;
      that.tableLoading = true;
      Resource.get("${_contextPath_}/admin_log/exception/get_page", this.queryForm, function (resp) {
        that.tableData = resp.data.data
        that.total = resp.data.total
        that.tableLoading = false
        that.notifyException = resp.data.notifyException
        that.maxExceptionNotifyPerDay = resp.data.maxExceptionNotifyPerDay
      });
    },
    pageChange: function (page) {
      this.queryForm.page = page;
      this.getData();
    },
    resetQuery: function() {
    	this.queryForm = Utils.copy(defaultQueryForm)
    	this.getData()
    },
    doRead: function (row, msg) {
      var that = this;
      Resource.post("${_contextPath_}/admin_log/exception/read", {
        logExceptionId: row.logExceptionId
      }, function (resp) {
        Message.success(msg ? msg : "标记为已读");
        row.isRead = true;
      });
    },
    doReadAll: function () {
      var that = this;
      Resource.post("${_contextPath_}/admin_log/exception/readAll", {}, function (resp) {
        Message.success("已将所有的记录标记为已读");
        that.getData();
      });
    },
    expandChange: function (row, expandedRows) {
      if (row.isRead == false) {
        this.doRead(row, "已自动标记为已读")
      }
    }
  }
})
</script>