#set($page_title='业务字典管理')

<style>
  .button-updown {padding-left: 12px;padding-right: 12px;}
</style>

<div id="app" v-cloak>

	<el-form :inline="true" @keyup.native.enter="getData">
	  <el-form-item>
	    <el-input v-model="queryForm.name" placeholder="字典名称"></el-input>
	  </el-form-item>
	  <el-form-item>
	    <el-input v-model="queryForm.code" placeholder="字典代号"></el-input>
	  </el-form-item>
	  <el-form-item>
	    <el-button type="primary" @click="(queryForm.page=1) && getData()">查询</el-button>
	    <el-button type="success" @click="handleAddOrEdit(true)">新增</el-button>
	  </el-form-item>
	</el-form>

  <el-table :data="tableData" border stripe v-loading.body="tableLoading">
    <el-table-column prop="name" label="字典名称" min-width="100"></el-table-column>
    <el-table-column prop="code" label="字典代号" min-width="150"></el-table-column>
    <el-table-column prop="description" label="描述" min-width="150"></el-table-column>
    <el-table-column prop="valuesCount" label="值数" min-width="50"></el-table-column>
    <el-table-column label="值示例(前3个)" min-width="250">
      <template slot-scope="props">
        <ul style="padding-left: 5px"><li v-for="o in props.row.values.slice(0,3)">{{o.code}} {{o.name}}</li></ul>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="145">
      <template slot-scope="scope">
        <el-button-group>
          <el-button type="primary" size="small" @click="handleAddOrEdit(false, scope.row)">编辑</el-button>
          <el-button type="primary" size="small" @click="handleValueEdit(scope.row)">编辑值</el-button>
        </el-button-group>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
    :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
  </el-pagination>
  
	<el-dialog :title="dialogTitle" :visible.sync="showDialog" top="10px" :close-on-click-modal="false">
    <el-form :model="addEditForm" label-position="right" label-width="80px" :rules="rules" ref="addEditForm">
      <el-form-item label="字典名称" prop="name">
        <el-input v-model="addEditForm.name" placeholder="字典名称"></el-input>
      </el-form-item>
      <el-form-item label="字典代号" prop="code">
        <el-input v-model="addEditForm.code" placeholder="字典代号，会被程序引用，修改小心">
          <template slot="prepend">
            <el-tooltip effect="dark" content="字典代号可能被程序或数据引用，请小心修改" v-if="addEditForm.id">
              <i class="el-icon-warning" style="color:red"></i>
            </el-tooltip>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="描述信息" prop="description">
        <el-input v-model="addEditForm.description" placeholder="描述信息"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="danger" @click="deleteDict" v-if="addEditForm.id">删除</el-button>
      <el-button @click="showDialog = false">取消</el-button>
      <el-button type="primary" @click="doAddOrEdit">确定</el-button>
    </div>
	</el-dialog>

    <el-dialog title="编辑字典值" :visible.sync="showValueDialog" top="0" width="800px" :close-on-click-modal="false">
      <el-table :data="addEditForm.values" border stripe max-height="400">
        <el-table-column label="名称" min-width="100">
          <template slot-scope="props">
            <el-input v-model="props.row.name"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="代号" min-width="150">
          <template slot-scope="props">
            <el-input v-model="props.row.code"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="描述" min-width="200">
          <template slot-scope="props">
            <el-input v-model="props.row.description"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="155">
          <template slot-scope="props">
            <el-button-group>
              <el-button size="small" @click="valueUpDown(props.$index, true)" class="button-updown">
                <i class="el-icon-arrow-up"></i></el-button>
              <el-button size="small" @click="valueUpDown(props.$index, false)" class="button-updown">
                <i class="el-icon-arrow-down"></i></el-button>
              <el-button type="danger" size="small" @click="deleteValue(props.$index)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
        <p style="padding-top:10px"><i class="el-icon-warning" style="color:red"></i>代号可能被程序或数据引用，请小心修改</p>
        <div slot="footer">
          <el-dropdown split-button type="success" @click="addNewValue(1)" @command="addNewValue">新增一行
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="5">新增5行</el-dropdown-item>
              <el-dropdown-item command="20">新增20行</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
	        <el-button type="primary" @click="saveValues">保存</el-button>
	    </div>
    </el-dialog>
</div>

<script>
var defaultQueryForm = {page: 1, pageSize: 10}
var defaultAddForm = {}
var vm = new Vue({
  el: '#app',
  data: {
    queryForm: Utils.copy(defaultQueryForm),
    addEditForm: Utils.copy(defaultAddForm),
    tableLoading: false,
    total: 0,
    tableData: [],
    showDialog: false,
    dialogTitle: '',
    rules: {
      name: Form.notBlankValidator,
      code: Form.notBlankValidator
    },
    showValueDialog: false
  },
  created: function() {
    this.getData()
  },
  methods: {
    getData: function() {
      var that = this
      that.tableLoading = true
      Resource.get("${_contextPath_}/admin_dict/get_page", this.queryForm, function (resp) {
        that.tableData = resp.data.data
        that.total = resp.data.total
        that.tableLoading = false
      })
    },
    pageChange: function (page) {
      this.queryForm.page = page
      this.getData();
    },
    handleAddOrEdit: function(isAdd, row) {
      this.showDialog = true;
      this.dialogTitle = isAdd ? '新增数据字典' : '编辑'
      Form.clearError(this, 'addEditForm')
      this.addEditForm = isAdd ? Utils.copy(defaultAddForm) : Utils.copy(row)
    },
    doAddOrEdit: function() {
      var that = this, formName = 'addEditForm'
      var isEdit = false
      if(this.addEditForm.id) {isEdit = true}
      Form.validate(this, formName, function() {
        Resource.post("${_contextPath_}/admin_dict/add_or_update", that.addEditForm, function(resp){
          Message.success(isEdit ? "修改成功" : "新增成功");
          isEdit ? (that.showDialog = false) : that.addEditForm = Utils.copy(defaultAddForm)
          that.getData()
        })
      })
    },
    handleValueEdit: function(row) {
      this.showValueDialog = true
      this.addEditForm = Utils.copy(row)
      this.addEditForm.values.push({})
    },
    deleteDict: function() {
      var that = this;
      Message.confirm("请谨慎删除数据字典，它可能被程序或数据引用，确定要删除吗?", function(){
        Resource.post("${_contextPath_}/admin_dict/delete", {id: that.addEditForm.id}, function(){
          Message.success("删除成功，列表已刷新");
          that.getData();
        });
      });
    },
    addNewValue: function(n) {
      for(var i = 0; i < n; i++) this.addEditForm.values.push({})
    },
    deleteValue: function(index) {
      this.addEditForm.values.splice(index, 1)
    },
    saveValues: function() {
      var that = this, names = {}, codes = {}, error = null
      for(var i = 0; i < this.addEditForm.values.length; i++) {
        var value = this.addEditForm.values[i]
        if(value.name&&!value.code || !value.name&&value.code) {
          error = '名称和代号必须同时提供或同时留空'
          break;
        }
        if(value.name && names[value.name]) {
          error = '名称有重复'
          break;
        }
        if(value.code && codes[value.code]) {
          error = '代号有重复'
          break;
        }
        names[value.name] = 'true'
        codes[value.code] = 'true'
      }
      if(error) {
        Message.error(error)
        return
      }
      Resource.post("${_contextPath_}/admin_dict/edit_values", that.addEditForm, function(resp){
        Message.success("修改成功");
        that.showValueDialog = false
        that.getData()
      })
    },
    valueUpDown: function (index, isUp) {
      var noChangeIndex = isUp ? 0 : this.addEditForm.values.length
      var upDown = isUp ? -1 : 1
      if (index != noChangeIndex) {
        var temp = this.addEditForm.values[index]
        this.addEditForm.values.splice(index, 1)
        this.addEditForm.values.splice(index + upDown, 0, temp)
      }
    },
  }
})
</script>