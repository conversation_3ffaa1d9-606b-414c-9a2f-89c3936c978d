#set($page_title='个人资料编辑')

<style></style>

<div id="app" v-cloak>
  <el-card class="pw-block-center" style="width: 600px">
	<el-form label-position="left" label-width="80px" :model="info" :rules="rules" ref="infoForm">
	  <el-form-item label="登录名">
	    <span>{{info.userName}}</span>
	  </el-form-item>
	  <el-form-item label="真实姓名" prop="realName">
	    <el-input v-model="info.realName"></el-input>
	  </el-form-item>
	  <el-form-item label="手机号">
	    <el-input v-model="info.phone"></el-input>
	  </el-form-item>
	  <el-form-item label="Email">
	    <el-input v-model="info.email"></el-input>
	  </el-form-item>
	  <el-form-item label="个性签名">
	    <el-input v-model="info.signature"></el-input>
	  </el-form-item>
	  <el-form-item label="新密码">
	    <el-input v-model="info.password" placeholder="留空表示不修改密码" type="password"></el-input>
	  </el-form-item>
	  <el-form-item label="重输密码" v-show="info.password">
	    <el-input v-model="info.repeatPassword" placeholder="请重输一遍密码" type="password"></el-input>
	  </el-form-item>
	</el-form>
	<div class="pw-block-center" style="width: 200px">
	    <el-button type="primary" @click="edit">保存</el-button>
	    <el-button type="danger" plain @click="logout">退出登录</el-button>
	</div>
  </el-card>
</div>

<script>
var vm = new Vue({
  el: '#app',
  data: {
    info: {},
    rules: {
      realName: Form.notBlankValidator('请填写真实姓名'),
    }
  },
  created: function() {
    var that = this
    Resource.get('${_contextPath_}/admin_user/get_my_info', {}, function(resp){
      that.info = resp.data
    })
  },
  methods: {
    logout: function() {
      Message.confirm('确定退出登录吗?', function() {
      	Resource.post('${_contextPath_}/admin_login/logout', {}, function(resp) {
      		window.top.location.href='${_contextPath_}/' // jump out iframe
		})
      })
    },
    edit: function() {
      Resource.post('${_contextPath_}/admin_user/edit_my_info', this.info)
    }
  }
})
</script>