#set($page_title='角色列表')

<style>
  .el-checkbox-group .el-checkbox {margin-left: 30px  }  /*用户列表对齐*/
</style>

<div id="app" v-cloak>
  <el-form :inline="true" @keyup.native.enter="getData">
    <el-form-item><el-input v-model="queryForm.name" placeholder="角色名称"></el-input></el-form-item>
    <el-form-item><el-input v-model="queryForm.roleGroup" placeholder="角色分组"></el-input></el-form-item>
    <el-form-item>
      <el-button type="primary" @click="getData">查询</el-button>
      <el-button @click="resetQuery">重置</el-button>
      <el-button type="success" @click="handleAddOrEditRole(true)">新增角色</el-button>
    </el-form-item>
  </el-form>

  <el-table :data="tableData" border stripe v-loading.body="tableLoading">
    <el-table-column type="expand">
      <template slot-scope="props">
        <el-col :span="15">
          <div>url列表</div>
          <el-table :data="props.row.urlList" border>
            <el-table-column prop="urlName" label="url名称" width="200"></el-table-column>
            <el-table-column prop="urlDescription" label="url描述"></el-table-column>
          </el-table>
          <span style="color:grey">(仅显示菜单，完整点击[URL]按钮查看)</span>
        </el-col>
        <el-col :span="8" style="margin-left: 20px">
          <div>用户列表</div>
          <el-table :data="props.row.userList" border>
            <el-table-column prop="userName" label="用户名"></el-table-column>
            <el-table-column prop="realName" label="真实姓名"></el-table-column>
          </el-table>
        </el-col>
      </template>
    </el-table-column>
    <el-table-column prop="name" label="角色名称" width="150"></el-table-column>
    <el-table-column prop="code" label="角色编号" width="150"></el-table-column>
    <el-table-column prop="description" label="描述" min-width="100"></el-table-column>
    <el-table-column prop="roleGroup" label="角色分组" min-width="100"></el-table-column>
    <el-table-column prop="urlCount" label="URL数" width="80"></el-table-column>
    <el-table-column prop="userCount" label="用户数" width="80"></el-table-column>
    <el-table-column label="操作" width="300">
      <template slot-scope="scope">
        <el-button type="primary" size="small" @click="handleAddOrEditRole(false, scope.row)">编辑</el-button>
        <el-button type="primary" size="small" @click="handleEditURL(scope.row)">
          <i class="el-icon-edit"></i>URL
        </el-button>
        <el-button type="primary" size="small" @click="handleEditUsers(scope.row)">
          <i class="el-icon-edit"></i>用户
        </el-button>
        <el-button type="danger" size="small" @click="handleDeleteRole(scope.row, scope.$index)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <el-pagination style="float:right" @current-change="pageChange" :current-page="queryForm.page"
    :total="total" :page-size="queryForm.pageSize" layout="total, prev, pager, next, jumper" background>
  </el-pagination>

  <el-dialog :title="dialogTitle" :visible.sync="editRole.visible" top="0" :close-on-click-modal="false">
    <el-form :model="editRole.form" label-position="right" label-width="120px"
             :rules="editRole.rules" ref="editRole.form">
      <el-form-item label="* 角色名称" prop="name">
        <el-input v-model="editRole.form.name" placeholder="角色名称"></el-input>
      </el-form-item>
      <el-form-item label="角色编号" prop="code">
          <el-input v-model="editRole.form.code" placeholder="角色编号">
              <template slot="prepend">
                  <el-tooltip effect="dark" content="角色编号可能被程序或数据引用，请小心修改" 
                      v-if="editRole.form.roleId && editRole.form.code">
                        <i class="el-icon-warning" style="color:red"></i>
                     </el-tooltip>
              </template>
          </el-input>
      </el-form-item>
      <el-form-item label="角色描述" prop="description">
        <el-input v-model="editRole.form.description" placeholder="角色描述"></el-input>
      </el-form-item>
      <el-form-item label="角色分组" prop="roleGroup">
        <el-input v-model="editRole.form.roleGroup" placeholder="角色分组"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="editRole.visible=false">取 消</el-button>
      <el-button type="primary" @click="doAddOrEditRole">保 存</el-button>
    </div>
  </el-dialog>

  <el-dialog :title="dialogTitle" :visible.sync="editURL.visible" top="0" :close-on-click-modal="false">
    <div v-if="!editURL.data.selectedUrl">加载中...</div>
    <div v-else>
      <el-tree :data="editURL.data.urlTree" :props="editURL.defaultProps"
        node-key="id" ref="urlTree" default-expand-all highlight-current
        :expand-on-click-node="false">
		    <span slot-scope="scope">
		      <span>
            <div v-if="scope.data.type=='FOLDER'">
              <el-tag size="mini">目录</el-tag> {{ scope.node.label }}
            </div>
            <el-checkbox v-else v-model="editURL.data.selectedUrl" :label="scope.data.id">
              <el-tag size="mini" type="warning" v-if="scope.data.type=='MENU'">菜单</el-tag>
              <el-tag size="mini" type="info" v-if="scope.data.type=='OTHER'">其它</el-tag>
              {{ scope.node.label }}
            </el-checkbox>
          </span>
		    </span>
      </el-tree>
    </div>
    <div slot="footer">
      <el-button @click="editURL.visible=false">取 消</el-button>
      <el-button type="primary" @click="doRoleUrlSave">保 存</el-button>
    </div>
  </el-dialog>

  <el-dialog :title="dialogTitle" :visible.sync="editUsers.visible" top="0" width="800" :close-on-click-modal="false">
    <div v-if="!editUsers.data.allUsers">加载中...</div>
    <div v-else>
      <el-checkbox-group v-model="editUsers.data.checkUsers">
        <el-checkbox v-for="user in editUsers.data.allUsers" :label="user.userId" @click.native="changeUserList">
          {{user.nickname}}<span v-show="user.realName!=null && user.realName!=''">({{user.realName}})</span>
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div slot="footer">
      <el-button @click="editUsers.visible=false">取 消</el-button>
      <el-button type="primary" @click="doRoleUserSave" :disabled="editUsers.isChange==false">保 存</el-button>
    </div>
  </el-dialog>
</div>

<script>
  var defaultQueryForm = {page: 1, pageSize: 10};
  var defaultEditRoleForm = {name: '', code: '', description: '', roleGroup: ''};
  var vm = new Vue({
    el: '#app',
    data: {
      queryForm: Utils.copy(defaultQueryForm),
      total: 0,
      tableData: [],
      tableLoading: false,
      dialogTitle: '',
      editRole: {
        form: Utils.copy(defaultEditRoleForm),
        rules: {name: Form.notBlankValidator('用户名不能为空')},
        visible: false
      },
      editUsers: {
        data: {roldId: '', allUsers: [], checkUsers: []},
        isChange: false,
        visible: false
      },
      editURL: {
        data: {roleId: '',urlTree: [], selectedUrl: []},
        defaultProps: {children: 'children',label: 'name'},
        visible: false
      }
    },
    created: function () {
      this.getData()
    },
    methods: {
      getData: function () {
        var that = this;
        that.tableLoading = true;
        Resource.get("${_contextPath_}/admin_role/get_page", this.queryForm, function (resp) {
          that.tableData = resp.data.data
          that.total = resp.data.total
          that.tableLoading = false
        });
      },
      pageChange: function (page) {
        this.queryForm.page = page;
        this.getData();
      },
      resetQuery: function() {
        this.queryForm = Utils.copy(defaultQueryForm);
        this.getData();
      },
      handleAddOrEditRole: function (isAdd, row) {
        this.editRole.visible = true;
        this.dialogTitle = isAdd ? '新增角色' : ("编辑角色信息 - " + row.name);
        Form.clearError(this, 'editRole.form')
        this.editRole.form = isAdd ? Utils.copy(defaultEditRoleForm) : Utils.copy(row)
      },
      doAddOrEditRole: function () {
        var that = this;
        var formName = 'editRole.form';
        var isEdit = this.editRole.form.roleId ? true : false;
        Form.validate(this, formName, function () {
          Resource.post("$!{_contextPath_}/admin_role/add_or_edit", that.editRole.form, function (resp) {
            Message.success(isEdit ? "修改角色信息成功" : "新增角色成功");
            isEdit ? (that.editRole.visible = false) : (that.editRole.form = Utils.copy(defaultEditRoleForm));
            that.getData();
          })
        })
      },
      handleEditURL: function (row) {
        var that = this;
        var roleId = row.roleId;
        this.editURL.data.roleId = roleId;
        this.editURL.data.urlTree = [];
        this.editURL.data.selectedUrl = null;
        this.editURL.visible = true;
        this.dialogTitle = '编辑URL权限 - ' + row.name;
        Resource.get('${_contextPath_}/admin_url/tree_data', {}, function (resp) {
          that.editURL.data.urlTree = resp.data;
          Resource.get('${_contextPath_}/admin_role_url/getTreeUrlIdsByRole', {roleId: roleId}, function (resp) {
            if (that.editURL.data.roleId == roleId) {
              that.editURL.data.selectedUrl = resp.data.checkIds
            }
          })
        })
      },
      doRoleUrlSave: function () {
        var that = this;
        Resource.post('$!{_contextPath_}/admin_role_url/saveRoleUrls', {
          roleId: this.editURL.data.roleId,
          roleUrls: this.editURL.data.selectedUrl
        }, function (resp) {
          Message.success('保存url列表成功');
          that.editURL.visible = false;
          that.getData();
        })
      },
      handleEditUsers: function (row) {
        var that = this;
        var roleId = row.roleId;
        this.dialogTitle = '编辑用户列表 - ' + row.name;
        this.editUsers.visible = true;
        this.editUsers.data.roleId = roleId;
        this.editUsers.data.allUsers = null;
        this.editUsers.data.checkUsers = [];
        this.editUsers.isChange = false;
        Resource.get('$!{_contextPath_}/admin_user_role/userListByRoleId', {roleId: roleId}, function (resp) {
          if (that.editUsers.data.roleId == roleId) {
            var checkUserList = resp.data.userList;
            var tempCheckUserIds = new Array();
            for (var i = 0; i < checkUserList.length; i++) {
              tempCheckUserIds[i] = checkUserList[i].userId;
            }
            that.editUsers.data.allUsers = resp.data.allUserList;
            that.editUsers.data.checkUsers = tempCheckUserIds;
          }
        })
      },
      changeUserList: function () {
        this.editUsers.isChange = true;
      },
      doRoleUserSave: function () {
        var that = this;
        Resource.post('$!{_contextPath_}/admin_user_role/saveRoleUsers', {
          roleId: that.editUsers.data.roleId,
          roleUsers: that.editUsers.data.checkUsers
        }, function (resp) {
          Message.success('保存角色列表成功');
          that.editUsers.visible = false;
          that.getData();
        })
      },
      handleDeleteRole: function (row, index) {
        var that = this;
        Message.confirm("确定要删除角色【" + row.name + "】吗？", function () {
          Resource.post("$!{_contextPath_}/admin_role/delete", {
            roleId: row.roleId
          }, function () {
            Message.success("删除成功");
            that.getData();
          })
        })
      }
    }
  })
</script>