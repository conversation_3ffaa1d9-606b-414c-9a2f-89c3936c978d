#set($page_title='系统URL/权限管理')

<style>
  .custom-tree-node {flex: 1;display: flex;align-items: center;justify-content: space-between;font-size: 14px;padding-right: 8px;
     height:100%;/*修复官方slot不是100%高导致点击编辑偶发没反应的问题*/}
  .el-checkbox-group .el-checkbox {margin-left: 30px} /*角色设置对齐*/
</style>

<div id="app" v-cloak>
  <el-row v-show="treeData" :gutter="10">
    <el-col :span="12">
      <el-row>
        <el-button plain @click="toggleTreeExpand" style="width:18%">{{treeExpand?'全部收起':'全部展开'}}</el-button>
        <el-button plain type="success" @click="append(null)" style="width:18%">
            <i class="el-icon-circle-plus-outline"></i>根节点</el-button>
        <el-input placeholder="输入关键字进行过滤，拖拽节点可排序" v-model="filterText" style="width:60%"></el-input>
      </el-row>
      <el-row>
        <el-tree :data="treeData" node-key="id" ref="tree" highlight-current :props="treeProps"
          class="filter-tree" :filter-node-method="filterNode" draggable :expand-on-click-node="false"
          @node-drop="saveTreeBtnType = 'primary'" :allow-drag="allowDrag" :allow-drop="allowDrop">
		    <span class="custom-tree-node" slot-scope="scope" @click="edit(scope.data, scope.node)">
		      <span v-if="!(scope.data.scanned && scope.data.type=='FOLDER')">
		        <el-tag size="mini" v-if="scope.data.type=='FOLDER'">目录</el-tag>
                <el-tag size="mini" type="warning" v-if="scope.data.type=='MENU'">菜单</el-tag>
                <el-tag size="mini" type="info" v-if="scope.data.type=='OTHER'">其它</el-tag>
                <el-tooltip content="该URL不在扫描URL列表中，请检查" 
                    v-if="scope.data.type!='FOLDER' && scope.data.type!='MENU' && !scope.data.scanned && !scope.data.inScan">
                    <span style="color:red">{{ scope.node.label }}</span>
                </el-tooltip>
                <span v-else>{{ scope.node.label }}</span>
		      </span>
              <span v-else><el-tag size="mini" type="info">{{ scope.node.label }}</el-tag></span>
              <span>
                <i class="el-icon-circle-plus-outline" v-if="!isInScannedNode(scope.node)" @click="append(scope.data)"></i>
                <i class="el-icon-delete" v-if="!(scope.data.scanned&&scope.data.type=='FOLDER')" @click="remove(scope.node, scope.data)"></i>
              </span>
		    </span>
        </el-tree>
      </el-row>
    </el-col>
    <el-col :span="12" style="position:fixed;right:0">
      <el-row>
        <el-button :type="saveTreeBtnType" @click="saveTree" :disabled="saveTreeBtnType=='info'">保存树形结构</el-button>
      </el-row>
      <div v-if="!isEditScannedNode">
	      <el-card>
	        <el-form :model="editForm" label-position="right" label-width="80px" :rules="rules" ref="editForm">
	          <el-form-item label="* 节点类型" prop="type">
	            <el-select v-model="editForm.type" placeholder="节点类型">
	              <el-option key="FOLDER" label="目录" value="FOLDER" :disabled="!(editForm.type=='FOLDER'||!editForm.url)"></el-option>
	              <el-option key="MENU" label="菜单" value="MENU" :disabled="editForm.type=='FOLDER'||!isEditParentFolder"></el-option>
	              <el-option key="OTHER" label="其它" value="OTHER" :disabled="editForm.type=='FOLDER'"></el-option>
	            </el-select>
	          </el-form-item>
	          <el-form-item label="* 名称" prop="name">
	            <el-input v-model="editForm.name" placeholder="名称" style="width:50%"></el-input>
	          </el-form-item>
	          <el-form-item label="图标" prop="icon">
	            <el-input v-model="editForm.icon" placeholder="图标className" style="width:50%"></el-input>
	            <a target="_blank" href="http://element-cn.eleme.io/#/zh-CN/component/icon" style="text-decoration: none">查询图标名称</a>
	          </el-form-item>
	          <el-form-item label="URL/权限" prop="url" v-show="editForm.type=='MENU' || editForm.type=='OTHER'">
	            <el-input v-model="editForm.url" placeholder="菜单类型必须是精确url，其它类型支持正则表达式"></el-input>
	          </el-form-item>
	          <el-form-item label="备注" prop="description">
	            <el-input v-model="editForm.description" placeholder="备注"></el-input>
	          </el-form-item>
	        </el-form>
	        <el-row style="text-align: center">
	          <el-tooltip class="item" effect="dark" content="修改将直接保存到数据库" placement="bottom" v-if="editForm.id">
	            <el-button type="primary" @click="doEdit" :disabled="!editData">修改</el-button>
	          </el-tooltip>
	          <el-button type="primary" @click="doEdit" :disabled="!editData" v-else>修改</el-button>
	        </el-row>
	      </el-card>
	      <el-card v-show="editData">
	        <div v-if="editForm.type=='FOLDER'">目录无需设置角色</div>
	        <div v-else-if="!editForm.id">请先保存URL树形结构，再点击编辑角色</div>
	        <div v-else-if="!allRoles">角色加载中...</div>
	        <div v-else>
            <p>角色设置
              <el-tooltip placement="top" content="删除角色将自动删除该节点下所有子节点对应角色">
                <el-tag size="mini" type="info">说明</el-tag>
              </el-tooltip>
            </p>
            <el-checkbox-group v-model="checkUrlRoles">
              <el-checkbox v-for="urlRole in allRoles" :label="urlRole.roleId" @click.native="isChangeUrlRoles=true">
                {{urlRole.roleName}}
              </el-checkbox>
            </el-checkbox-group>
	          <el-row style="text-align: center">
	            <el-tooltip class="item" effect="dark" content="保存将直接保存至数据库" placement="bottom">
	              <el-button type="primary" @click="doUrlRoleSave" :disabled="isChangeUrlRoles==false">保存</el-button>
	            </el-tooltip>
	          </el-row>
	        </div>
	      </el-card>
      </div>
      <div v-else>
        <el-card>请将扫描节点拖入URL树再编辑</el-card>
      </div>
    </el-col>
  </el-row>

  <el-dialog title="新增节点" :visible.sync="showAdd" top="30px" :close-on-click-modal="false">
    <el-form :model="addForm" label-position="right" label-width="80px" :rules="rules" ref="addForm">
      <el-form-item label="* 节点类型" prop="type">
        <el-select v-model="addForm.type" placeholder="节点类型">
          <el-option key="FOLDER" label="目录" value="FOLDER" v-show="curAppend==treeData||curAppend.type=='FOLDER'"></el-option>
          <el-option key="MENU" label="菜单" value="MENU" v-show="curAppend==treeData||curAppend.type=='FOLDER'"></el-option>
          <el-option key="OTHER" label="其它" value="OTHER"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="* 名称" prop="name">
        <el-input v-model="addForm.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item label="URL/权限" prop="url" v-show="addForm.type=='MENU' || addForm.type=='OTHER'">
        <el-input v-model="addForm.url" placeholder="菜单类型必须是精确url，其它类型支持正则表达式"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="showAdd = false">取 消</el-button>
      <el-button type="primary" @click="doAppend">确 定</el-button>
    </div>
  </el-dialog>
</div>

<script>
  var vm = new Vue({
    el: '#app',
    data: {
      treeData: null,
      treeProps: {children: 'children',label: 'name'},
      treeExpand: false,
      filterText: '',
      showAdd: false,
      rules: {
        name: Form.notBlankValidator('名称不能为空'),
        type: Form.notBlankValidator('类型必须选择')
      },
      curAppend: {},
      addForm: {},
      editForm: {},
      editData: null,
      isEditScannedNode: false, isEditParentFolder: false,
      saveTreeBtnType: 'info',
      isChangeUrlRoles: false,
      allRoles: null,
      checkUrlRoles: null
    },
    created: function() {
      var that = this
      Resource.get('${_contextPath_}/admin_url/tree_data?scan=true', {}, function (resp) {
        that.treeData = resp.data
      })
    },
    watch: {
      filterText: function (val) {this.$refs.tree.filter(val)}
    },
    computed: {
    	hasScannedFolder: function(){
    		return this.treeData.length>0 && this.treeData[this.treeData.length-1].scanned}
    },
    methods: {
      isInScannedNode: function(node) { // 是否是扫描目录下的节点
    	  if(!node) return false;
    	  do {
    		  if(node.data && node.data.type=='FOLDER' && node.data.scanned) return true
    		  node = node.parent
    	  } while (node)
    	  return false
      },
      filterNode: function (value, data) {
        if (!value) return true;
        var valueUpperCase = value.toUpperCase();
        var dataNameUpperCase = data.name.toUpperCase();
        return dataNameUpperCase.indexOf(valueUpperCase) !== -1;
      },
      append: function (data) {
        if(data) {
        	this.curAppend = data
        	var type = (data.type=='MENU'||data.type=='OTHER')?'OTHER':'MENU'
        	this.addForm = {type: type}
        	// this.addForm.type = (data.type=='MENU'||data.type=='OTHER')?'OTHER':'MENU' // 这样不行
        } else {
        	this.curAppend = this.treeData
        	this.addForm = {type: 'FOLDER'}
        	// this.addForm.type = 'FOLDER' // 这样不行
        }
        this.showAdd = true
      },
      doAppend: function () {
        var that = this
        Form.validate(this, 'addForm', function () {
          var newChild = Utils.copy(that.addForm)
          newChild.id = null; newChild.children=[]; newChild.webkey=Utils.uuid()
          if (that.curAppend == that.treeData) {
            that.hasScannedFolder ? that.treeData.splice(that.treeData.length-1, 0, newChild)
            		: that.treeData.push(newChild);
          } else {
            if (!that.curAppend.children) that.$set(that.curAppend, 'children', []);
            that.curAppend.children.push(newChild);
          }
          that.showAdd = false
          that.saveTreeBtnType = 'primary'
        })
      },
      remove: function (node, data) {
        if(this.isInScannedNode(node)) { // 对于扫描的节点，删除即忽略
          Message.confirm("删除扫描节点将忽略自动扫描该节点，它将不会再出现在扫描列表中", function () {
            Resource.post('${_contextPath_}/admin_url/ignore_url', {url:data.url}, function(){
              Message.success('删除成功')
              var parent = node.parent;
              var children = parent.data.children || parent.data;
              var index = children.findIndex(function(d) {return d.name === data.name})
              children.splice(index, 1);
            })
          })
        } else {
          var msg = '确定要删除节点【' + data.name + '】吗?'
          if (data.children && data.children.length > 0) msg += '将删除其下级所有节点.'
          var that = this
          Message.confirm(msg, function () {
            var parent = node.parent;
            var children = parent.data.children || parent.data;
            var index;
            if (data.id) {
              index = children.findIndex(function(d) {return d.id === data.id});
            } else {
              index = children.findIndex(function(d) {return d.webkey === data.webkey});
            }
            children.splice(index, 1);
            that.resetEdit()
            that.saveTreeBtnType = 'primary'
          })
        }
      },
      resetEdit: function() {
    	  this.editForm = {}; this.editData = null
          this.allRoles = null;this.checkUrlRoles = null;this.isChangeUrlRoles = false;
      },
      edit: function (data, node) {
        if(node) {
          this.isEditScannedNode = this.isInScannedNode(node)
          this.isEditParentFolder = node.parent && node.parent.data.type == 'FOLDER'
        }
        this.resetEdit()
        this.editForm = Utils.copy(data)
          this.editData = data
          if(data.id && data.type!='FOLDER') this.getUrlRolesByUrlId(data.id);
      },
      doEdit: function () {
        var that = this
        Form.validate(this, 'editForm', function () {
          Utils.extend(that.editData, that.editForm)
          if (that.editForm.id) {
            Resource.post('${_contextPath_}/admin_url/save_node', that.editForm)
          } else {
            Message.success("临时保存成功，记得最后【保存树形结构】")
          }
        })
      },
      findData: function(id, webkey) { // 找到任意一个id或webkey相同即可
    	  function _find(datas, id, webkey) {
          for(var i = 0; i < datas.length; i++) {
            if(datas[i].id == id || datas[i].webkey == webkey) return datas[i]
            if(datas[i].children) {
              var tmp = _find(datas[i].children, id, webkey)
              if(tmp) return tmp
            }
          }
          return null
        }
        return _find(this.treeData, id, webkey)
      },
      saveTree: function () {
        var that = this
        var editWebkey = this.editForm.webkey; var editId = this.editForm.id
        Resource.post('${_contextPath_}/admin_url/save_tree', {treeData: this.treeData}, function(){
          Resource.get('${_contextPath_}/admin_url/tree_data?scan=true', {}, function (resp) {
            var expandKeys = that._getExpandKey()
            that.treeData = resp.data
            Message.success('保存成功')
            that.saveTreeBtnType = 'info'
            if(editWebkey || editId) {
                var data = that.findData(editId, editWebkey) // 保持编辑块有效
                data && data.id ? that.edit(data) : that.resetEdit()
            }
            that.$nextTick(function(){that._setExpandKey(expandKeys)})
          })
        }, function (resp) {
          if(resp.data && resp.data.length>0) {
        	  Message.error("发现" + resp.data.length + "个错误: " + resp.data[0].msg + "等")
          }
        })
      },
      getUrlRolesByUrlId: function (urlId) {
        var that = this;
        Resource.get('$!{_contextPath_}/admin_role_url/roleListByUrlId', {urlId: urlId}, function(resp) {
          if (urlId == that.editForm.id) {
            that.allRoles = resp.data.allRoles;
            that.checkUrlRoles = resp.data.urlRoles;
          }
        })
      },
      doUrlRoleSave: function () {
        var that = this;
        Resource.post('$!{_contextPath_}/admin_role_url/saveUrlRoles', {
          urlId: that.editForm.id,
          urlRolelist: that.checkUrlRoles
        }, function (resp) {
          Message.success('保存角色列表成功');
        })
      },
      allowDrag: function(node) {
    	  return !(node.data.scanned && node.data.type=='FOLDER')
      },
      allowDrop: function(draggingNode, dropNode, type) {
    	  if(type == 'inner') {
          if(draggingNode.data.type!='OTHER' && dropNode.data.type != 'FOLDER') return false;
          if(this.isInScannedNode(dropNode)) return false;
    	  }
    	  return true;
      },
      toggleTreeExpand: function() {
        this.treeExpand = !this.treeExpand
        var nodes = this.$refs.tree.store._getAllNodes()
        for(var i=0;i<nodes.length;i++) nodes[i].expanded=this.treeExpand;
      },
      _getExpandKey: function() {
        var nodes = this.$refs.tree.store._getAllNodes()
        var keys = []
        for(var i=0;i<nodes.length;i++) {
          if(nodes[i].expanded) {
            if(nodes[i].data.id) keys.push(nodes[i].data.id)
            if(nodes[i].data.webkey) keys.push(nodes[i].data.webkey)
          }
        }
        return keys
      },
      _setExpandKey: function(keys) {
        var nodes = this.$refs.tree.store._getAllNodes()
        for(var i=0;i<nodes.length;i++) {
          if(keys.indexOf(nodes[i].data.id)>=0 || keys.indexOf(nodes[i].data.webkey)>=0)
            nodes[i].expanded=true
        }
      }
    }
  })
</script>