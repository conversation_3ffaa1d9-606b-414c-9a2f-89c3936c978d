#set($layout="no_layout.vm")
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="renderer" content="webkit">

    <link rel="stylesheet" href="${_resourceContextPath_}/admin_static/css/elementui/index.css?v=2.15.14">
    <link rel="stylesheet" href="${_resourceContextPath_}/admin_static/css/pw-common.css?v=2.0.13">

    <style>
        .pw-header {text-align: center;color: #666; padding-left: 26px;}
        .pw-siderbar {width: 200px;position:fixed;top:0;height: 100%;border-radius:0;overflow-y: scroll;overflow-x: hidden;}
        .pw-content {padding: 0;}
        .pw-content-out {margin:0 0 0 200px;}
        .pw-content-in {margin:0 0 0 0px;}
        .pw-content iframe {padding:0;width: 100%;height: 100%;border: none;}
        .pw-fix-bottom {position: fixed;bottom: 0; width: 200px;}
        .pw-space-bottom {height: 36px;overflow: auto;}
        .pw-collapse {width:26px;height:26px; position: absolute;cursor: pointer;z-index: 1000;}

        .fade-enter-active, .fade-leave-active {transition: all .3s;}
        .fade-enter, .fade-leave-to {transform: translateX(-200px);opacity: 0;}
    </style>
</head>
<body>
<div id="body" v-cloak>

    <div class="pw-collapse" @click="toggleMenu" :style="{opacity: pwCircleOpacity}">
        <svg width="64" height="64" fill="#777" style="height:26px;width:26px" version="1.1" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="m966.8 568.85h-909.61c-31.397 0-56.851-25.453-56.851-56.851 0-31.397 25.453-56.85 56.851-56.85h909.61c31.397 0 56.85 25.453 56.85 56.85 0.001023 31.398-25.452 56.851-56.85 56.851z"/><path d="m966.8 881.53h-909.61c-31.397 0-56.851-25.453-56.851-56.85s25.453-56.85 56.851-56.85h909.61c31.397 0 56.85 25.453 56.85 56.85 0.001023 31.397-25.452 56.85-56.85 56.85z"/><path d="m966.8 256.17h-909.61c-31.397 0-56.851-25.453-56.851-56.85s25.453-56.851 56.851-56.851h909.61c31.397 0 56.85 25.453 56.85 56.851 0.001023 31.397-25.452 56.85-56.85 56.85z"/></svg>
    </div>
    <transition name="fade">
        <el-menu class="pw-siderbar" :unique-opened="true" @select="handleClick"
                 :default-active="menuKey" background-color="#f6f8f9" text-color="#222222" v-show="!isCollapse"
                 active-text-color="#1aad19">
            <h3 class="pw-header">{{menuInfo.systemName}}</h3>
            <item :model="menuInfo.menus"></item>

            <div class="pw-space-bottom"></div>
            <el-row class="pw-fix-bottom">
                <el-button-group>
                    <el-button icon="el-icon-message" size="medium" style="width:80px;"></el-button>
                    <el-button icon="el-icon-setting" size="medium" style="width:120px;" @click="myinfo">{{menuInfo.userName}}</el-button>
                </el-button-group>
            </el-row>
        </el-menu>
    </transition>
    <div class="pw-content" :class="pwContentClass">
        <iframe :src="frameSrc" id="mainframe"></iframe>
    </div>
</div>
</html>

<script src="${_resourceContextPath_}/admin_static/js/vue.min.js"></script>
<script src="${_resourceContextPath_}/admin_static/js/elementui/index.js?v=2.15.14"></script>
<script src="${_resourceContextPath_}/admin_static/js/vue-resource.min.js"></script>
<script src="${_resourceContextPath_}/admin_static/js/common-utils.min.js?v=2.0.13"></script>

<script type="text/x-template" id="item-template">
    <span>
  <span v-for="(menu,index) in model">
    <el-submenu v-if="menu.subs" :index="level+'-'+index">
        <template slot="title">
            <i :class="menu.icon" v-if="menu.icon"></i>{{menu.title}}</template>
        <item :model="menu.subs" :level="level+'-'+index"></item>
    </el-submenu>
    <el-menu-item v-else :index="menu.url">
        <i :class="menu.icon" v-if="menu.icon"></i>{{menu.title}}</el-menu-item>
  </span>
</span>
</script>
<script>
    Vue.component('item', {
        template: '#item-template',
        props: {
            model: Array,
            level: '1'
        }
    })
</script>

<script>

    function _getMenu(url, menus) {
        if(menus) {
            for(var i=0; i<menus.length; i++) {
                if(menus[i].subs && menus[i].subs.length > 0) {
                    var menu = _getMenu(url, menus[i].subs);
                    if(menu) return menu
                } else {
                    if(url == menus[i].url) return menus[i]
                }
            }
        }
        return null
    }

    window._index_vm = new Vue({
        el: '#body',
        data: {
            menuInfo: {},
            menuKey: '',
            frameSrc: '',
            isCollapse: false,
            pwContentClass: '',
            pwCircleOpacity: 0,
            isPressedCtrl: false,
            myinfoUrl: '${_contextPath_}/admin_user/my'
        },
        created: function() {
            this.getData()
            var showMenu = localStorage.getItem("_index_show_menu");
            showMenu === "false" ? this._hideMenu(true) : this._showMenu(true)

            var href = window.location.hash.substr(1)
            this.menuKey = href
            this.frameSrc = href

            if (!href) {
                this._showMenu(false) // 空白页面无论如何显示菜单但不更改菜单开关storage
            }

            this.setDocTitle()

            var that = this;
            window.addEventListener('keydown', function(event){
                if(event.keyCode == 17) that.isPressedCtrl = true
            })
            window.addEventListener('keyup', function(event){
                if(event.keyCode == 17) that.isPressedCtrl = false
            })
        },
        mounted: function() {
            document.addEventListener('keyup', this.handleEscKeyPress);
        },
        methods: {
            handleEscKeyPress(event) {
                if (event.key === "Escape") {
                    this.toggleMenu()
                }
            },
            getData: function() {
                var that = this
                Resource.get("${_contextPath_}/index_menu", {}, function (resp) {
                    that.menuInfo = resp.data
                    document.title = that.menuInfo.systemName
                })
            },
            handleClick: function(key, keyPath) {
                if(!key) return
                if(this.isPressedCtrl) {
                    window.open(key)
                } else {
                    location.href = "#" + key
                    this.frameSrc = this.menuKey = key
                    this.setDocTitle()
                }
            },
            toggleMenu: function() {
                this.isCollapse ? this._showMenu(true) : this._hideMenu(true)
            },
            getMenu: function(url) {
                return _getMenu(url, this.menuInfo.menus)
            },
            _showMenu: function(isStorage) {
                this.isCollapse = false
                this.pwContentClass = "pw-content-out"
                this.pwCircleOpacity = 0.9
                if (isStorage) {localStorage.setItem("_index_show_menu", "true")}
            },
            _hideMenu: function(isStorage) {
                this.isCollapse = true
                this.pwContentClass = "pw-content-in"
                this.pwCircleOpacity = 0.1
                if (isStorage) {localStorage.setItem("_index_show_menu", "false")}
            },
            setDocTitle: function() {
                var title
                if(this.frameSrc == this.myinfoUrl) title = "个人信息编辑"
                if(this.menuKey && !title) {
                    var menu = this.getMenu(this.menuKey)
                    if(menu) title = menu.title
                }
                if(title) document.title = title + " | " + this.menuInfo.systemName
            },
            myinfo: function() {
                if(this.isPressedCtrl) {
                    window.open(this.myinfoUrl)
                } else {
                    location.href = "#" + this.myinfoUrl
                    this.frameSrc = this.myinfoUrl
                    this.setDocTitle()
                }
            }
        }
    })

</script>
