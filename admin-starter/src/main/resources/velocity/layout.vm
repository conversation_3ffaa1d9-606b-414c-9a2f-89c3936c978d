<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <meta name="renderer" content="webkit">

  <title>$!page_title</title>

  <link rel="stylesheet" href="${_resourceContextPath_}/admin_static/css/elementui/index.css?v=2.15.14">
  <link rel="stylesheet" href="${_resourceContextPath_}/admin_static/css/pw-common.css?v=2.0.13">

  <script src="${_resourceContextPath_}/admin_static/js/vue.js"></script>
  <script src="${_resourceContextPath_}/admin_static/js/vue-resource.min.js"></script>
  <script src="${_resourceContextPath_}/admin_static/js/elementui/index.js?v=2.15.14"></script>

  <script src="${_resourceContextPath_}/admin_static/js/common-utils.min.js?v=2.0.13"></script>

  <script src="${_resourceContextPath_}/admin_static/js/el-table-sticky.umd.min.js?v=1.3.2"></script>

  #parse('layout_ex.vm')

</head>
<body>
  $screen_content
</body>

<script>
  if (window.parent && window.parent._index_vm && window.parent._index_vm.toggleMenu) {
    document.addEventListener('keyup', function() {
      if (event.key === "Escape") {
        window.parent._index_vm.toggleMenu()
      }
    });
  }
</script>

</html>