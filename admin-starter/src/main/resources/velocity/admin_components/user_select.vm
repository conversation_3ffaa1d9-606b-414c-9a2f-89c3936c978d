<script type="text/x-template" id="A99c63460c32d4a0c">
  <el-select v-model="myvalue" :multiple="multiple" filterable remote :reserve-keyword="multiple" :clearable="true"
    :placeholder="placeholder" :remote-method="remoteMethod"
    @visible-change="visibleChange">
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
    </el-option>
  </el-select>
</script>
<script>
Vue.component('user-select', {
    template: '#A99c63460c32d4a0c',
    props: {
        value: '', // v-model
        placeholder: {
        	type: String, default: '用户名、真实姓名、手机号'
        },
        multiple: { // 是否多选
        	type: Boolean, default: false
        }
    },
    data: function() {
    	return {
    		myvalue: '',
    		options: []
    	}
    },
    created: function() { // 处理回显值
    	if(this.value) {
    		this.myvalue = Array.isArray(this.value) ? this.value.map(function(o){return o+''}) : this.value+''
    	}
    	if(this.myvalue) this.initOption(this.myvalue)
    },
    watch: {
    	myvalue: function(val) {
    		this.$emit("input", val)
    	},
    	value: function(val) {
    	  this.myvalue = val ? val : ''
    	}
    },
    methods: {
    	_query: function(param, callback) { // 只缓存无条件的查询
    	  if(!param) {
    	    var data = window['_user_select_components_data']
    	    if(data) {
    	      this.options = data
    	      if(callback) callback()
    	      return
    	    }
    	  }
	   	  var that = this
		  Resource.get('${_contextPath_}/admin_user/query_user', param, function(resp){
	       	that.options = resp.data.map(function(o) {
	            return {value: o.id+'', label: o.userName + (o.realName?'('+o.realName+')':'')}
	        })
	        if(!param) window['_user_select_components_data'] = that.options
	        if(callback) callback()
		  })
    	},
    	initOption: function(id) {
    		var matched = this.options.filter(function(o) {return o.id==id})
    		if(matched.length==0) {
    			Array.isArray(id) ? this._query({ids:id}) : this._query({id:id})
    		}
    	},
    	remoteMethod: function(query) {
    		var that = this
    		this._query(query ? {q:query} : null)
        },
        visibleChange: function(val) {
            if(val) {
              if(!this.myvalue || Array.isArray(this.myvalue)&&this.myvalue.length==0) this.remoteMethod('')
            }
        }
    }
})
</script>