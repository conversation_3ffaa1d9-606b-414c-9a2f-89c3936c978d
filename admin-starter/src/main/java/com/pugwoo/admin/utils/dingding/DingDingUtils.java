package com.pugwoo.admin.utils.dingding;

import com.pugwoo.admin.config.properties.DingDingProperties;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DingDingUtils {

    public DingDingUtils(DingDingProperties dingDingProperties) {
        setRobots(dingDingProperties.getRobots());
    }

    private Map<String, String> accessTokenToSecretMap;

    private void setRobots(List<DingDingProperties.RobotDTO> robots) {
        accessTokenToSecretMap = new HashMap<>();
        ListUtils.forEach(robots, o -> {
            accessTokenToSecretMap.put(o.getAccessToken(), o.getSecret());
        });
    }

    /**
     * 发送文本消息
     * @return 成功返回true
     */
    public boolean send(String robotAccessToken, String text) {
        return send(robotAccessToken, DingDingMsgDTO.of(text));
    }

    /**
     * 发送消息
     * @return 成功返回true
     */
    public boolean send(String robotAccessToken, DingDingMsgDTO msg) {
        String secret = accessTokenToSecretMap.get(robotAccessToken);

        String url = "https://oapi.dingtalk.com/robot/send?access_token=" + robotAccessToken;

        if (StringTools.isNotBlank(secret)) {
            try {
                Long timestamp = System.currentTimeMillis();
                String stringToSign = timestamp + "\n" + secret;
                Mac mac = Mac.getInstance("HmacSHA256");
                mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
                byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
                String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)),"UTF-8");
                url += "&timestamp=" + timestamp + "&sign=" + sign;
            } catch (Exception e) {
                log.error("sign fail", e);
                return false;
            }
        }

        Browser browser = new Browser();
        try {
            HttpResponse resp = browser.postJson(url, msg);
            return resp.getResponseCode() == 200;
        } catch (IOException e) {
            log.error("call dingding api fail, url:{}, param:{}", url, JSON.toJson(msg), e);
            return false;
        }
    }

}
