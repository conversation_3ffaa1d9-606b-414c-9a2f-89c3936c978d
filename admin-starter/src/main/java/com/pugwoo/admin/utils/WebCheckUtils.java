package com.pugwoo.admin.utils;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;

/**
 * web层检查工具类，通过抛异常的形式，简化了代码
 * <AUTHOR>
 */
public class WebCheckUtils {
	
	/**
	 * 清除掉前端传入的DO中的自动设置的信息，避免给用户机会修改这些基础信息值
	 * @param baseDO
	 */
	public static void clearBaseInfo(AdminBaseDO baseDO) {
		if(baseDO != null) {
			baseDO.setDeleted(null);
			baseDO.setCreateTime(null);
			baseDO.setCreateUserId(null);
			baseDO.setUpdateTime(null);
			baseDO.setUpdateUserId(null);
		}
	}

	public static void assertNotBlank(String str, String errMsg) {
		if(StringUtils.isBlank(str)) {
			throw new AdminInnerException(AdminErrorCode.ILLEGAL_PARAMETERS, errMsg);
		}
	}
	
	public static void assertNotEmpty(String str, String errMsg) {
		if(StringUtils.isEmpty(str)) {
			throw new AdminInnerException(AdminErrorCode.ILLEGAL_PARAMETERS, errMsg);
		}
	}
	
	public static void assertNotNull(Object obj, String errMsg) {
		if(obj == null) {
			throw new AdminInnerException(AdminErrorCode.ILLEGAL_PARAMETERS, errMsg);
		}
	}
	
	/**
	 * 断言匹配上正则表达式
	 * @param str 当str为null时，断言不匹配
	 * @param regex 正则表达式，必须提供，为null时断言不匹配
	 * @param errMsg
	 */
	public static void assertRegexMatch(String str, String regex, String errMsg) {
		if(str == null || regex == null) {
			throw new AdminInnerException(AdminErrorCode.ILLEGAL_PARAMETERS, errMsg);
		}
		if(!str.matches(regex)) {
			throw new AdminInnerException(AdminErrorCode.ILLEGAL_PARAMETERS, errMsg);
		}
	}
	
	/**
	 * 仅适用于中国手机号
	 */
	public static void assertPhone(String str) {
		assertRegexMatch(str, "^1\\d{10}$", "请填写正确的手机号");
	}

	/**
	 * 一种简单的CSRF检查方案，用于Spring MVC框架
	 * 有两种其它方案：1. 每个请求带csrfToken，但每个请求带csrfToken的方式成本过高，前后端依赖加重
	 *       2.为每个请求指定只有POST方法支持，可以有效防御CSRF，但容易人为疏忽忘加，其次每个接口都加上也增加了工作量
	 *
	 * 本方案：对于api接口（判断方式是接口方法注解了ResponseBody或类注解了RestController），
	 *       要求其为ajax请求，否则csrf校验不通过。
	 *       为了加强安全性，再加上判断其Referer必须有值
	 *
	 * 特别的: 对于文件上传，不进行ajax方式校验，
	 *        同时要求文件上传必须是注入MultipartFile(或其子类)或MultipartFile[]的方式
	 *
	 * 说明：ajax请求在现代浏览器中，除非服务器CORS头部允许，否则会被浏览器拦截。
	 *
	 * @param request
	 * @param handler spring mvc拦截器的handler参数，只处理HandlerMethod类型
	 * @return 返回true表示csrf校验通过，返回false为不通过
	 */
	public static boolean csrfPassed(HttpServletRequest request, Object handler, boolean isRequireLogin) {
		if(!(handler instanceof HandlerMethod)) {
			return true;
		}

		HandlerMethod handlerMethod = (HandlerMethod) handler;
		boolean isResponseBody = handlerMethod.getMethodAnnotation(ResponseBody.class) != null;
		boolean isRestController = handlerMethod.getBeanType().getAnnotation(RestController.class) != null;

		if(!(isResponseBody || isRestController)) {
			return true;
		}

		// 对于上传文件接口，不采用XMLHttpRequest方式校验
		// 上传文件接口判断依据：输入参数有MultipartFile 或 MultipartFile[] 类型参数
		boolean isFileUpload = false;
		MethodParameter[] methodParameters = handlerMethod.getMethodParameters();
		if(methodParameters != null) {
			for(MethodParameter methodParameter : methodParameters) {
				Class<?> typeClass = methodParameter.getParameterType();
				if(MultipartFile.class.isAssignableFrom(typeClass)
						|| MultipartFile[].class.isAssignableFrom(typeClass)) {
					isFileUpload = true;
					break;
				}
			}
		}

		if(isFileUpload && !isRequireLogin) { // 对于文件上传且不需要登陆态的，不校验csrf，因为它肯定是POST方式
			return true;
		}

		if(!isFileUpload) {
			// jquery及常规js库请求都会带上该头部，不带上该头部的js库不要采用
			String requestedWithHeader = request.getHeader("X-Requested-With");
			if(!"XMLHttpRequest".equals(requestedWithHeader)) {
				return false;
			}
		}

		String referer = request.getHeader("Referer");
		if(referer == null || referer.trim().isEmpty()) {
			return false;
		}

		return true;
	}
}
