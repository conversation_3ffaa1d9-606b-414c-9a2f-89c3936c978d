package com.pugwoo.admin.utils.impl;

import com.pugwoo.admin.utils.IUploadUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

public class UploadUtilsQcloudImpl implements IUploadUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(UploadUtilsQcloudImpl.class);

    private final String region;
    private final String bucketName;
    private final String customDomain;

    private COSClient cosClient = null;

    public UploadUtilsQcloudImpl(String secretId, String secretKey, String region,
                                 String bucketName, String customDomain) {
        this.region = region;
        this.bucketName = bucketName;
        this.customDomain = customDomain;

        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        ClientConfig clientConfig = new ClientConfig(new Region(region));
        cosClient = new COSClient(cred, clientConfig);
    }

    private String transform(String filename) {
        if(!filename.startsWith("/")) {
            filename = "/" + filename;
        }
        return filename;
    }

    private String getUrlRoot() {
        StringBuilder sb = new StringBuilder("https://");
        if(StringUtils.isNotBlank(customDomain)) {
            sb.append(customDomain.trim());
        } else {
            sb.append(bucketName).append(".cos.").append(region).append(".myqcloud.com");
        }
        return sb.toString();
    }

    @Override
    public String upload(File file, String filename) {
        try {
            filename = transform(filename);
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, filename, file);
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            if(StringUtils.isNotBlank(putObjectResult.getETag())) {
                return getUrlRoot() + filename;
            } else {
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("upload file {} fail", file.getPath(), e);
            return null;
        }
    }

    @Override
    public String upload(byte[] data, String filename) {
        try {
            filename = transform(filename);
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(data.length);
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, filename,
                    new ByteArrayInputStream(data), objectMetadata);
            PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
            if(StringUtils.isNotBlank(putObjectResult.getETag())) {
                return getUrlRoot() + filename;
            } else {
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("upload file fail", e);
            return null;
        }
    }

    @Override
    public String upload(InputStream in, String filename) {
        // 腾讯云存储对象上传inputStream时，需要指定流长度，因此这里做性能优化处理
        // 对于128K以下的数据，直接上传，否则写入文件后使用文件上传
        try {
            filename = transform(filename);

            int BUF_SIZE = 128 * 1024;
            byte[] buf = new byte[BUF_SIZE];
            int len = in.read(buf);
            if(len < 0) len = 0; // 处理空文件上传的情况
            if(len < BUF_SIZE) {
                ObjectMetadata objectMetadata = new ObjectMetadata();
                objectMetadata.setContentLength(len);
                PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, filename,
                        new ByteArrayInputStream(buf), objectMetadata);
                PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
                if(StringUtils.isNotBlank(putObjectResult.getETag())) {
                    return getUrlRoot() + filename;
                } else {
                    return null;
                }
            } else {
                File file = File.createTempFile("qcloud-cos", ".tmp");
                FileOutputStream out = new FileOutputStream(file);
                out.write(buf);
                while((len = in.read(buf)) >= 0) {
                    out.write(buf, 0, len);
                }
                out.close();
                String url = this.upload(file, filename);
                try {
                    file.delete();
                } catch (Exception e) { // ignore
                    file.deleteOnExit();
                }
                return url;
            }
        } catch (Exception e) {
            LOGGER.error("upload file fail", e);
            return null;
        } finally {
            IOUtils.close(in);
        }
    }

    @Override
    public boolean delete(String filename) {
        try {
            filename = transform(filename);
            cosClient.deleteObject(bucketName, filename);
            return true;
        } catch (Exception e) {
            LOGGER.error("delete file fail", e);
            return false;
        }
    }

}
