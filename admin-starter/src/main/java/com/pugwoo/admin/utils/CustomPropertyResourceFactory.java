package com.pugwoo.admin.utils;

import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.support.DefaultPropertySourceFactory;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.core.io.support.PropertySourceFactory;
import org.springframework.lang.Nullable;

import java.io.IOException;
import java.util.Optional;

/**
 * <AUTHOR> <br>
 * 支持加载 properties 及 yaml 配置
 */
public class CustomPropertyResourceFactory implements PropertySourceFactory {
    
    @Override
    public PropertySource<?> createPropertySource(@Nullable String name, EncodedResource resource) throws IOException {
        String resourceName = Optional.ofNullable(name).orElse(resource.getResource().getFilename());
        if (resourceName != null && (resourceName.endsWith(".yml") || resourceName.endsWith(".yaml"))) {
            return new YamlPropertySourceLoader().load(resourceName, resource.getResource()).get(0);
        } else {
            return new DefaultPropertySourceFactory().createPropertySource(name, resource);
        }
    }
}
