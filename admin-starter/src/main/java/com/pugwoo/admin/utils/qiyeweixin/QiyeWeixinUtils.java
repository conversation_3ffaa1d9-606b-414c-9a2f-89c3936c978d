package com.pugwoo.admin.utils.qiyeweixin;

import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class QiyeWeixinUtils {

    public static boolean send(String robotKey, String text) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=" + robotKey;

        Map<String, Object> map = new HashMap<>();
        map.put("msgtype", "text");
        map.put("text", MapUtils.of("content", text));

        Browser browser = new Browser();
        try {
            HttpResponse resp = browser.postJson(url, map);
            return resp.getResponseCode() == 200;
        } catch (IOException e) {
            log.error("call qiyeweixin api fail, url:{}, param:{}", url, JSON.toJson(map), e);
            return false;
        }
    }

}
