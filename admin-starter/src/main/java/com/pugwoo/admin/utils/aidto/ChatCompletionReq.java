package com.pugwoo.admin.utils.aidto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChatCompletionReq {

    @Data
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Message {

        /**一般是 user assistant，还有system但不一定支持，例如o1-mini就不支持system*/
        private String role;

        /**
         * 消息内容，可以是字符串（纯文本模式）或者对象数组（支持图片的vision模式）
         * 当只有文本时，content是String类型
         * 当包含图片时，content是List<ContentPart>类型，用于支持vision API
         */
        private Object content;

        public Message(String role, Object content) {
            this.role = role;
            this.content = content;
        }
    }

    /**
     * Vision API的内容部分，支持文本和图片
     */
    @Data
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ContentPart {
        /**内容类型：text 或 image_url*/
        private String type;

        /**文本内容，当type为text时使用*/
        private String text;

        /**图片URL信息，当type为image_url时使用*/
        @JsonProperty("image_url")
        private ImageUrl imageUrl;

        public ContentPart(String type, String text) {
            this.type = type;
            this.text = text;
        }

        public ContentPart(String type, ImageUrl imageUrl) {
            this.type = type;
            this.imageUrl = imageUrl;
        }
    }

    /**
     * 图片URL信息
     */
    @Data
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ImageUrl {
        /**图片URL，支持http/https链接或data:image格式的base64数据*/
        private String url;

        /**图片处理详细程度：low, high, auto，若使用高分辨率模式（detail: "high"），需确保图片短边≤768px，长边≤2000px*/
        private String detail;

        public ImageUrl(String url) {
            this.url = url;
        }

        public ImageUrl(String url, String detail) {
            this.url = url;
            this.detail = detail;
        }
    }

    private String model;

    private boolean stream = false;

    /**
     * 支持多轮对话内容
     */
    private List<Message> messages = new ArrayList<>();

    /**
     * 空值单次输出的最大token数
     */
    @JsonProperty("max_tokens")
    private Integer maxTokens = 4000;

    /**温度，取值范围0-2，用于控制回答的随机性，不填则用系统默认，NextChat之类的工具，默认值是0.5
     *
     * temperature = 0 （确定性输出）
     * 0 < temperature < 1 （平衡性输出）
     * temperature = 1 （较高随机性输出）
     * temperature > 1 （极高随机性输出）
     * */
    private BigDecimal temperature = new BigDecimal("0.5");

}
