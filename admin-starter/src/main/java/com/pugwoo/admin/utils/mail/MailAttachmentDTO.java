package com.pugwoo.admin.utils.mail;

import java.io.InputStream;

/**
 * 邮件附件
 * <AUTHOR>
 */
public class MailAttachmentDTO {

	// 文件名
	private String fileName;

	// 输入流，发送之后将会被关闭
	private InputStream in;

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public InputStream getIn() {
		return in;
	}

	public void setIn(InputStream in) {
		this.in = in;
	}

}
