package com.pugwoo.admin.utils.aidto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ChatCompletionResp {

    @Data
    public static class Message {

        /**一般是assistant，模型的输出*/
        private String role;

        private String content;
    }

    @Data
    public static class Choice {

        private Integer index;

        /**
         * 结束原因：
         * 1) stop 这个是正常结束
         * 2) length 输出长度超出限制
         * 3) content_filter 模型内容被过滤
         * 4) null或空 模型无法生成结果
         */
        @JsonProperty("finish_reason")
        private String finishReason;

        private Message message;
    }

    private String id;

    /**实际的模型*/
    private String model;

    private List<Choice> choices;

    @Data
    public static class Usage {

        @JsonProperty("prompt_tokens")
        private Integer promptTokens;

        @JsonProperty("completion_tokens")
        private Integer completionTokens;

        @JsonProperty("total_tokens")
        private Integer totalTokens;

    }

    private Usage usage;

    @JsonProperty("system_fingerprint")
    private String systemFingerprint;

}
