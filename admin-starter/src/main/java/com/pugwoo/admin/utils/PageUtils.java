package com.pugwoo.admin.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import com.pugwoo.dbhelper.model.PageData;

/**
 * <AUTHOR>
 */
public class PageUtils {

	/**
	 * 转换dbHelper的PageData到前端模块框架的page值
	 * @param pageData
	 * @param transFunc 如果提供，pageData的数据会从T类型转换成Map，否则不转换
	 * @return
	 */
	public static <T> Map<String, Object> trans(PageData<T> pageData,
			Function<T, Map<String, Object>> transFunc) {
		
		Map<String, Object> result = new HashMap<>();
		result.put("total", pageData.getTotal());
		result.put("pageSize", pageData.getPageSize());
		
		if(transFunc != null) {
			List<Map<String, Object>> list = new ArrayList<>();
			for(T t : pageData.getData()) {
				list.add(transFunc.apply(t));
			}
			result.put("data", list);
		} else {
			result.put("data", pageData.getData());
		}
		
		return result;
	}
	
	/**
	 * 转换dbHelper的PageData到前端模块框架的page值
	 * @param pageData
	 * @return
	 */
	public static <T> Map<String, Object> trans(PageData<T> pageData) {
		return trans(pageData, null);
	}
	
}
