package com.pugwoo.admin.utils;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 标记权限，该注解仅对@RestController和@ResponseBody接口有效，对vm渲染url无效
 * - 可以加载Controller上，表示该Controller的所有方法都是该权限。
 * - 可以加载Controller的方法上，表示该方法有该权限。
 * - 加在方法上比加在类上优先级更高。
 *
 * 说明：URL和Permission共存，只有两个任何一个匹配，则认为有权限。
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Permission {

    /**
     * 权限代号。【空(空白)字符串】表示不需要权限
     * @return
     */
    String value();

    /**
     * 权限名称
     * @return
     */
    String name() default "";

}
