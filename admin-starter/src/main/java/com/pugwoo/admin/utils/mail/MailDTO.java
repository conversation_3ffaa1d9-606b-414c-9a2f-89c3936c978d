package com.pugwoo.admin.utils.mail;

import java.util.ArrayList;
import java.util.List;

/**
 * 邮件信息
 * <AUTHOR>
 */
public class MailDTO {

	// 收件人，如果没有提供则发送给自己
	private List<String> recvEmails = new ArrayList<>();

	// 邮件标题
	private String subject;

	// 邮件正文，纯文本形式 (一般text和content二选一)
	private String text;

	// 邮件正文，html格式，utf-8编码 (一般text和content二选一)
	private String content;

	// 邮件附件
	private List<MailAttachmentDTO> attachments = new ArrayList<>();

	public void addRecvMail(String mail) {
		recvEmails.add(mail);
	}

	public List<String> getRecvEmails() {
		return recvEmails;
	}

	public void setRecvEmails(List<String> recvEmails) {
		this.recvEmails = recvEmails;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public List<MailAttachmentDTO> getAttachments() {
		return attachments;
	}

	public void setAttachments(List<MailAttachmentDTO> attachments) {
		this.attachments = attachments;
	}

}
