package com.pugwoo.admin.utils;

import lombok.Data;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

/**
 * 解析权限相关的注解
 */
public class PermissionAnnotation {

    @Data
    public static class PermissionDTO {
        private String permission;
        private String name;

        public PermissionDTO(String permission, String name) {
            this.permission = permission;
            this.name = name;
        }
    }

    /**
     * 查询权限相关注解
     * @param handler
     * @return 返回null表示没有标记任何@NoPermission或@Permission
     *         返回中permission为空字符表示不需要权限
     */
    public static PermissionDTO getPermission(Object handler) {
        if(handler == null) {
            return null;
        }
        if(handler instanceof ResourceHttpRequestHandler) { // 静态资源不处理
            return null;
        }
        if(!(handler instanceof HandlerMethod)) {
            return null; // 不处理非HandlerMethod的
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;

        Permission permission = handlerMethod.getMethodAnnotation(Permission.class);
        if(permission != null) {
            return new PermissionDTO(permission.value(), permission.name());
        }

        NoPermission noPermission = handlerMethod.getMethodAnnotation(NoPermission.class);
        if(noPermission != null) {
             return new PermissionDTO("", noPermission.name());
        }

        permission = handlerMethod.getBeanType().getAnnotation(Permission.class);
        if(permission != null) {
            return new PermissionDTO(permission.value(), permission.name());
        }

        noPermission = handlerMethod.getBeanType().getAnnotation(NoPermission.class);
        if(noPermission != null) {
            return new PermissionDTO("", noPermission.name());
        }

        return null;
    }

}
