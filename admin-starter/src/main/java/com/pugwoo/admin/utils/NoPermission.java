package com.pugwoo.admin.utils;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 不需要权限，任何人都可以访问。
 *  - 可以加载Controller上，表示该Controller的所有方法都是该权限。
 *  - 可以加载Controller的方法上，表示该方法有该权限。
 *  - 加在方法上比加在类上优先级更高。
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface NoPermission {

    /**
     * 链接名称，可选
     * @return
     */
    String name() default "";

}
