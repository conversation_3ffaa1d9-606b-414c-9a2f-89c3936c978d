package com.pugwoo.admin.utils.mail;

import com.sun.mail.util.MailSSLSocketFactory;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.util.ByteArrayDataSource;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Properties;

public class MailUtils {

	// 发送者邮箱
	private String sendEmail;

	// 发送者邮箱密码
	private String password;

	// smtp服务器host
	private String smtpHost;

	// 是否启用ssl，qq邮箱必须启动ssl，更安全
	private boolean enableSSL = true;

	public MailUtils(String sender, String password, String smtpHost, boolean enableSSL) {
		this.sendEmail = sender;
		this.password = password;
		this.smtpHost = smtpHost;
		this.enableSSL = enableSSL;
	}

	public void sendMail(MailDTO mailDTO) throws MessagingException {
		sendMail(mailDTO, false);
	}

	public void sendMail(MailDTO mailDTO, boolean debug) throws MessagingException {
        Properties props = new Properties();
        if(debug) {
        	props.setProperty("mail.debug", "true");
        }

        props.setProperty("mail.smtp.auth", "true");
        props.setProperty("mail.host", smtpHost);
        props.setProperty("mail.transport.protocol", "smtp");

        if(enableSSL) {
			try {
				MailSSLSocketFactory sf = new MailSSLSocketFactory();
	            sf.setTrustAllHosts(true);
	            props.put("mail.smtp.ssl.enable", "true");
	            props.put("mail.smtp.ssl.socketFactory", sf);
			} catch (GeneralSecurityException e) {
				throw new RuntimeException(e);
			}
        }

        Session session = Session.getInstance(props);
        Message msg = new MimeMessage(session);
        msg.setSubject(encodeUTF8(mailDTO.getSubject()));

        boolean isMultipartSet = false;
        Multipart multipart = new MimeMultipart();

        // 设置html正文
        if(mailDTO.getContent() != null) {
        	isMultipartSet = true;

		    BodyPart mdp = new MimeBodyPart();
		    mdp.setContent(mailDTO.getContent(), "text/html;charset=utf-8");
		    multipart.addBodyPart(mdp);
        }

        // 邮件附件
        if(mailDTO.getAttachments() != null && !mailDTO.getAttachments().isEmpty()) {
        	isMultipartSet = true;

        	for(MailAttachmentDTO attachment : mailDTO.getAttachments()) {
    			BodyPart messageBodyPart = new MimeBodyPart();
				try {
					DataSource source = new ByteArrayDataSource(attachment.getIn(), "*/*");
					messageBodyPart.setDataHandler(new DataHandler(source));
					messageBodyPart.setFileName(encodeUTF8(attachment.getFileName()));
					multipart.addBodyPart(messageBodyPart);
				} catch (Exception e) {
					throw new MessagingException("add attachment fail, filename:"
				        + attachment.getFileName(), e);
				}
        	}
        }

        if(isMultipartSet) {
        	msg.setContent(multipart);
        }

        if(mailDTO.getText() != null) {
        	msg.setText(mailDTO.getText());
        }

		// 当text和content都没有设置时，设置一下，不然发送报错
		if (mailDTO.getContent() == null && mailDTO.getText() == null) {
			msg.setText("");
		}

        msg.setFrom(new InternetAddress(sendEmail));

        Transport transport = session.getTransport();
        transport.connect(smtpHost, sendEmail, password);

        List<Address> recvList = new ArrayList<>();
        // 如果接受者没有提供，则发送给自己
        if(mailDTO.getRecvEmails() == null || mailDTO.getRecvEmails().isEmpty()) {
        	recvList.add(new InternetAddress(sendEmail));
        } else {
            for(String mail : mailDTO.getRecvEmails()) {
            	recvList.add(new InternetAddress(mail));
            }
        }

        transport.sendMessage(msg, recvList.toArray(new Address[0]));
        transport.close();

        if(mailDTO.getAttachments() != null) {
        	for(MailAttachmentDTO attachment : mailDTO.getAttachments()) {
        		if(attachment.getIn() != null) {
        			try {
						attachment.getIn().close();
					} catch (IOException e) { // ignore
					}
        		}
        	}
        }

	}

	private static String encodeUTF8(String str) {
		if(str == null) {
			return "";
		}
		try {
			return "=?UTF-8?B?" + Base64.getEncoder().encodeToString(str.getBytes("utf-8"))
				+ "?=";
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		}
	}

}
