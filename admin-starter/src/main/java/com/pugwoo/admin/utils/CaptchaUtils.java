package com.pugwoo.admin.utils;

import org.patchca.color.ColorFactory;
import org.patchca.filter.predefined.*;
import org.patchca.service.ConfigurableCaptchaService;
import org.patchca.utils.encoder.EncoderHelper;
import org.patchca.word.RandomWordFactory;

import java.awt.*;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Random;

public class CaptchaUtils {

    private static final String LETTERS = "23456789abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ"; // 去掉0 O 1 I
    private static final int LENGTH = 4;

    private static Random random = new Random(); // 线程安全

    /**
     * 生成验证码
     * @param letters
     * @param length
     * @param out 图片输出流
     * @return
     */
    public static String genCaptcha(String letters, int length, OutputStream out) throws IOException {
        if (letters == null) {
            letters = LETTERS;
        }
        if (length <= 0) {
            length = LENGTH;
        }

        ConfigurableCaptchaService cs = new ConfigurableCaptchaService(); // 因为可能有线程安全问题，所以每次都new
        cs.setColorFactory(new ColorFactory() {
            @Override
            public Color getColor(int x) {
                int[] c = new int[3];
                int i = random.nextInt(c.length);
                for (int fi = 0; fi < c.length; fi++) {
                    if (fi == i) {
                        c[fi] = random.nextInt(71);
                    } else {
                        c[fi] = random.nextInt(256);
                    }
                }
                return new Color(c[0], c[1], c[2]);
            }
        });

        RandomWordFactory wf = new RandomWordFactory();
        wf.setCharacters(letters);
        wf.setMaxLength(length);
        wf.setMinLength(length);
        cs.setWordFactory(wf);

        switch (random.nextInt(5)) {
            case 0:
                cs.setFilterFactory(new CurvesRippleFilterFactory(cs.getColorFactory()));
                break;
            case 1:
                cs.setFilterFactory(new MarbleRippleFilterFactory());
                break;
            case 2:
                cs.setFilterFactory(new DoubleRippleFilterFactory());
                break;
            case 3:
                cs.setFilterFactory(new WobbleRippleFilterFactory());
                break;
            case 4:
                cs.setFilterFactory(new DiffuseRippleFilterFactory());
                break;
        }

        String code = EncoderHelper.getChallangeAndWriteImage(cs, "png", out);
        out.flush();

        return code;
    }

}
