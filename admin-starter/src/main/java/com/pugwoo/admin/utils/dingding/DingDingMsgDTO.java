package com.pugwoo.admin.utils.dingding;

import lombok.Data;

/**
 * 消息体
 */
@Data
public class DingDingMsgDTO {

    @Data
    public static class Content {
        private String content;
    }

    /**
     * 消息类型: text
     */
    private String msgtype;

    /**消息内容，目前这个是text的*/
    private Content text;

    /**
     * 构造文本消息
     */
    public static DingDingMsgDTO of(String text) {
        DingDingMsgDTO msg = new DingDingMsgDTO();
        msg.setMsgtype("text");
        Content content = new Content();
        content.setContent(text);
        msg.setText(content);
        return msg;
    }

}
