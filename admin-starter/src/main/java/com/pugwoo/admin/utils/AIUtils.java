package com.pugwoo.admin.utils;

import com.pugwoo.admin.utils.aidto.ChatCompletionReq;
import com.pugwoo.admin.utils.aidto.ChatCompletionResp;
import com.pugwoo.admin.utils.aidto.ChatCompletionStreamResp;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.Browser;
import com.pugwoo.wooutils.net.HttpResponse;
import com.pugwoo.wooutils.string.StringTools;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 封装了调用大模型api的工具。
 * 目前仅提供非流式的结果，因为对于工具类来说，没有必要使用流式。
 * <p>
 * 但是特别的是，对于有些模型，它的输出特别慢，例如deepseek-r1，等它思考的过程非常慢，如果不使用流式，非常容易被断，因此这个工具类还是支持将流式转成非流式。
 * <p>
 * ref <a href="https://code.pugwoo.com/learning/openai-sdk-demo">...</a>
 */
@Getter @Setter
@NoArgsConstructor
public class AIUtils {

    /**填写到根url即可，不需要含/v1 */
    private String apiUrl = "https://api.openai.com";
    private String apiKey;

    /**读超时秒数，默认10分钟 */
    private int readTimeoutSecond = 600;

    public AIUtils(String apiUrl, String apiKey) {
        this.apiUrl = apiUrl;
        this.apiKey = apiKey;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Message {
        private String role;
        private String content;
        /**
         * 图片信息，以data:image/png;base64, 开头的base64字符串
         */
        private List<String> imagesBase64;

        public Message(String role, String content) {
            this.role = role;
            this.content = content;
        }
    }

    /**
     * 最简单的单个问题，没有上下文，直接返回字符串的结果
     */
    @SneakyThrows
    public String chat(String model, String question) {
        return chat(model, List.of(new Message("user", question)));
    }

    /**
     * 最简单的单个问题，没有上下文，直接返回字符串的结果
     */
    @SneakyThrows
    public String chat(String model, Message message) {
        return chat(model, List.of(message));
    }

    /**
     * @param message 支持传递系统systemPrompt或历史对话消息
     */
    @SneakyThrows
    public String chat(String model, List<Message> message) {
        Browser browser = createBrowser();
        String url = getApiUrl();
        ChatCompletionReq req = createChatRequest(model, message, false);

        HttpResponse httpResponse = browser.postJson(url, req);
        validateResponse(httpResponse);

        String contentString = httpResponse.getContentString();
        ChatCompletionResp resp = JSON.parse(contentString, ChatCompletionResp.class);
        if (resp == null || resp.getChoices() == null || resp.getChoices().isEmpty()
            || resp.getChoices().getFirst().getMessage() == null) {
            throw new IOException("call api fail, code:" + httpResponse.getResponseCode() + ", msg:" + contentString);
        }
        return resp.getChoices().getFirst().getMessage().getContent();
    }

    /**
     * 对于有些模型，它的输出特别慢，例如deepseek-r1，等它思考的过程非常慢，如果不使用流式，非常容易被断，因此这个工具类还是支持将流式转成非流式
     */
    @SneakyThrows
    public String chatFromStream(String model, String question) {
        return chatFromStream(model, List.of(new Message("user", question)));
    }

    /**
     * 对于有些模型，它的输出特别慢，例如deepseek-r1，等它思考的过程非常慢，如果不使用流式，非常容易被断，因此这个工具类还是支持将流式转成非流式
     */
    @SneakyThrows
    public String chatFromStream(String model, Message message) {
        return chatFromStream(model, List.of(message));
    }

    @SneakyThrows
    public String chatFromStream(String model, List<Message> message) {
        Browser browser = createBrowser();
        String url = getApiUrl();
        ChatCompletionReq req = createChatRequest(model, message, true);

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        HttpResponse httpResponse = browser.postJson(url, req, byteArrayOutputStream);
        validateResponse(httpResponse);

        return processStreamResponse(byteArrayOutputStream.toString(), httpResponse);
    }

    private Browser createBrowser() {
        Browser browser = new Browser();
        browser.setReadTimeoutSeconds(readTimeoutSecond);
        browser.addRequestHeader("Authorization", "Bearer " + apiKey);
        return browser;
    }

    private String getApiUrl() {
        return apiUrl + "/v1/chat/completions";
    }

    private ChatCompletionReq createChatRequest(String model, List<Message> messages, boolean stream) {
        ChatCompletionReq req = new ChatCompletionReq();
        req.setModel(model);
        req.setStream(stream);

        for (Message msg : messages) {
            // 检查是否包含图片数据
            if (ListUtils.isNotEmpty(msg.getImagesBase64())) {
                // 包含图片，使用vision API格式
                req.getMessages().add(createVisionMessage(msg));
            } else {
                // 纯文本，使用传统格式
                req.getMessages().add(new ChatCompletionReq.Message(msg.getRole(), msg.getContent()));
            }
        }
        return req;
    }

    /**
     * 创建支持图片的vision消息格式
     */
    private ChatCompletionReq.Message createVisionMessage(Message msg) {
        List<ChatCompletionReq.ContentPart> contentParts = new ArrayList<>();

        // 添加文本内容（如果有）
        if (StringTools.isNotEmpty(msg.getContent())) {
            contentParts.add(new ChatCompletionReq.ContentPart("text", msg.getContent()));
        }

        // 添加图片内容
        for (String imageBase64 : msg.getImagesBase64()) {
            // 确保base64数据格式正确
            String imageUrl = ensureBase64DataUrl(imageBase64);
            ChatCompletionReq.ImageUrl imageUrlObj = new ChatCompletionReq.ImageUrl(imageUrl);
            contentParts.add(new ChatCompletionReq.ContentPart("image_url", imageUrlObj));
        }

        return new ChatCompletionReq.Message(msg.getRole(), contentParts);
    }

    /**
     * 确保base64数据是正确的data URL格式
     */
    private String ensureBase64DataUrl(String imageBase64) {
        if (imageBase64.startsWith("data:image/")) {
            // 已经是正确的data URL格式
            return imageBase64;
        } else if (imageBase64.startsWith("data:")) {
            // 是data URL但可能不是图片格式，直接返回
            return imageBase64;
        } else {
            // 纯base64数据，添加默认的图片前缀
            return "data:image/png;base64," + imageBase64;
        }
    }

    private void validateResponse(HttpResponse httpResponse) throws IOException {
        if (httpResponse.getResponseCode() != 200) {
            throw new IOException("call api fail, code:" + httpResponse.getResponseCode() +
                ", msg:" + httpResponse.getContentString());
        }
    }

    private String processStreamResponse(String output, HttpResponse httpResponse) throws IOException {
        String[] lines = StringTools.splitLines(output);
        StringBuilder sb = new StringBuilder();
        for (String line : lines) {
            if (line.startsWith("data: ")) {
                line = line.substring("data: ".length());
                if ("[DONE]".equals(line)) {
                    return sb.toString();
                }
                processStreamLine(line, sb, output, httpResponse);
            }
        }
        return sb.toString();
    }

    private void processStreamLine(String line, StringBuilder sb, String output, HttpResponse httpResponse) throws IOException {
        try {
            ChatCompletionStreamResp resp = JSON.parse(line, ChatCompletionStreamResp.class);
            if (resp != null && resp.getChoices() != null) {
                for (ChatCompletionStreamResp.Choice choice : resp.getChoices()) {
                    ChatCompletionStreamResp.Delta delta = choice.getDelta();
                    if (delta != null && StringTools.isNotEmpty(delta.getContent())) {
                        sb.append(delta.getContent());
                    }
                }
            }
        } catch (Exception e) {
            throw new IOException("call api fail, code:" + httpResponse.getResponseCode() + ", msg:" + output, e);
        }
    }

}
