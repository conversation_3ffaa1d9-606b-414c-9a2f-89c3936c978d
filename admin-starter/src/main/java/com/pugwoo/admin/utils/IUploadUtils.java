package com.pugwoo.admin.utils;

import java.io.File;
import java.io.InputStream;

/**
 * 上传工具接口，在Spring中有且只有一个主IUploadUtils对象时，
 * 可以自动注入到AdminUploadController并提供上传服务
 */
public interface IUploadUtils {

	/**
	 * 上传文件，成功返回完整的http url
	 * @param file 上传的本地文件
	 * @param filename 上传后命名的文件名
	 * @return 【特别注意】失败返回null
	 */
	String upload(File file, String filename);

	/**
	 * 上传文件，成功返回完整的http url
	 * @param in 上传的数据的输入流，会自动关闭in
	 * @param filename 上传后命名的文件名
	 * @return 【特别注意】失败返回null
	 */
	String upload(InputStream in, String filename);
	
	/**
	 * 上传文件，成功返回完整的http url
	 * @param data 上传的数据
	 * @param filename 上传后命名的文件名
	 * @return 【特别注意】失败返回null
	 */
	String upload(byte[] data, String filename);
	
	/**
	 * 删除文件
	 * @param filename 上传后命名的文件名
	 * @return
	 */
	boolean delete(String filename);

}
