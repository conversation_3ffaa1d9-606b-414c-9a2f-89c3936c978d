package com.pugwoo.admin;

import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.utils.CustomPropertyResourceFactory;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContextArgumentResolver;
import com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor;
import com.pugwoo.admin.web.interceptor.SentinelInterceptor;
import com.pugwoo.admin.web.weblog.CommonWebLogFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@ComponentScan("com.pugwoo.admin")
@Configuration
@EnableConfigurationProperties(AdminProperties.class)
@PropertySource(value = "classpath:/admin_default_configuration.yaml",
		factory = CustomPropertyResourceFactory.class)
public class AdminConfiguration implements WebMvcConfigurer {

    @Autowired
    private AdminUserLoginInterceptor adminUserLoginInterceptor;
	@Autowired
	private SentinelInterceptor sentinelInterceptor;
	@Autowired
	private AdminUserLoginContextArgumentResolver adminUserLoginContextArgumentResolver;

	@Override
	public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
		resolvers.add(adminUserLoginContextArgumentResolver);
	}

	/**
	 * 静态文件配置
	 */
	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {
		registry.addResourceHandler("/admin_static/**")
                .addResourceLocations("classpath:/admin_static/");
	}

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(sentinelInterceptor).addPathPatterns("/**").excludePathPatterns
				("/admin_static/**");
        registry.addInterceptor(adminUserLoginInterceptor).addPathPatterns("/**").excludePathPatterns
                ("/admin_static/**");
    }

	@Bean
	public CommonWebLogFilter WebLogFilter() {
		return new CommonWebLogFilter();
	}

}
