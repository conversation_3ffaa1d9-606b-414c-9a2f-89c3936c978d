package com.pugwoo.admin.config;

import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.service.AdminLogService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

/**
 * 启动定时任务相关
 */
@Configuration("adminScheduleConfigure")
@EnableScheduling
@Slf4j
public class AdminScheduleConfigure implements SchedulingConfigurer {

    @Autowired
    private AdminProperties adminProperties;
    @Resource
    private AdminLogService adminLogService;

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        ThreadPoolTaskScheduler taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setPoolSize(adminProperties.getSchedulePoolSize());
        taskScheduler.initialize();
        taskScheduler.setErrorHandler(throwable -> {
            log.error("schedule task exception", throwable);
            adminLogService.sendExceptionNotifyToAdmin("定时任务发生异常",
                    "异常:" + throwable.getMessage() + ",exception class:" + throwable.getClass().getName());
        });
        scheduledTaskRegistrar.setTaskScheduler(taskScheduler);
    }
}
