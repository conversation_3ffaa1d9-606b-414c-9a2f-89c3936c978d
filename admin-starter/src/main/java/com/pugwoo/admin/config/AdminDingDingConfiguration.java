package com.pugwoo.admin.config;

import com.pugwoo.admin.config.properties.DingDingProperties;
import com.pugwoo.admin.utils.dingding.DingDingUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(DingDingProperties.class)
public class AdminDingDingConfiguration {

    @Autowired
    private DingDingProperties dingDingProperties;

    @Bean("dingDingUtils")
    public DingDingUtils dingdingUtils() {
        return new DingDingUtils(dingDingProperties);
    }

}
