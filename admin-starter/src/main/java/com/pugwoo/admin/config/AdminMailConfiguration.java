package com.pugwoo.admin.config;

import com.pugwoo.admin.config.properties.MailProperties;
import com.pugwoo.admin.utils.mail.MailUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @date 2018-07-16
 */
@Configuration
@EnableConfigurationProperties(MailProperties.class)
public class AdminMailConfiguration {

    @Autowired
    private MailProperties mailProperties;

    @Bean("mailUtils")
    public MailUtils mailUtils() {
        String sender = mailProperties.getSender();
		if(StringUtils.isBlank(sender)) {
			return null;
		}
		String password = mailProperties.getPassword();
		if(StringUtils.isBlank(password)) {
			return null;
		}
		String smtphost = mailProperties.getSmtphost();
		if(StringUtils.isBlank(smtphost)) {
			return null;
		}
		boolean enableSSL = true;
		String _enableSSL = mailProperties.getEnableSSL();
		if("false".equals(_enableSSL)) {
			enableSSL = false;
		}
		return new MailUtils(sender, password, smtphost, enableSSL);
    }
}