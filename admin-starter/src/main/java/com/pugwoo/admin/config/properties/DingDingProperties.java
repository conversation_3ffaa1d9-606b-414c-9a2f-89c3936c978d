package com.pugwoo.admin.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * 钉钉的相关配置，主要是机器人的token的密钥配置
 */
@Data
@ConfigurationProperties(prefix = "admin.dingding")
public class DingDingProperties {

    @Data
    public static class RobotDTO {
        private String accessToken;
        private String secret;
    }

    private List<RobotDTO> robots;

}
