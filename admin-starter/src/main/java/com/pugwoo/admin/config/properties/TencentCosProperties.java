package com.pugwoo.admin.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @date 2018-07-16
 */
@Data
@ConfigurationProperties(prefix = "admin.txcos")
public class TencentCosProperties {

    /**[必填]腾讯云的secretId，请确保有cos权限*/
    private String secretId;

    /**[必填]腾讯云的secretKey，请确保有cos权限*/
    private String secretKey;

    /**[必填]COS桶的区域，例如ap-guangzhou*/
    private String region;

    /**[必填]桶名称*/
    private String bucketName;

    /**[可选]自定义域名，如果没有提供则用腾讯云给的域名；这个适合于有自己域名的cdn*/
    private String customDomain;

}
