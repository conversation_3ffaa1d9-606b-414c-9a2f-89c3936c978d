package com.pugwoo.admin.config;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.MDC;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;

import java.util.UUID;

@EnableAspectJAutoProxy
@Aspect
@Component
@Slf4j
public class AdminScheduleTraceUuidAspect {

    @Around("@annotation(org.springframework.scheduling.annotation.Scheduled)")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        try {
            String uuid = UUID.randomUUID().toString();
            uuid = uuid.substring(uuid.length() - 12);

            MDC.put("requestUuid", "「" + uuid + "」");
        } catch (Throwable e) {
            log.error("generate trace uuid fail", e);
        }

        Object ret = pjp.proceed();

        try {
            MDC.put("requestUuid", ""); // 清除MDC
        } catch (Throwable e) {
            log.error("clear trace uuid fail", e);
        }

        return ret;
    }

}
