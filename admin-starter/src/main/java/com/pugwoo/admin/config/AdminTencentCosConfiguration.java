package com.pugwoo.admin.config;

import com.pugwoo.admin.config.properties.TencentCosProperties;
import com.pugwoo.admin.utils.impl.UploadUtilsQcloudImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @date 2018-07-16
 */
@Configuration
@EnableConfigurationProperties(TencentCosProperties.class)
public class AdminTencentCosConfiguration {

    @Autowired
    private TencentCosProperties tencentCosProperties;

    @Bean("uploadUtilsQcloudImpl")
    public UploadUtilsQcloudImpl uploadUtilsQcloud() {
        if(tencentCosProperties == null) {
            return null;
        }
        String secretId = tencentCosProperties.getSecretId();
        if(StringUtils.isBlank(secretId)) {
            return null;
        }
        String secretKey = tencentCosProperties.getSecretKey();
        if(StringUtils.isBlank(secretKey)) {
            return null;
        }
        String region = tencentCosProperties.getRegion();
        if(StringUtils.isBlank(region)) {
            return null;
        }
        String bucketName = tencentCosProperties.getBucketName();
        if(StringUtils.isBlank(bucketName)) {
            return null;
        }
        return new UploadUtilsQcloudImpl(secretId, secretKey, region,
                bucketName, tencentCosProperties.getCustomDomain());
    }
}