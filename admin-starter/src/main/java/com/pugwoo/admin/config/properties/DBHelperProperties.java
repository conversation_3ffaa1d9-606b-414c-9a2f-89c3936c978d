package com.pugwoo.admin.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "admin.jdbc")
public class DBHelperProperties {

	/**
	 * [可选]对应于DBHelper的setSlowSqlWarningValve:
	 * 设置SQL执行超时的WARN log的超时时间，单位毫秒，默认为1000毫秒
	 */
	private String slowSqlWarningValve;
	
	/**
	 * [可选]对应于DBHelper的setMaxPageSize:
	 * 设置允许的每页最大的个数，当页数超过允许的最大页数时，设置为最大页数
	 */
	private String maxPageSize;

	/**
	 * [可选]输出sql是否带上请求id(如果有)，默认启用
	 */
	private Boolean withRequestUuid;

	/**
	 * [可选]是否禁止adminDBHelper作为主库，默认不启用，也即adminDBHelper默认是primary bean
	 */
	private Boolean disableAdminDBHelperAsPrimary;

}
