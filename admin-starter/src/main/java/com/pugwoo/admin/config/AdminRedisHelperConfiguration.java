package com.pugwoo.admin.config;

import com.pugwoo.wooutils.cache.HiSpeedCacheAspect;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.RedisSyncAspect;
import com.pugwoo.wooutils.redis.impl.JsonRedisObjectConverter;
import com.pugwoo.wooutils.redis.impl.RedisHelperImpl;
import jakarta.annotation.Resource;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(RedisProperties.class)
public class AdminRedisHelperConfiguration {

	@Resource
	private RedisProperties redisProperties;

	@Bean("adminRedisHelper")
	public RedisHelper adminRedisHelper() {
		RedisHelperImpl redisHelper = new RedisHelperImpl();
		redisHelper.setHost(redisProperties.getHost());
		redisHelper.setPort(redisProperties.getPort());
		redisHelper.setPassword(redisProperties.getPassword());
		redisHelper.setDatabase(redisProperties.getDatabase());
		redisHelper.setRedisObjectConverter(new JsonRedisObjectConverter());

		return redisHelper;
	}

	@Bean
	@ConditionalOnMissingBean
	HiSpeedCacheAspect hiSpeedCacheAspect() {
		return new HiSpeedCacheAspect();
	}

	@Bean
	@ConditionalOnMissingBean
	RedisSyncAspect redisSyncAspect() {
		return new RedisSyncAspect();
	}

}
