package com.pugwoo.admin.config.properties;

import com.pugwoo.admin.enums.AdminSecurityLevelEnum;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "admin")
public class AdminProperties {

    /**[可选]系统名称，中文，展现在页面左上角；默认：后台管理系统*/
    private String systemName = "后台管理系统";

    /**[可选]默认false，不使用CDN的js/css资源*/
    private Boolean useCDNResource = false;

    /**[可选]默认空，忽略登录拦截器的url前缀，多个逗号,隔开*/
    private String ignoreLoginInterceptor;

    /**[可选]默认STRICT，admin安全级别，详见AdminSecurityLevelEnum*/
    private String securityLevel = AdminSecurityLevelEnum.STRICT.getCode();

    /**[可选]慢web阈值，单位毫秒，默认3000*/
    private Integer slowWebTimeMs = 3000;

    /**[可选]定时任务默认线程池大小，默认10*/
    private Integer schedulePoolSize = 10;

    /**[可选]是否开启web请求记录，默认false*/
    private Boolean enableWebLog = false;

    /**[可选]是否打开将web请求记录回调，默认false，当启用时，Spring容器内必须提供一个IWebLogCallback的回调，否则等价于不启用callback*/
    private Boolean enableWebLogCallback = false;

    /**[可选]是否将慢log记录到db，默认true*/
    private Boolean logWebSlowToDB = true;

    /**[可选]是否开启csrf检查，默认false*/
    private Boolean enableCSRFCheck = false;

    /**[可选]将web请求记录写入到db，默认false*/
    private Boolean enableWebLogToDB = false;

    /**[可选]当应用重启时，是否发送告警，默认false*/
    private Boolean notifyWhenRestart = false;

    /**[可选]启用web请求并发数限制，默认true*/
    private Boolean enableRequestConcurrentLimit = true;

    /**[可选]*/
    private Integer maxRequestConcurrentLimit = 50;

    /**[可选]是否真正发送消息（邮件、企业微信、钉钉等），默认true；当设置为false时，不会真正发送消息，但是会log打印出来*/
    private Boolean isActuallySendMsg = true;

    /**[可选]当系统的web请求、定时任务发生异常时，是否告警通知*/
    private Boolean notifyException = true;

    /**[可选]发送异常消息告警一天最多的条数，自然天*/
    private Integer maxExceptionNotifyPerDay = 3;

    /**[可选]是否显示具体的异常信息，默认true，对于敏感的系统，可以设置为false*/
    private Boolean isShowExceptionDetailMsg = true;

    /**[可选]登录态相关配置*/
    private TokenProperties token = new TokenProperties();

    /**[可选]请求流量限制相关配置*/
    private RequestLimitProperties requestLimit = new RequestLimitProperties();

    /**token相关的设置*/
    @Data
    public static class TokenProperties {
        /**[可选]登录态过期时间，秒数，默认为3天*/
        private Integer expireSecond = 259200;
    }

    /**请求相关的流量控制*/
    @Data
    public static class RequestLimitProperties {
        /**[可选]是否开启并发限制，默认开启*/
        private Boolean enableConcurrentLimit = true;
        /**[可选]每个请求method的最大并发数，默认70；<br>
         * 选择70的理由：如果性能强依赖于线程池，那么性能为峰值的35%，此时请提高线程池(默认200个)；<br>
         * 此时，最大允许2个接口占满线程池，共占140个连接池，剩余60个，仍可维持应用正常运作*/
        private Integer maxConcurrent = 70;
    }

}
