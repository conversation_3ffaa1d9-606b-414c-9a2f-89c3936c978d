package com.pugwoo.admin.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @date 2018-07-16
 */
@Data
@ConfigurationProperties(prefix = "admin.mail")
public class MailProperties {

    /**[必填]发送者账号，例如***********/
    private String sender;

    /**[必填]发送者密码*/
    private String password;

    /**[必填]smtp服务器，例如smtp.qq.com*/
    private String smtphost;

    /**[可选]是否启动ssl，默认false*/
    private String enableSSL;

}
