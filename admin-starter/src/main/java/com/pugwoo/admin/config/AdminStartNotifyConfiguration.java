package com.pugwoo.admin.config;

import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;

@Slf4j
@Component
public class AdminStartNotifyConfiguration {

    @Resource
    private AdminProperties adminProperties;
    @Resource
    private AdminNotifyService adminNotifyService;
    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        // 每次重启后，往临时文件里面写入一个标识已经启动过的文件
        File file = new File(System.getProperty("java.io.tmpdir") +
                "/" + adminProperties.getSystemName() + "-application-started.txt");
        if(file.exists()) {
            if (adminProperties.getNotifyWhenRestart() == null || !adminProperties.getNotifyWhenRestart()) {
                return;
            }

            // 已经启动过了，如果配置了告警，则发送
            List<AdminUserDO> users = dbHelper.getAll(AdminUserDO.class, "where disabled=0 and is_admin=1 and email!=''");
            if (ListUtils.isNotEmpty(users)) {
                for (AdminUserDO user : users) {
                    try {
                        adminNotifyService.sendEmail(user.getEmail(),
                                adminProperties.getSystemName() + ":" + " restarted warning!", "");
                    } catch (Exception e) { // 每个用户发送互不影响
                        log.error("send exception msg to {} fail", user.getEmail(), e);
                    }
                }
            }
        } else {
            try {
                boolean ignored = file.createNewFile();
            } catch (Exception ignored) {
            }
        }
    }

}
