package com.pugwoo.admin.config;

import com.pugwoo.admin.config.properties.DBHelperProperties;
import com.pugwoo.admin.web.interceptor.AdminLogDBHelperSlowSqlCallback;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Map;

@Configuration
@EnableConfigurationProperties(DBHelperProperties.class)
public class AdminDBHelperConfiguration  {

	@Autowired
	private ApplicationContext appContext;

	@Getter
    private JdbcTemplate jdbcTemplate;

	@ConditionalOnProperty(prefix = "admin.jdbc", name = "disableAdminDBHelperAsPrimary",
			havingValue = "true", matchIfMissing = false)
	@Bean("adminDBHelper")
	public DBHelper adminDBHelperAsNotPrimary() {
		return _adminDBHelper();
	}

	@ConditionalOnProperty(prefix = "admin.jdbc", name = "disableAdminDBHelperAsPrimary",
			havingValue = "false", matchIfMissing = true)
	@Primary
	@Bean("adminDBHelper")
	public DBHelper adminDBHelper() {
		return _adminDBHelper();
	}

	private DBHelper _adminDBHelper() {
		DBHelperProperties dbHelperProperties = appContext.getBean(DBHelperProperties.class);
		AdminLogDBHelperSlowSqlCallback adminLogDBHelperSlowSqlCallback = appContext.getBean(AdminLogDBHelperSlowSqlCallback.class);

		Map<String, JdbcTemplate> jdbcTpl = appContext.getBeansOfType(JdbcTemplate.class);

		jdbcTemplate = null;

		if(jdbcTpl.isEmpty()) {
			throw new RuntimeException("mysql jdbcTemplate is required");
		}

		if(jdbcTpl.size() == 1) {
			for(Map.Entry<String, JdbcTemplate> e : jdbcTpl.entrySet()) {
				jdbcTemplate = e.getValue();
			}
		} else {
			JdbcTemplate tpl = jdbcTpl.get("adminJdbcTemplate");
			if(tpl == null) {
				throw new RuntimeException("mysql multi jdbcTemplate should have an instant with bean name: adminJdbcTemplate");
			}
			jdbcTemplate = tpl;
		}

        if(jdbcTemplate == null) {
			throw new RuntimeException("mysql jdbcTemplate is required");
		}

		SpringJdbcDBHelper springJdbcDBHelper = new SpringJdbcDBHelper();
        
        if(isNotBlank(dbHelperProperties.getSlowSqlWarningValve())) {
        	try {
        		long timeoutWarningValve = Long.parseLong(dbHelperProperties.getSlowSqlWarningValve().trim());
        		if(timeoutWarningValve > 0) {
        			springJdbcDBHelper.setSlowSqlWarningValve(timeoutWarningValve);
        		}
        	} catch(Exception e) { // ignore parse
        	}
        }
        
        if(isNotBlank(dbHelperProperties.getMaxPageSize())) {
        	try {
        		int maxPageSize = Integer.parseInt(dbHelperProperties.getMaxPageSize().trim());
        		if(maxPageSize > 0) {
        			springJdbcDBHelper.setMaxPageSize(maxPageSize);
        		}
        	} catch (Exception e) { // ignore parse
			}
        }
        
        springJdbcDBHelper.setJdbcTemplate(jdbcTemplate);
        springJdbcDBHelper.setSlowSqlWarningCallback(adminLogDBHelperSlowSqlCallback);
        
        return springJdbcDBHelper;
	}

	private boolean isNotBlank(String str) {
		return str != null && !str.trim().isEmpty();
	}
	
}
