package com.pugwoo.admin.service.impl;

import com.pugwoo.admin.SpringContext;
import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.entity.AdminTaskDO;
import com.pugwoo.admin.entity.AdminTaskLogDO;
import com.pugwoo.admin.enums.AdminTaskStatusEnum;
import com.pugwoo.admin.enums.AdminTaskTriggerTypeEnum;
import com.pugwoo.admin.service.AdminTaskService;
import com.pugwoo.admin.service.TaskAlertService;
import com.pugwoo.admin.utils.ClassUtils;
import com.pugwoo.admin.web.tasklog.TaskLog;
import com.pugwoo.admin.web.tasklog.TaskLogContext;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.string.StringTools;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

@Service
@Slf4j
public class AdminTaskServiceImpl implements AdminTaskService {

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Autowired
    private TaskAlertService taskAlertService;

    private static ExecutorService pool = ThreadPoolUtils.createThreadPool(
            10, 60, 30, "rerun-task");

    @Override
    public AdminTaskDO getById(Integer id) {
        if(id == null) {
           return null;
        }
        return dbHelper.getByKey(AdminTaskDO.class, id);
    }

    @Override
    public PageData<AdminTaskDO> getPage(int page, int pageSize) {
        return dbHelper.getPage(AdminTaskDO.class, page, pageSize);
    }

    @Override
    public ResultBean<Long> insertOrUpdate(AdminTaskDO adminTaskDO) {
        if(adminTaskDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(adminTaskDO);
        return rows > 0 ? ResultBean.ok(adminTaskDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null) {
            return false;
        }

        AdminTaskDO adminTaskDO = new AdminTaskDO();
        adminTaskDO.setId(id);
        return dbHelper.delete(adminTaskDO) > 0;
    }

    // log部分

    @Override
    public AdminTaskLogDO getLogById(Long id) {
        if(id == null) {
            return null;
        }
        return dbHelper.getByKey(AdminTaskLogDO.class, id);
    }

    @Override
    public PageData<AdminTaskLogDO> getLogPage(int page, int pageSize, Long taskId) {
        if (taskId == null) {
            return dbHelper.getPage(AdminTaskLogDO.class, page, pageSize);
        } else {
            return dbHelper.getPage(AdminTaskLogDO.class, page, pageSize,
                "where task_id = ? order by id desc", taskId);
        }
    }

    @Override
    public ResultBean<Long> insertOrUpdateLog(AdminTaskLogDO adminTaskLogDO) {
        if(adminTaskLogDO == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数");
        }
        // TODO 这里需要对新增或修改进行参数检查和条件限制，更推荐独立出更面向服务的新增修改方法

        int rows = dbHelper.insertOrUpdate(adminTaskLogDO);
        return rows > 0 ? ResultBean.ok(adminTaskLogDO.getId()) :
                ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "新增或更新失败");
    }

    @Override
    public boolean deleteLogById(Long id) {
        if(id == null) {
            return false;
        }

        AdminTaskLogDO adminTaskLogDO = new AdminTaskLogDO();
        adminTaskLogDO.setId(id);
        return dbHelper.delete(adminTaskLogDO) > 0;
    }

    @Override
    public ResultBean<Long> rerunTask(Long logId) {
        if (logId == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数logId");
        }

        // 1. 获取原始任务日志记录
        AdminTaskLogDO originalLog = dbHelper.getByKey(AdminTaskLogDO.class, logId);
        if (originalLog == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "任务日志记录不存在");
        }

        // 2. 如果任务禁用，则不允许重跑
        AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, originalLog.getTaskId());
        if (Boolean.TRUE.equals(task.getCtrlDisabled())) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "任务被禁用，不允许重跑");
        }

        try {
            // 3. 创建新的任务日志记录
            AdminTaskLogDO newLog = createNewLogForRerun(originalLog);
            dbHelper.insert(newLog);

            // 4. 执行任务
            pool.submit(() -> executeTask(originalLog, newLog));

            return ResultBean.ok(newLog.getId());
        } catch (Exception e) {
            log.error("重跑任务失败", e);
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "重跑任务失败: " + e.getMessage());
        }
    }

    @Override
    public ResultBean<Long> runTask(Long taskId, String args) {
        if (taskId == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "缺少参数taskId");
        }

        // 1. 获取任务信息
        AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, taskId);
        if (task == null) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "任务不存在");
        }

        // 2. 如果任务禁用，则不允许运行
        if (Boolean.TRUE.equals(task.getCtrlDisabled())) {
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "任务被禁用，不允许运行");
        }

        try {
            // 3. 创建新的任务日志记录
            AdminTaskLogDO newLog = createNewLogForRun(task, args);
            dbHelper.insert(newLog);

            // 4. 执行任务
            pool.submit(() -> executeTaskFromTask(task, newLog, args));

            return ResultBean.ok(newLog.getId());
        } catch (Exception e) {
            log.error("运行任务失败", e);
            return ResultBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "运行任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建重跑的新日志记录
     */
    private AdminTaskLogDO createNewLogForRerun(AdminTaskLogDO originalLog) {
        AdminTaskLogDO newLog = new AdminTaskLogDO();
        newLog.setTaskId(originalLog.getTaskId());
        newLog.setTaskName(originalLog.getTaskName());
        newLog.setTaskCode(originalLog.getTaskCode());
        newLog.setClassName(originalLog.getClassName());
        newLog.setMethodName(originalLog.getMethodName());
        newLog.setArgs(originalLog.getArgs());
        newLog.setCronExpression(originalLog.getCronExpression());
        newLog.setFixRateMs(originalLog.getFixRateMs());
        newLog.setTimeoutSecond(originalLog.getTimeoutSecond());
        newLog.setStatus(AdminTaskStatusEnum.NEW.getCode());
        newLog.setCostMs(0);
        newLog.setErrorMsg("");

        // 设置触发类型为页面触发
        newLog.setTriggerType(AdminTaskTriggerTypeEnum.MANUAL.getCode());

        try {
            newLog.setRunIp(String.join(";", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            newLog.setRunIp("EXCEPTION " + e.getClass().getName());
            log.error("fail to get machine ips", e);
        }

        return newLog;
    }

    /**
     * 创建运行任务的新日志记录
     */
    private AdminTaskLogDO createNewLogForRun(AdminTaskDO task, String args) {
        AdminTaskLogDO newLog = new AdminTaskLogDO();
        newLog.setTaskId(task.getId());
        newLog.setTaskName(task.getTaskName());
        newLog.setTaskCode(task.getTaskCode());
        newLog.setClassName(task.getClassName());
        newLog.setMethodName(task.getMethodName());
        newLog.setArgs(args);
        newLog.setCronExpression(task.getCronExpression());
        newLog.setFixRateMs(task.getFixRateMs());
        newLog.setTimeoutSecond(task.getTimeoutSecond());
        newLog.setStatus(AdminTaskStatusEnum.NEW.getCode());
        newLog.setCostMs(0);
        newLog.setErrorMsg("");

        // 设置触发类型为页面触发
        newLog.setTriggerType(AdminTaskTriggerTypeEnum.MANUAL.getCode());

        try {
            newLog.setRunIp(String.join(";", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            newLog.setRunIp("EXCEPTION " + e.getClass().getName());
            log.error("fail to get machine ips", e);
        }

        return newLog;
    }

    /**
     * 执行任务
     */
    private void executeTask(AdminTaskLogDO originalLog, AdminTaskLogDO newLog) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 设置任务状态为RUNNING
            newLog.setStatus(AdminTaskStatusEnum.RUNNING.getCode());
            dbHelper.update(newLog);
            
            // 设置任务的isRunning状态为true
            AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, originalLog.getTaskId());
            if (task != null) {
                task.setIsRunning(true);
                dbHelper.update(task);
            }

            // 2. 获取类和方法
            Class<?> clazz = Class.forName(originalLog.getClassName());
            Object bean = SpringContext.context.getBean(clazz);

            // 3. 解析方法签名和参数
            String methodSignature = originalLog.getMethodName();
            Method method = findMethodBySignature(clazz, methodSignature);
            if (method == null) {
                throw new RuntimeException("找不到方法: " + methodSignature);
            }

            // 4. 解析参数
            Object[] args = parseArgs(originalLog.getArgs(), method.getParameterTypes());

            // 5. 执行方法
            TaskLogContext.setIsTriggerByManual(true);
            method.invoke(bean, args);
            TaskLogContext.setIsTriggerByManual(false);

            // 6. 更新成功状态
            long costMs = System.currentTimeMillis() - startTime;
            newLog.setStatus(AdminTaskStatusEnum.SUCCESS.getCode());
            newLog.setCostMs((int) costMs);
            dbHelper.update(newLog);

            // 7. 更新任务统计信息
            updateTaskSuccessInfo(originalLog.getTaskId());

        } catch (Exception e) {
            // 更新失败状态
            long costMs = System.currentTimeMillis() - startTime;
            newLog.setStatus(AdminTaskStatusEnum.FAIL.getCode());
            newLog.setCostMs((int) costMs);

            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer, true));
            String errorMsg = writer.toString();
            if (errorMsg.length() > 65000) {
                errorMsg = errorMsg.substring(0, 65000);
            }
            newLog.setErrorMsg(errorMsg);
            dbHelper.update(newLog);

            // 更新任务失败信息
            updateTaskFailInfo(originalLog.getTaskId(), newLog);

            throw new RuntimeException("任务执行失败", e);
        }
    }

    /**
     * 从任务定义执行任务
     */
    private void executeTaskFromTask(AdminTaskDO task, AdminTaskLogDO newLog, String args) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 设置任务状态为RUNNING
            newLog.setStatus(AdminTaskStatusEnum.RUNNING.getCode());
            dbHelper.update(newLog);
            
            // 设置任务的isRunning状态为true
            task.setIsRunning(true);
            dbHelper.update(task);

            // 2. 获取类和方法
            Class<?> clazz = Class.forName(task.getClassName());
            Object bean = SpringContext.context.getBean(clazz);

            // 3. 解析方法签名和参数
            String methodSignature = task.getMethodName();
            Method method = findMethodBySignature(clazz, methodSignature);
            if (method == null) {
                throw new RuntimeException("找不到方法: " + methodSignature);
            }

            // 4. 解析参数
            Object[] methodArgs = parseArgs(args, method.getParameterTypes());

            // 5. 执行方法
            TaskLogContext.setIsTriggerByManual(true);
            method.invoke(bean, methodArgs);
            TaskLogContext.setIsTriggerByManual(false);

            // 6. 更新成功状态
            long costMs = System.currentTimeMillis() - startTime;
            newLog.setStatus(AdminTaskStatusEnum.SUCCESS.getCode());
            newLog.setCostMs((int) costMs);
            dbHelper.update(newLog);

            // 7. 更新任务统计信息
            updateTaskSuccessInfo(task.getId());

        } catch (Exception e) {
            // 更新失败状态
            long costMs = System.currentTimeMillis() - startTime;
            newLog.setStatus(AdminTaskStatusEnum.FAIL.getCode());
            newLog.setCostMs((int) costMs);

            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer, true));
            String errorMsg = writer.toString();
            if (errorMsg.length() > 65000) {
                errorMsg = errorMsg.substring(0, 65000);
            }
            newLog.setErrorMsg(errorMsg);
            dbHelper.update(newLog);

            // 更新任务失败信息
            updateTaskFailInfo(task.getId(), newLog);

            throw new RuntimeException("任务执行失败", e);
        }
    }

    /**
     * 根据方法签名查找方法
     */
    private Method findMethodBySignature(Class<?> clazz, String methodSignature) {
        for (Method method : clazz.getDeclaredMethods()) {
            if (ClassUtils.getMethodSignature(method).equals(methodSignature)) {
                return method;
            }
        }
        return null;
    }

    /**
     * 解析参数
     */
    private Object[] parseArgs(String argsJson, Class<?>[] parameterTypes) {
        if (StringTools.isBlank(argsJson) || "null".equals(argsJson)) {
            return new Object[0];
        }

        try {
            Object[] rawArgs = JSON.parse(argsJson, Object[].class);
            if (rawArgs == null) {
                return new Object[0];
            }

            Object[] typedArgs = new Object[rawArgs.length];
            for (int i = 0; i < rawArgs.length && i < parameterTypes.length; i++) {
                if (rawArgs[i] == null) {
                    typedArgs[i] = null;
                } else {
                    // 简单的类型转换，可能需要根据实际情况扩展
                    typedArgs[i] = convertArgument(rawArgs[i], parameterTypes[i]);
                }
            }
            return typedArgs;
        } catch (Exception e) {
            log.error("解析参数失败: " + argsJson, e);
            return new Object[0];
        }
    }

    /**
     * 参数类型转换
     */
    private Object convertArgument(Object arg, Class<?> targetType) {
        if (arg == null) {
            return null;
        }

        if (targetType.isAssignableFrom(arg.getClass())) {
            return arg;
        }

        // 基本类型转换
        if (targetType == String.class) {
            return arg.toString();
        } else if (targetType == Integer.class || targetType == int.class) {
            return Integer.valueOf(arg.toString());
        } else if (targetType == Long.class || targetType == long.class) {
            return Long.valueOf(arg.toString());
        } else if (targetType == Boolean.class || targetType == boolean.class) {
            return Boolean.valueOf(arg.toString());
        } else if (targetType == Double.class || targetType == double.class) {
            return Double.valueOf(arg.toString());
        } else if (targetType == Float.class || targetType == float.class) {
            return Float.valueOf(arg.toString());
        }

        // 对于复杂对象，尝试JSON转换
        try {
            String json = JSON.toJson(arg);
            return JSON.parse(json, targetType);
        } catch (Exception e) {
            log.warn("参数类型转换失败，使用原始值: " + arg.getClass() + " -> " + targetType, e);
            return arg;
        }
    }

    /**
     * 更新任务成功信息
     */
    private void updateTaskSuccessInfo(Long taskId) {
        if (taskId == null) {
            return;
        }

        try {
            AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, taskId);
            if (task != null) {
                task.setIsLastSuccess(true);
                task.setIsRunning(false); // 任务完成，设置运行状态为false
                task.setLastTime(LocalDateTime.now());
                task.setCountSuccess((task.getCountSuccess() == null ? 0 : task.getCountSuccess()) + 1);
                dbHelper.update(task);
            }
        } catch (Exception e) {
            log.error("更新任务成功信息失败", e);
        }
    }

    /**
     * 更新任务失败信息并发送告警
     */
    private void updateTaskFailInfo(Long taskId, AdminTaskLogDO taskLog) {
        if (taskId == null) {
            return;
        }

        try {
            AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, taskId);
            if (task != null) {
                task.setIsLastSuccess(false);
                task.setIsRunning(false); // 任务完成，设置运行状态为false
                task.setLastErrorTime(LocalDateTime.now());
                task.setCountError((task.getCountError() == null ? 0 : task.getCountError()) + 1);
                dbHelper.update(task);

                // 发送任务失败告警邮件
                if (taskLog != null) {
                    try {
                        taskAlertService.sendTaskFailureAlert(task, taskLog);
                    } catch (Exception alertException) {
                        log.error("发送任务失败告警邮件异常，任务: {}", task.getTaskName(), alertException);
                    }
                }
            }
        } catch (Exception e) {
            log.error("更新任务失败信息失败", e);
        }
    }

    // ========== 从TaskLogAOP移动过来的数据库操作方法 ==========

    @Override
    public AdminTaskLogDO generateNewTaskLog(Method method, Object[] args, TaskLog taskLog,
                                             Scheduled scheduled, AdminTaskStatusEnum status) {
        // 1. 创建AdminTaskLogDO
        String taskCode = getTaskCode(method, taskLog);
        AdminTaskLogDO adminTaskLogDO = buildAdminTaskLogDO(method, args, taskLog, scheduled, taskCode);
        adminTaskLogDO.setStatus(status.getCode());

        // 2. 处理AdminTaskDO的插入或更新
        AdminTaskDO adminTaskDO = createOrUpdateAdminTask(adminTaskLogDO);
        adminTaskLogDO.setTaskId(adminTaskDO.getId()); // 关联任务ID

        dbHelper.insert(adminTaskLogDO);
        return adminTaskLogDO;
    }

    @Override
    public AdminTaskDO createOrUpdateAdminTask(AdminTaskLogDO adminTaskLogDO) {
        // 1. 根据taskCode查询现有记录
        AdminTaskDO existingTask = dbHelper.getOne(AdminTaskDO.class, "where task_code=?", adminTaskLogDO.getTaskCode());

        // 2. 构建新的任务信息
        if (existingTask == null) {
            AdminTaskDO newTaskInfo = new AdminTaskDO();
            newTaskInfo.setTaskCode(adminTaskLogDO.getTaskCode());
            newTaskInfo.setTaskName(adminTaskLogDO.getTaskName());
            newTaskInfo.setClassName(adminTaskLogDO.getClassName());
            newTaskInfo.setMethodName(adminTaskLogDO.getMethodName());
            newTaskInfo.setCronExpression(adminTaskLogDO.getCronExpression());
            newTaskInfo.setFixRateMs(adminTaskLogDO.getFixRateMs());
            newTaskInfo.setTimeoutSecond(adminTaskLogDO.getTimeoutSecond());
            newTaskInfo.setIsLastSuccess(true); // 新任务默认为成功
            newTaskInfo.setLastTime(LocalDateTime.now());
            newTaskInfo.setLastErrorTime(null);
            newTaskInfo.setCountSuccess(0);
            newTaskInfo.setCountError(0);
            dbHelper.insert(newTaskInfo);
            return newTaskInfo;
        }

        return existingTask;
    }

    @Override
    public void updateFailedTask(Future<AdminTaskLogDO> future, String errorMsg, long start) {
        long end = System.currentTimeMillis();
        AdminTaskLogDO one = null;
        try {
            one = future.get();
            one.setCostMs((int)(end - start));
            one.setStatus(AdminTaskStatusEnum.FAIL.getCode());
            one.setErrorMsg(errorMsg);
            dbHelper.update(one);

            // 更新任务统计信息
            if (one.getTaskId() != null) {
                AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, one.getTaskId());
                if (task != null) {
                    updateTaskInfo(task, one);
                    task.setIsLastSuccess(false);
                    task.setIsRunning(false); // 任务完成，设置运行状态为false
                    task.setLastErrorTime(LocalDateTime.now());
                    task.setCountError(task.getCountError() == null ? 1 : task.getCountError() + 1);
                    dbHelper.update(task);

                    // 发送任务失败告警邮件
                    try {
                        taskAlertService.sendTaskFailureAlert(task, one);
                    } catch (Exception alertException) {
                        log.error("发送任务失败告警邮件异常，任务: {}", task.getTaskName(), alertException);
                    }
                }
            }
        } catch (Exception e) {
            log.error("【定时任务执行失败】更新失败:{}", JSON.toJson(one), e);
        }
    }

    @Override
    public void updateSuccessfulTask(Future<AdminTaskLogDO> future, long start) {
        long end = System.currentTimeMillis();
        AdminTaskLogDO one = null;
        try {
            one = future.get();
            one.setCostMs((int)(end - start));
            one.setStatus(AdminTaskStatusEnum.SUCCESS.getCode());
            dbHelper.update(one);

            // 更新任务统计信息
            if (one.getTaskId() != null) {
                AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, one.getTaskId());
                if (task != null) {
                    updateTaskInfo(task, one);
                    task.setIsLastSuccess(true);
                    task.setIsRunning(false); // 任务完成，设置运行状态为false
                    // 不要更新last time，last time认为是任务开始运行时间
                    task.setCountSuccess(task.getCountSuccess() == null ? 1 : task.getCountSuccess() + 1);
                    dbHelper.update(task);
                }
            }
        } catch (Exception e) {
            log.error("【定时任务执行成功】更新失败:{}", JSON.toJson(one), e);
        }
    }

    @Override
    public void updateTaskStatusToRunning(Future<AdminTaskLogDO> future) {
        try {
            AdminTaskLogDO taskLogDO = future.get();
            taskLogDO.setStatus(AdminTaskStatusEnum.RUNNING.getCode());
            // 不要更新last time，last time认为是任务开始运行时间
            dbHelper.update(taskLogDO);

            // 更新任务的isRunning状态为true
            if (taskLogDO.getTaskId() != null) {
                AdminTaskDO task = dbHelper.getByKey(AdminTaskDO.class, taskLogDO.getTaskId());
                if (task != null) {
                    task.setIsRunning(true);
                    dbHelper.update(task);
                }
            }
        } catch (Exception e) {
            log.error("【定时任务状态更新】更新为RUNNING状态失败", e);
        }
    }

    @Override
    public void recordSkippedTask(Method method, Object[] args, TaskLog taskLog,
                                  Scheduled scheduled, AdminTaskDO existingTask) {
        try {
            // 创建AdminTaskLogDO
            AdminTaskLogDO adminTaskLogDO = buildAdminTaskLogDO(method, args, taskLog, scheduled, existingTask.getTaskCode());
            adminTaskLogDO.setStatus(AdminTaskStatusEnum.SKIPPED.getCode());
            adminTaskLogDO.setErrorMsg("任务已被禁用，不需要执行");
            adminTaskLogDO.setTaskId(existingTask.getId());

            dbHelper.insert(adminTaskLogDO);

            updateTaskInfo(existingTask, adminTaskLogDO);
            dbHelper.update(existingTask);

        } catch (Exception e) {
            log.error("【定时任务跳过执行】记录失败", e);
        }
    }

    @Override
    public AdminTaskDO getByTaskCode(String taskCode) {
        return dbHelper.getOne(AdminTaskDO.class, "where task_code=?", taskCode);
    }

    // ========== 辅助方法 ==========

    /**
     * 获取任务代码
     */
    private String getTaskCode(Method method, TaskLog taskLog) {
        if (taskLog != null && StringTools.isNotBlank(taskLog.taskCode())) {
            return taskLog.taskCode();
        } else {
            return method.getDeclaringClass().getName() + "." + ClassUtils.getMethodSignature(method);
        }
    }

    /**
     * 构建AdminTaskLogDO的公共方法
     */
    private AdminTaskLogDO buildAdminTaskLogDO(Method method, Object[] args, TaskLog taskLog,
                                               Scheduled scheduled, String taskCode) {
        AdminTaskLogDO adminTaskLogDO = new AdminTaskLogDO();
        adminTaskLogDO.setTaskName(taskLog == null ? "" : taskLog.taskName());
        adminTaskLogDO.setClassName(method.getDeclaringClass().getName());
        adminTaskLogDO.setMethodName(ClassUtils.getMethodSignature(method));
        adminTaskLogDO.setTaskCode(taskCode);
        adminTaskLogDO.setTriggerType(AdminTaskTriggerTypeEnum.SCHEDULED.getCode());

        if (scheduled != null) {
            adminTaskLogDO.setCronExpression(scheduled.cron());
            if (scheduled.fixedRate() != 0) {
                adminTaskLogDO.setFixRateMs((int) scheduled.fixedRate());
            } else if (scheduled.fixedDelay() != 0) {
                adminTaskLogDO.setFixRateMs((int) scheduled.fixedDelay());
            }
        }

        try {
            adminTaskLogDO.setRunIp(String.join(";", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            adminTaskLogDO.setRunIp("EXCEPTION " + e.getClass().getName());
            log.error("fail to get machine ips", e);
        }

        Integer timeout = taskLog == null ? -1 : taskLog.timeoutSeconds();
        adminTaskLogDO.setTimeoutSecond(Objects.equals(timeout, -1) ? null : timeout);
        adminTaskLogDO.setArgs(JSON.toJson(args));
        adminTaskLogDO.setCostMs(0);

        return adminTaskLogDO;
    }

    /**
     * 更新任务信息
     */
    private void updateTaskInfo(AdminTaskDO existingTask, AdminTaskLogDO adminTaskLogDO) {
        existingTask.setTaskName(adminTaskLogDO.getTaskName());
        existingTask.setClassName(adminTaskLogDO.getClassName());
        existingTask.setMethodName(adminTaskLogDO.getMethodName());
        existingTask.setCronExpression(adminTaskLogDO.getCronExpression());
        existingTask.setFixRateMs(adminTaskLogDO.getFixRateMs());
        existingTask.setTimeoutSecond(adminTaskLogDO.getTimeoutSecond());
    }

}