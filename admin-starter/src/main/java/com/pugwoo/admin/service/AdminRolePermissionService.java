package com.pugwoo.admin.service;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminRoleDO;
import com.pugwoo.admin.entity.AdminUrlDO;
import com.pugwoo.admin.entity.AdminUrlIgnoreDO;
import com.pugwoo.admin.vo.*;
import com.pugwoo.dbhelper.model.PageData;

import java.util.List;

/**
 * Role-Url 操作
 *  user_role
 *  role
 *  role_url
 *  url
 *  url_ignore
 */
public interface AdminRolePermissionService {

//==========user_role
    /**
     * 获取属于roleId角色的所有用户<br/>
     *      包括用户信息<br/>
     * @param roleId    角色id
     * @return  user_role - user List
     */
    List<UserRoleRelatedUserVO> getUserListByRoleId(Long roleId);

    /**
     * 获取属于user_id用户的所有角色<br/>
     *      包括角色信息<br/>
     * @param userId    用户id
     * @return  user_role - role List
     */
    List<UserRoleRelatedRoleVO> getRoleListByUserId(Long userId);

    /**
     * 更新 角色-用户 列表
     * @param roleId    角色Id
     * @param userIds   角色Id对应的新的userIds
     * @return  true 成功<br/>false 失败
     */
    boolean saveByRoleIdAndUserIds(Long roleId, List<Long> userIds);

    /**
     * 更新 角色 - 用户 列表
     * @param userId    用户id
     * @param roleIds   用户id对应的新的角色列表
     * @return true 成功<br/>false 失败
     */
    boolean saveByUserIdAndRoleIds(Long userId, List<Long> roleIds);

    /**
     * 根据 user_id 删除记录
     * @param userId    用户id
     * @return  删除条数
     */
    int deleteUserRoleByUserId(Long userId);

//==========role
    /**
     * 获取角色分页记录
     * @param page 第几页
     * @param pageSize 每页记录条数
     * @param name 角色名称
     * @param roleGroup 角色分组
     * @return 角色分页数据
     */
    PageData<AdminRoleVO> getRolePage(int page, int pageSize, String name, String roleGroup);

    /**
     * 根据id获取角色信息
     * @param roleId 角色id
     * @return  角色信息
     */
    AdminRoleDO getRoleById(Long roleId);

    /**
     * 获取全部角色列表
     * @return  全部角色列表
     */
    List<AdminRoleDO> getAllRoles();

    /**
     * 插入或更新角色
     * 		如果key为null，则插入
     * 		如果key不为null，则更新
     * @param adminRoleDO 角色信息对象
     * @return 插入数量
     */
    int insertOrUpdateRole(AdminRoleDO adminRoleDO);

    /**
     * 根据id删除角色<br/>
     *      删除role表， id = roleId<br/>
     *      删除role_url表，role_id = roleId<br/>
     *      删除user_role表，role_id = roleId
     * @param roleId    角色id
     * @return          被删除的行数总和
     */
    int deleteRoleById(Long roleId);

    /**
     * 校验角色name是否已经存在
     * @param adminRoleDO 如果id存在，校验除该name以外的是否存在
     * @param name  角色名称
     * @return  true 存在 <br/>false 不存在
     */
    boolean isExistRoleName(AdminRoleDO adminRoleDO, String name);

    /**
     * 校验角色code是否已经存在
     * @param adminRoleDO 如果id存在，校验除该code以外的是否存在
     * @param code  角色code
     * @return  true 存在 <br/>false 不存在
     */
    boolean isExistRoleCode(AdminRoleDO adminRoleDO, String code);

//==========role_url
    /**
     * 更新url对应的角色列表<br/>
     *      某一个节点 url_id 对于原来的数据，<br/>
     *          如果roleList中有新增的角色，则需要对该url及其所有目录父节点都增加该角色,父节点已有该角色的除外<br/>
     *          如果roleList中有减少的角色，则需要对该url及其所有子节点都减少该角色<br/>
     * @param urlId     需要更新的url_id
     * @param roleIds   新的角色 Id List
     * @return  true 成功<br/>false 失败<br/>
     */
    boolean saveByUrlIdAndRoleIds(Long urlId, List<Long> roleIds);

    /**
     * 更新roleId对应的url列表<br/>
     *      url列表的所有url都需获取其父节点，直至根部<br/>
     * @param roleId    需要更新的role_id
     * @param urlIds    新的url Id list
     * @return  true 成功<br/>false 失败<br/>
     */
    boolean saveByRoleIdAndUrlIds(Long roleId, List<Long> urlIds);

    /**
     * 根据UrlId获取对应的角色(含角色表的详细信息)
     * @param urlId url id
     * @return  role_url - role
     */
    List<RoleUrlRelatedRoleVO> getRoleByUrlId(Long urlId);

    /**
     * 根据roleId获取对应的url(含url的详细信息)
     * @param roleId    role id
     * @return  role_url - url
     */
    List<RoleUrlRelatedUrlVO> getUrlByRoleId(Long roleId);

//==========url
    /**
     * 根据id查询Url信息
     * @param id url id
     * @return url信息
     */
    AdminUrlDO getUrlById(Long id);

    /**
     * 查询所有的urls列表
     */
    List<AdminUrlTreeVO> getAllUrls();

    /**
     * 获得指定用户的全部有权限的url，去重，不确定顺序。其中目录不设置权限，故所有目录都会返回。
     * @param userId	用户id
     * @return	用户拥有的url(去重)
     */
    UserActuallyPermissionDTO getAllUrlsByUserId(Long userId);

    /**
     * 更新url信息
     * @param adminUrlDO url对象
     * @return true 成功
     */
    boolean updateUrl(AdminUrlDO adminUrlDO);

    /**
     * 根据所有的权限url，生成树形菜单
     * @param urls url对象List
     * @return 树形菜单
     */
    List<MenuDTO> genMenu(List<AdminUrlDTO> urls);

    /**
     * 获得所有的url并组合成树形结构
     */
    List<AdminUrlTreeVO> genUrlTree(List<AdminUrlTreeVO> urls);

    /**
     * 检查树形结构:
     * 1. 所有节点必须有类型
     * 2. MENU类型的下面不允许有FOLDER或MENU类型
     * @param treeData 树形结构数据
     * @return 检查错误信息，没有错误为空
     */
    List<WebJsonBean> checkUrlTree(List<AdminUrlTreeVO> treeData);

    /**
     * 保存树形结构
     * @param treeData 树形数据结构
     * @return  true 成功<br/>false 失败<br/>
     */
    boolean saveUrlTree(List<AdminUrlTreeVO> treeData);

//==========url_ignore
	/**
	 * 忽略自动扫描的url
	 * @param url   url
	 * @return  true 成功<br/>false 失败<br/>
	 */
	boolean ignoreUrl(String url);

    /**
     * 获取所有的忽略url
     * @return 忽略的url List
     */
	List<AdminUrlIgnoreDO> getAllIgnoreUrls();
}
