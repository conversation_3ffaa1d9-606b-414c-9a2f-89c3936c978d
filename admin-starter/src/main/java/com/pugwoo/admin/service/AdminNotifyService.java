package com.pugwoo.admin.service;

/**
 * 发送通知服务，支持：
 * 1）邮件发送，需要配置admin.mail，一共3项子配置。
 * 2）钉钉发送，约定格式为*******************，需要配置admin.dingding，至少2项子配置
 * 3）企业微信发送，约定格式为**************，无需配置。
 */
public interface AdminNotifyService {

    /**
     * 发送邮件，也支持发送钉钉、企业微信
     * @param toEmails 多个邮箱地址可用逗号或分号隔开
     * @param title 内容
     * @param content 正文，正文如果没有请留空，内部会自动处理
     * @return 发送成功返回true
     */
    boolean sendEmail(String toEmails, String title, String content);

}
