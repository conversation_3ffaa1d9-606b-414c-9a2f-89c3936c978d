package com.pugwoo.admin.service.impl;

import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.admin.utils.dingding.DingDingUtils;
import com.pugwoo.admin.utils.mail.MailDTO;
import com.pugwoo.admin.utils.mail.MailUtils;
import com.pugwoo.admin.utils.qiyeweixin.QiyeWeixinUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class AdminNotifyServiceImpl implements AdminNotifyService {

    @Autowired(required = false)
    private MailUtils mailUtils;
    @Autowired(required = false)
    private DingDingUtils dingDingUtils;

    @Autowired
    private AdminProperties adminProperties;

    @Override
    public boolean sendEmail(String toEmails, String title, String content) {
        if (StringTools.isBlank(toEmails)) {
            log.error("sendEmail error, toEmails is blank");
            return false;
        }

        List<String> to = parseEmail(toEmails);

        List<String> emails = ListUtils.filter(to, o -> !o.endsWith("@dd.com") && !o.endsWith("@qywx.com"));
        List<String> dingDings = ListUtils.filter(to, o -> o.endsWith("@dd.com"));
        List<String> qiyeWeixin = ListUtils.filter(to, o -> o.endsWith("@qywx.com"));

        boolean isSendSuccess = true;
        // 发送邮件
        if (ListUtils.isNotEmpty(emails)) {
            MailDTO mail = new MailDTO();
            mail.setSubject(title);
            mail.setText(StringTools.isBlank(content) ? "[正文为空]" : content);
            mail.setRecvEmails(emails);
            try {
                if (adminProperties.getIsActuallySendMsg()) {
                    if (mailUtils == null) {
                        log.error("mailUtils is not confi");
                        isSendSuccess = false;
                    } else {
                        mailUtils.sendMail(mail);
                    }
                } else {
                    log.info("admin.isActuallySendMsg=false sendEmail, mail:{}", JSON.toJson(mail));
                }
            } catch (MessagingException e) {
                log.error("sendEmail error, msg:{}", JSON.toJson(mail), e);
                isSendSuccess = false;
            }
        }

        // 发送钉钉
        if (ListUtils.isNotEmpty(dingDings)) {
            // 处理掉@dd.com
            dingDings = ListUtils.transform(dingDings, o -> o.substring(0, o.length() - "@dd.com".length()));
            String text = StringTools.isBlank(content) ? title : title + " >>> " + content;
            for (String accessToken : dingDings) {
                try {
                    if (adminProperties.getIsActuallySendMsg()) {
                        boolean success = dingDingUtils.send(accessToken, text);
                        if (!success) {
                            isSendSuccess = false;
                            log.error("send dingding to {} fail, text:{}", accessToken, text);
                        }
                    } else {
                        log.info("admin.isActuallySendMsg=false sendDingding, accessToken:{}, text:{}", accessToken, text);
                    }
                } catch (Exception e) {
                    log.error("send dingding to {} fail, text:{}", accessToken, text, e);
                    isSendSuccess = false;
                }
            }
        }

        // 发送企业微信
        if (ListUtils.isNotEmpty(qiyeWeixin)) {
            // 处理掉@qywx.com
            qiyeWeixin = ListUtils.transform(qiyeWeixin, o -> o.substring(0, o.length() - "@qywx.com".length()));
            String text = StringTools.isBlank(content) ? title : title + " >>> " + content;
            for (String robotKey : qiyeWeixin) {
                try {
                    if (adminProperties.getIsActuallySendMsg()) {
                        boolean success = QiyeWeixinUtils.send(robotKey, text);
                        if (!success) {
                            isSendSuccess = false;
                            log.error("send qiyeweixin to {} fail, text:{}", robotKey, text);
                        }
                    } else {
                        log.info("admin.isActuallySendMsg=false send qiyeweixin to {}, text:{}", robotKey, text);
                    }
                } catch (Exception e) {
                    log.error("send qiyeweixin to {} fail, text:{}", robotKey, text, e);
                    isSendSuccess = false;
                }
            }
        }

        return isSendSuccess;
    }

    private static List<String> parseEmail(String emails) {
        String[] email = emails.split("[;,]"); // 支持逗号或分号分隔
        return ListUtils.filter(Arrays.asList(email), StringTools::isNotBlank);
    }
}
