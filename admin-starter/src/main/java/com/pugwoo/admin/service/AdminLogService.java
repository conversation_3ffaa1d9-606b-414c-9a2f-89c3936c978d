package com.pugwoo.admin.service;

import com.pugwoo.admin.entity.AdminLogExceptionDO;
import com.pugwoo.admin.entity.AdminLogSlowSqlDO;
import com.pugwoo.admin.entity.AdminLogSlowWebDO;
import com.pugwoo.admin.enums.OperateTypeEnum;
import com.pugwoo.admin.enums.SecurityLevelEnum;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContext;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.web.method.HandlerMethod;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

public interface AdminLogService {

	/**
	 * 记录用户操作
	 * @param remark 操作说明
	 * @param opType 操作类型
	 * @param secLevel 安全等级
	 * @return  true 成功 <br/> false 失败
	 */
	boolean log(String remark, OperateTypeEnum opType,
                SecurityLevelEnum secLevel);

    /**
     * 该接口仅提供给LoginController使用，其它方法不要调用这个方法
     * @param remark    操作说明
     * @param opType    操作类型
     * @param secLevel  安全等级
     * @param adminUserLoginContext 用户登录信息
     * @return  true 成功 <br/> false 失败
     */
	boolean log(String remark, OperateTypeEnum opType,
                SecurityLevelEnum secLevel, AdminUserLoginContext adminUserLoginContext);

//========exception start========

	/**
	 * 根据id获取一条log记录
	 * @param logId id
	 * @return  log记录
	 */
	AdminLogExceptionDO getExceptionById(Long logId);

	/**
	 * 分页查询记录,按id逆序
	 * @param page          页数
	 * @param pageSize      每页记录数
	 * @param onlyUnRead    是否只查看未读
     * @param dateRange     起始-结束日期
	 * @return  每页记录
	 */
	PageData<AdminLogExceptionDO> getExceptionForPage(Integer page, Integer pageSize, Boolean onlyUnRead, List<Date> dateRange);

	/**
	 * 提供给异常处理器直接使用，这个方法不会因为数据库异常而抛出异常，以免循环异常
	 */
	void addException(Exception ex, HttpServletRequest request, HandlerMethod handlerMethod);

	/**
	 * 设置为已读
	 * @param adminLogExceptionDO 记录表DO
	 * @return      操作数
	 */
	boolean setExceptionRead(AdminLogExceptionDO adminLogExceptionDO);

    /**
     * 设置所有记录为已读
     * @return  true 成功 <br/> false 失败
     */
	boolean setExceptionReadAll();

	/**
	 * 发送异常消息通知给admin管理员
	 * @param title 消息标题
	 * @param content 消息内容
	 */
	void sendExceptionNotifyToAdmin(String title, String content);

//========exception end==========

//========slow web start========

	/**
	 * 添加一条slowWeb记录，异步线程插入
	 * @param slowWebDO 记录表DO
	 * @return 插入数量
	 */
	void addSlowWeb(AdminLogSlowWebDO slowWebDO);

	/**
	 * 分页查询记录,按id逆序
	 * @param page          页数
	 * @param pageSize      每页记录数
	 * @param onlyUnRead    是否只查看未读
     * @param dateRange     起始-结束日期
	 * @return  每页记录
	 */
	PageData<AdminLogSlowWebDO> getSlowWebForPage(Integer page, Integer pageSize, Boolean onlyUnRead, List<Date> dateRange);


	/**
	 * 根据id获取一条log记录
	 * @param logId id
	 * @return  log记录
	 */
	AdminLogSlowWebDO getSlowWebById(Long logId);

	/**
	 * 设置为已读
	 * @param  slowWebDO 记录表DO
	 * @return      操作数
	 */
	boolean setSlowWebRead(AdminLogSlowWebDO slowWebDO);

    /**
     * 设置所有记录为已读
     * @return true 成功 <br/> false 失败
     */
    boolean setSlowWebReadAll();

//========slow web end==========

//========slow sql start========

	/**
	 * 添加一条slowSQL记录
	 * @param slowSqlDO 记录表DO
	 * @return 插入数量
	 */
	Integer addSlowSql(AdminLogSlowSqlDO slowSqlDO);

	/**
	 * 分页查询记录,按id逆序
	 * @param page          页数
	 * @param pageSize      每页记录数
	 * @param onlyUnRead    是否只查看未读
     * @param dateRange     起始-结束日期
	 * @return  每页记录
	 */
	PageData<AdminLogSlowSqlDO> getSlowSqlForPage(Integer page, Integer pageSize, Boolean onlyUnRead, List<Date> dateRange);


	/**
	 * 根据id获取一条log记录
	 * @param logId id
	 * @return  log记录
	 */
	AdminLogSlowSqlDO getSlowSqlById(Long logId);

	/**
	 * 设置为已读
	 * @param  slowSqlDO 记录表DO
	 * @return      操作数
	 */
	boolean setSlowSqlRead(AdminLogSlowSqlDO slowSqlDO);

    /**
     * 设置所有记录为已读
     * @return true 成功 <br/> false 失败
     */
    boolean setSlowSqlReadAll();

//========slow sql end==========

}
