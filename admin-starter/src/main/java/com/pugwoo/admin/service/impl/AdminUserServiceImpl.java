package com.pugwoo.admin.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.RedisKeyNamespace;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.entity.AdminDepartmentDO;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.service.AdminRolePermissionService;
import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.vo.*;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContext;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.tree.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2018-04-25
 */
@Slf4j
@Service
public class AdminUserServiceImpl implements AdminUserService {

    @Autowired @Qualifier("adminDBHelper")
    private DBHelper dbHelper;
    @Autowired @Qualifier("adminRedisHelper")
    private RedisHelper redisHelper;
    @Autowired
    private AdminRolePermissionService rolePermissionService;

    @Autowired
    private AdminProperties adminProperties;

    @Override
    public boolean isExistUsername(String username) {
    	if(username == null) {return false;}
    	return dbHelper.isExist(AdminUserDO.class, "where user_name=?", username);
    }
    
    @Override
    public List<AdminUserDO> getAllUser() {
        return dbHelper.getAll(AdminUserDO.class);
    }
    
    @Override
    public List<AdminUserDO> queryUser(List<Long> ids, String keyword, int limit) {
    	StringBuilder where = new StringBuilder("where 1=1");
    	List<Object> params = new ArrayList<>();
    	if(ids != null && !ids.isEmpty()) {
    		where.append(" and id in (?)");
    		params.add(ids);
    	} else if(StringUtils.isNotBlank(keyword)) {
    		where.append(" and (user_name like ? or real_name like ? or phone like ?)");
    		params.add("%" + keyword + "%");
    		params.add("%" + keyword + "%");
    		params.add("%" + keyword + "%");
    	}
    	where.append(" limit " + limit);
    	return dbHelper.getAll(AdminUserDO.class, where.toString(), params.toArray());
    }

    @Override
    @Synchronized(keyScript = "args[0].userName", customExceptionMessage = "当前有同名用户正在创建或编辑，请稍后重试")
    public boolean addOrUpdate(AdminUserDO adminUserDO) {
    	if(adminUserDO == null) {return false;}
    	return dbHelper.insertOrUpdate(adminUserDO) > 0;
    }

    @Override
    @Transactional
    public boolean deleteUserById(Long userId) {
        if (userId == null) {return false;}
        int rowDeleteUser = dbHelper.delete(AdminUserDO.class, "where id=?", userId);
        rolePermissionService.deleteUserRoleByUserId(userId);
        return rowDeleteUser > 0;
    }

    @Override
    public AdminUserDO getUserById(Long userId) {
        if (userId == null) {return null;}
        return dbHelper.getByKey(AdminUserDO.class, userId);
    }

    @Override
    public AdminUserDO getUserByUsername(String username) {
        if (StringUtils.isBlank(username)) {return null;}
        return dbHelper.getOne(AdminUserDO.class, "where user_name=?", username);
    }

    @Override
    public AdminUserLoginContext buildAdminUserLoginContext(AdminUserDO user) {
        AdminUserLoginContext loginContext = new AdminUserLoginContext();
        loginContext.setUserId(user.getId());
        loginContext.setUserName(user.getUserName());
        loginContext.setAdmin(user.getIsAdmin() != null && user.getIsAdmin());

        UserActuallyPermissionDTO permission = rolePermissionService.getAllUrlsByUserId(user.getId());

        List<AdminUrlDTO> urls = ListUtils.transform(permission.getUrls(), o -> AdminUrlDTO.from(o));
        loginContext.setUrls(urls);

        return loginContext;
    }

    @HiSpeedCache(keyScript = "args[0]", expireSecond = 60)
    @Override
    public void renewalToken(String token) {
        String key = RedisKeyNamespace.ADMIN_LOGIN_TOKEN + token;
        Integer tokenExpireTime = adminProperties.getToken().getExpireSecond();
        redisHelper.setExpire(key, tokenExpireTime);
    }

    private Map<String, AdminUserLoginContext> getLoginContextMap() {
        String pattern = RedisKeyNamespace.ADMIN_LOGIN_TOKEN + "*";
        Set<String> keys = redisHelper.execute(jedis -> {
            try {
                return jedis.keys(pattern);
            } catch (Exception e) {
                log.error("operate jedis KEYS error, pattern:{}", pattern, e);
                return null;
            }
        });
        if(keys == null) {
            return new HashMap<>();
        }

        Map<String, AdminUserLoginContext> result = new HashMap<>();
        for (String key : keys) {
            AdminUserLoginContext userLoginContext = redisHelper.getObject(key, AdminUserLoginContext.class);
            if (userLoginContext != null) {
                result.put(key, userLoginContext);
            }
        }

        return result;
    }

    @Override
    public boolean refreshAllUserUrls() {
        Map<String, AdminUserLoginContext> redisKeyObject = getLoginContextMap();
        Map<Long, UserActuallyPermissionDTO> localCache = new HashMap<>(); // 缓存，减少数据库操作
        
        for (Map.Entry<String, AdminUserLoginContext> entry : redisKeyObject.entrySet()) {
            AdminUserLoginContext userLoginContext = entry.getValue();
            Long userId = userLoginContext.getUserId();

            UserActuallyPermissionDTO permission;
            if (localCache.containsKey(userId)) {
                permission = localCache.get(userId);
            } else {
                permission = rolePermissionService.getAllUrlsByUserId(userId);
                localCache.put(userId, permission);
            }

            List<AdminUrlDTO> urls = ListUtils.transform(permission.getUrls(), o -> AdminUrlDTO.from(o));

            userLoginContext.setUrls(urls);
            long expireTime = redisHelper.getExpireSecond(entry.getKey());
            redisHelper.setObject(entry.getKey(), (int)expireTime, userLoginContext);
        }
        
    	return true;
    }
    
    @Override
    public boolean removeUserLoginContext(Long userId) {
    	if(userId == null) {return false;}
        Map<String, AdminUserLoginContext> redisKeyObject = getLoginContextMap();;
        for (Map.Entry<String, AdminUserLoginContext> entry : redisKeyObject.entrySet()) {
             if(userId.equals(entry.getValue().getUserId())) {
            	 redisHelper.remove(entry.getKey());
             }
        }
    	return true;
    }

    @Override
    public boolean updateUserLoginContext(AdminUserDO adminUserDO) {
        if (adminUserDO == null || adminUserDO.getId() == null) { return false; }
        Map<String, AdminUserLoginContext> redisKeyObject = getLoginContextMap();
        for (Map.Entry<String, AdminUserLoginContext> entry : redisKeyObject.entrySet()) {
            AdminUserLoginContext userLoginContext = entry.getValue();
            boolean needUpdate = false;
            if(Objects.equals(userLoginContext.getUserId(), adminUserDO.getId())) {
                if (!Objects.equals(userLoginContext.isAdmin(), adminUserDO.getIsAdmin())) {
                    userLoginContext.setAdmin(adminUserDO.getIsAdmin());
                    needUpdate = true;
                }
                if (Objects.equals(userLoginContext.getUserName(), adminUserDO.getUserName())) {
                    userLoginContext.setUserName(adminUserDO.getUserName());
                    needUpdate = true;
                }
                if (needUpdate) {
                    long expireTime = redisHelper.getExpireSecond(entry.getKey());
                    redisHelper.setObject(entry.getKey(), (int)expireTime, userLoginContext);
                }
            }
        }
        return true;
    }

    @Override
    public List<AdminDepartmentTreeVO> getDepartmentTree() {
        return TreeUtils.genTreeNode(dbHelper.getAll(AdminDepartmentTreeVO.class));
    }

    @Override
    public AdminDepartmentDO getDepartmentById(Long id) {
        if (id == null) {return null;}
        return dbHelper.getByKey(AdminDepartmentDO.class, id);
    }

    @Override
    public boolean updateDepartment(AdminDepartmentDO departmentDO) {
        if (departmentDO == null) {return false;}
        return dbHelper.update(departmentDO) > 0;
    }

    @Override
    public WebJsonBean checkDepartmentTree(List<AdminDepartmentTreeVO> departmentTreeData) {
        for (AdminDepartmentTreeVO node : departmentTreeData) {
            if (StringUtils.isBlank(node.getName())) {
                return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "存在【部门名称】为空的节点！");
            }
            if (node.getId() == null && StringUtils.isBlank(node.getWebkey())) {
                return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "存在非法数据！");
            }
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                WebJsonBean result = checkDepartmentTree(node.getChildren());
                if (result.getCode() != 0) {
                    return result;
                }
            }
        }
        return WebJsonBean.ok();
    }

    @Override @Transactional
    public boolean saveDepartmentTree(List<AdminDepartmentTreeVO> departmentTreeData) {
        List<Long> existId = new ArrayList<>();

        List<AdminUserDO> userNeedUpdateList;
        if (departmentTreeData.isEmpty()) {
            dbHelper.delete(AdminDepartmentDO.class, "where 1 = 1");
            userNeedUpdateList = dbHelper.getAll(AdminUserDO.class,
                    "where department_id is not null");
        } else {
            _saveTree(existId, departmentTreeData, null);
            dbHelper.delete(AdminDepartmentDO.class, "where id not in (?)", existId);
            //删除用户表中不存在的部门id
            userNeedUpdateList = dbHelper.getAll(AdminUserDO.class,
                    "where department_id is not null and department_id not in (?)",
                    existId);
        }
        ListUtils.forEach(userNeedUpdateList, o -> o.setDepartmentId(null));
        dbHelper.updateWithNull(userNeedUpdateList);
        return true;
    }

    /**
     * 递归保存树形信息
     * @param existId
     * @param treeData
     * @param parentId
     */
    private void _saveTree(List<Long> existId, List<AdminDepartmentTreeVO> treeData, Long parentId) {
        if (treeData == null) {
            return;
        }
        for (int i = 0; i < treeData.size(); i++) {
            AdminDepartmentTreeVO node = treeData.get(i);
            node.setSeq(i + 1);
            if (parentId == null) {
                node.setParentId(0L);
            } else {
                node.setParentId(parentId);
            }
            dbHelper.insertOrUpdate(node);
            existId.add(node.getId());
            _saveTree(existId, (List<AdminDepartmentTreeVO>) node.getChildren(), node.getId());
        }
    }

    @Override
    public boolean saveUserDepartment(Long userId, Long departmentId) {
        if (userId == null || departmentId == null) {
            return false;
        }
        AdminUserDO userDO = getUserById(userId);
        if (userDO == null) {
            return false;
        }
        AdminDepartmentDO departmentDO = getDepartmentById(departmentId);
        if (departmentDO == null) {
            return false;
        }
        userDO.setDepartmentId(departmentId);

        return dbHelper.insertOrUpdate(userDO) > 0;
    }

    @Override
    public AdminDepartmentAndChildrenVO getDepartmentAndChildrenVO(Long departmentId) {
        if (departmentId == null) {
            return null;
        }
        return dbHelper.getByKey(AdminDepartmentAndChildrenVO.class, departmentId);
    }

    @Override
    public List<Long> getSelfAndChildrenIds(AdminDepartmentAndChildrenVO departmentAndChildrenVO) {
        if (departmentAndChildrenVO == null) {
            return new ArrayList<>();
        }
        List<Long> selfIdAndChildrenIds = new ArrayList<>();
        _getSelfAndChildrenIds(selfIdAndChildrenIds, departmentAndChildrenVO);
        return selfIdAndChildrenIds;
    }

    /**递归获取节点id及其子节点id*/
    private void _getSelfAndChildrenIds(List<Long> ids, AdminDepartmentAndChildrenVO departmentAndChildrenVO){
        if (departmentAndChildrenVO == null) {
            return;
        }
        ids.add(departmentAndChildrenVO.getId());
        for (AdminDepartmentAndChildrenVO children : departmentAndChildrenVO.getChildren()) {
            _getSelfAndChildrenIds(ids, children);
        }
    }

    @Override
    public List<AdminUserDO> getUserByDepartmentId(Long departmentId) {
        if (departmentId == null) { return new ArrayList<>(); }
        return dbHelper.getAll(AdminUserDO.class, "where department_id = ?", departmentId);
    }

    @Override
    public List<AdminUserDO> getUserByDepartmentIds(List<Long> departmentIds) {
        if (departmentIds.isEmpty()) { return new ArrayList<>(); }
        return dbHelper.getAll(AdminUserDO.class, "where department_id in (?)", departmentIds);
    }
    
    @Override
    public PageData<AdminUserRelatedDepartmentVO> getUserWithDepartments(
    		int page, int pageSize, String keyword, 
    		List<Long> departmentIds, Date createStarttime, Date createEndTime) {
		StringBuilder sql = new StringBuilder("where 1=1");
		List<Object> params = new ArrayList<>();
		
		if(StringUtils.isNotBlank(keyword)) {
			sql.append(" and (user_name like ? or real_name like ? or phone like ?)");
			params.add("%" + keyword + "%");
			params.add("%" + keyword + "%");
			params.add("%" + keyword + "%");
		}
		
		if(departmentIds != null && !departmentIds.isEmpty()) {
			//获取最后一个id -> 再获取其所有子部门的 id
			Long departmentIdsLast = departmentIds.get(departmentIds.size()-1);
			AdminDepartmentAndChildrenVO departmentAndChildrenVO = getDepartmentAndChildrenVO(departmentIdsLast);
			if (departmentAndChildrenVO != null){
				List<Long> searchIds = getSelfAndChildrenIds(departmentAndChildrenVO);
				sql.append(" and department_id in (?)");
				params.add(searchIds);
			}
		}
		
		if(createStarttime != null) {
			sql.append(" and create_time>=?");
			params.add(createStarttime);
		}
		if(createEndTime != null) {
			sql.append(" and create_time<=?");
			params.add(createEndTime);
		}
    	return dbHelper.getPage(AdminUserRelatedDepartmentVO.class, page, pageSize,
    			sql.toString(), params.toArray());
    }
}
