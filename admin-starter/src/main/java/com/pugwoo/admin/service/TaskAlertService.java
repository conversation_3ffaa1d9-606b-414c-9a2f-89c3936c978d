package com.pugwoo.admin.service;

import com.pugwoo.admin.entity.AdminTaskDO;
import com.pugwoo.admin.entity.AdminTaskLogDO;

/**
 * 任务告警服务
 * 用于处理任务失败时的邮件告警功能
 */
public interface TaskAlertService {

    /**
     * 发送任务失败告警邮件
     * @param task 任务信息
     * @param taskLog 任务日志信息
     */
    void sendTaskFailureAlert(AdminTaskDO task, AdminTaskLogDO taskLog);

    /**
     * 检查邮箱地址格式是否正确
     * @param emails 邮箱地址，多个用分号分隔
     * @return 是否格式正确
     */
    boolean isValidEmailFormat(String emails);
}
