package com.pugwoo.admin.service;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminDepartmentDO;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.vo.AdminDepartmentAndChildrenVO;
import com.pugwoo.admin.vo.AdminDepartmentTreeVO;
import com.pugwoo.admin.vo.AdminUserRelatedDepartmentVO;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContext;
import com.pugwoo.dbhelper.model.PageData;

import java.util.Date;
import java.util.List;

/**
 * description:
 *   用户操作
 *   部门操作
 * <AUTHOR>
 * @date 2018-04-25
 */
public interface AdminUserService {
	
    /**
     * 根据userId获取记录
     * @param userId 用户id
     * @return  user记录<br/> userId为null时返回null
     */
    AdminUserDO getUserById(Long userId);

    /**
     * 根据username获得用户DO
     * @param username 用户名
     */
    AdminUserDO getUserByUsername(String username);
    
    /**
     * 查询附带上部门信息的用户列表
     * @param departmentIds 部门id，支持多个，将查询该部门下所有部门的用户
     */
    PageData<AdminUserRelatedDepartmentVO> getUserWithDepartments(int page, int pageSize,
                                                                  String keyword, List<Long> departmentIds,
                                                                  Date createStarttime, Date createEndTime);
    
    /**
     * 按关键词查询用户，用于自动完成input
     * @param ids 当提供id时，直接拿id对应的用户，不执行keyword查询
     * @param keyword
     * @return
     */
    List<AdminUserDO> queryUser(List<Long> ids, String keyword, int limit);

	/**
	 * 用户名是否存在
	 * @param username 用户名，不做trim()处理
	 * @return true 存在 <br/> false 不存在
	 */
	boolean isExistUsername(String username);

    /**
     * 获取所有的用户信息
     * @return  user List
     */
    List<AdminUserDO> getAllUser();
    
    /**
     * 插入或更新用户
     * @param adminUserDO 用户信息do
     * @return 插入数量
     */
    boolean addOrUpdate(AdminUserDO adminUserDO);

    /**
     * 删除用户
     * @param userId 用户id
     * @return  删除条数
     */
    boolean deleteUserById(Long userId);
    
    /**
     * 刷新所有用户redis缓存中的urls
     * @return true 成功<br/> false 失败<br/>
     */
    boolean refreshAllUserUrls();

    /**
     * 构建登录态的上下文
     */
    AdminUserLoginContext buildAdminUserLoginContext(AdminUserDO user);

    /**
     * 续期token，这个有缓存，1分钟续期1次就足够了
     */
    void renewalToken(String token);
    
    /**
     * 移除指定用户的登陆态
     * @param userId 用户id
     * @return true 成功<br/> false 失败<br/>
     */
    boolean removeUserLoginContext(Long userId);

    /**
     * 更新指定用户的登录态中的 userName, isAdmin
     * @param adminUserDO
     * @return
     */
    boolean updateUserLoginContext(AdminUserDO adminUserDO);

    /**
     * 部门转换为树形数据
     * @return 部门树形数据
     */
    List<AdminDepartmentTreeVO> getDepartmentTree();

    /**
     * 根据id获取部门信息
     * @param id 部门id
     * @return 部门信息
     */
    AdminDepartmentDO getDepartmentById(Long id);

    /**
     * 更新部门信息
     * @param departmentDO 部门对象
     * @return true 成功
     */
    boolean updateDepartment(AdminDepartmentDO departmentDO);

    /**
     * 校验部门树形结构
     *      部门名称name不允许为空
     *      部门id为空的，webkey必须有值
     * 如果校验失败，立即返回
     * @param departmentTreeData 部门树形数据
     * @return 校验不通过直接返回第一条错误信息
     */
    WebJsonBean checkDepartmentTree(List<AdminDepartmentTreeVO> departmentTreeData);

    /**
     * 保存部门树形结构
     * @param departmentTreeData 部门树形数据
     * @return true 成功
     */
    boolean saveDepartmentTree(List<AdminDepartmentTreeVO> departmentTreeData);

    /**
     * 更新用户部门信息
     * @param userId 用户id
     * @param departmentId 部门id
     * @return true 成功
     */
    boolean saveUserDepartment(Long userId, Long departmentId);

    /**
     * 获取部门及其所有子部门信息
     * @param departmentId 部门id
     * @return 部门及其所有子部门信息
     */
    AdminDepartmentAndChildrenVO getDepartmentAndChildrenVO(Long departmentId);

    /**
     * 获取部门节点及其子节点的 id
     * @param departmentAndChildrenVO 部门及其所有子部门信息
     * @return 部门及其所有子部门的 id list
     */
    List<Long> getSelfAndChildrenIds(AdminDepartmentAndChildrenVO departmentAndChildrenVO);

    /**
     * 根据部门id获取用户信息
     * @param departmentId 部门id
     * @return userList
     */
    List<AdminUserDO> getUserByDepartmentId(Long departmentId);

    /**
     * 根据部门id list获取用户信息
     * @param departmentIds 部门id list
     * @return user id
     */
    List<AdminUserDO> getUserByDepartmentIds(List<Long> departmentIds);
    
}
