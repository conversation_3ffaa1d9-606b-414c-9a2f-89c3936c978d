package com.pugwoo.admin.service;

import com.pugwoo.admin.bean.ResultBean;
import com.pugwoo.admin.entity.AdminTaskDO;
import com.pugwoo.admin.entity.AdminTaskLogDO;
import com.pugwoo.admin.enums.AdminTaskStatusEnum;
import com.pugwoo.admin.web.tasklog.TaskLog;
import com.pugwoo.dbhelper.model.PageData;
import org.springframework.scheduling.annotation.Scheduled;

import java.lang.reflect.Method;
import java.util.concurrent.Future;

public interface AdminTaskService {

    /**
     * 重跑指定的任务日志记录
     * @param logId 任务日志ID
     * @return 重跑结果，成功返回新的日志记录ID
     */
    ResultBean<Long> rerunTask(Long logId);

    /**
     * 运行指定的任务
     * @param taskId 任务ID
     * @param args 任务参数，JSON格式的数组字符串
     * @return 运行结果，成功返回新的日志记录ID
     */
    ResultBean<Long> runTask(Long taskId, String args);

    /**
     * 通过主键获得数据
     */
    AdminTaskDO getById(Integer id);
    
    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     */
    PageData<AdminTaskDO> getPage(int page, int pageSize);
    
    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdate(AdminTaskDO adminTaskDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteById(Long id);

    // 以下是log部分

    /**
     * 通过主键获得数据
     */
    AdminTaskLogDO getLogById(Long id);

    /**
     * 获得分页数据
     * @param page 页数，从1开始，必须>=1
     * @param pageSize 每页个数，必须>=1
     * @param taskId 任务ID，可选参数，用于过滤特定任务的日志
     */
    PageData<AdminTaskLogDO> getLogPage(int page, int pageSize, Long taskId);

    /**
     * 更新数据，失败返回null。
     * 注意：这个方法非常灵活，可以修改任何数据，请小心暴露，原则上这个方法不要被太远的应用调用。
     */
    ResultBean<Long> insertOrUpdateLog(AdminTaskLogDO adminTaskLogDO);

    /**
     * 根据主键删除数据
     */
    boolean deleteLogById(Long id);

    // 以下是从TaskLogAOP移动过来的数据库操作方法

    /**
     * 生成新的任务日志记录
     * @param method 执行的方法
     * @param args 方法参数
     * @param taskLog TaskLog注解
     * @param scheduled Scheduled注解
     * @param status 任务状态
     * @return 创建的任务日志记录
     */
    AdminTaskLogDO generateNewTaskLog(Method method, Object[] args, TaskLog taskLog,
                                      Scheduled scheduled, AdminTaskStatusEnum status);

    /**
     * 创建或更新AdminTaskDO记录
     * @param adminTaskLogDO 任务日志信息
     * @return 创建或更新的任务记录
     */
    AdminTaskDO createOrUpdateAdminTask(AdminTaskLogDO adminTaskLogDO);

    /**
     * 更新失败的任务记录
     * @param taskLogFuture 任务日志Future对象
     * @param errorMsg 错误信息
     * @param startTime 开始时间
     */
    void updateFailedTask(Future<AdminTaskLogDO> taskLogFuture, String errorMsg, long startTime);

    /**
     * 更新成功的任务记录
     * @param taskLogFuture 任务日志Future对象
     * @param startTime 开始时间
     */
    void updateSuccessfulTask(Future<AdminTaskLogDO> taskLogFuture, long startTime);

    /**
     * 更新任务状态为RUNNING
     * @param taskLogFuture 任务日志Future对象
     */
    void updateTaskStatusToRunning(Future<AdminTaskLogDO> taskLogFuture);

    /**
     * 记录被跳过的任务
     * @param method 执行的方法
     * @param args 方法参数
     * @param taskLog TaskLog注解
     * @param scheduled Scheduled注解
     * @param existingTask 现有的任务记录
     */
    void recordSkippedTask(Method method, Object[] args, TaskLog taskLog,
                          Scheduled scheduled, AdminTaskDO existingTask);

    /**
     * 根据taskCode查询任务
     * @param taskCode 任务编码
     * @return 任务记录
     */
    AdminTaskDO getByTaskCode(String taskCode);

}