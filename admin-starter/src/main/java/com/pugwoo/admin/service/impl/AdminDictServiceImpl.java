package com.pugwoo.admin.service.impl;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.admin.entity.AdminDictDO;
import com.pugwoo.admin.entity.AdminDictValueDO;
import com.pugwoo.admin.service.AdminDictService;
import com.pugwoo.admin.vo.AdminDictVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class AdminDictServiceImpl implements AdminDictService {
	
	@Autowired @Qualifier("adminDBHelper")
	private DBHelper dbHelper;
	
	@Override
	public List<AdminDictValueDO> getDictValues(String code) {
		if(code == null) return new ArrayList<>();
		return dbHelper.getAll(AdminDictValueDO.class, "where dict_code=?", code);
	}

	@Override
	public PageData<AdminDictVO> getPage(int page, int pageSize, String name, String code) {
		StringBuilder where = new StringBuilder("where 1=1");
		List<Object> params = new ArrayList<>();
		if(StringUtils.isNotBlank(name)) {
			where.append(" and name like ?");
			params.add("%" + name.trim() + "%");
		}
		if(StringUtils.isNotBlank(code)) {
			where.append(" and code like ?");
			params.add("%" + code.trim() + "%");
		}
		return dbHelper.getPage(AdminDictVO.class, page, pageSize, where.toString(), params.toArray());
	}

	@Override
	public boolean isConflict(Long id, String name, String code) {
		if(name == null || code == null) {
			return false;
		}
		if(id != null) {
			return dbHelper.isExist(AdminDictDO.class, "where (name=? or code=?) and id!=?", name, code, id);
		} else {
			return dbHelper.isExist(AdminDictDO.class, "where name=? or code=?", name, code);
		}
	}
	
	@Override @Transactional
	public AdminDictDO insertOrUpdate(AdminDictDO adminDictDO) {
		boolean needUpdate = adminDictDO.getId() != null;
        int rows = dbHelper.insertOrUpdate(adminDictDO);
        if(needUpdate) {
        	List<AdminDictValueDO> values = dbHelper.getAll(AdminDictValueDO.class,
        			"where dict_id=?", adminDictDO.getId());
        	for(AdminDictValueDO value : values) {
        		if(!Objects.equals(value.getDictCode(), adminDictDO.getCode())) {
        			value.setDictCode(adminDictDO.getCode());
        			dbHelper.update(value);
        		}
        	}
        }
        return rows > 0 ? adminDictDO : null;
	}
	
	@Override @Transactional
	public boolean delete(Long id) {
		if(id == null) return false;
		dbHelper.delete(AdminDictDO.class, "where id=?", id);
		dbHelper.delete(AdminDictValueDO.class, "where dict_id=?", id);
		return true;
	}
	
	@Override
	public boolean batchInsertUpdateValues(Long id, String code, List<AdminDictValueDO> values) {
		for(int i = 0; i < values.size(); i++) {
			AdminDictValueDO value = values.get(i);
			value.setDictId(id);
			value.setDictCode(code);
			value.setSeq(i + 1);
		}

		List<Long> newIds = ListUtils.transform(ListUtils.filter(values, value -> value.getId() != null),
				AdminCoreDO::getId);
		if (newIds.isEmpty()) {
			dbHelper.delete(AdminDictValueDO.class, "where dict_id=?", id); // not in (null) 不会匹配到任何数据
		} else {
			dbHelper.delete(AdminDictValueDO.class, "where dict_id=? and id not in (?)", id, newIds);
		}

		List<AdminDictValueDO> toUpdate = ListUtils.filter(values, value -> value.getId() != null);
		List<AdminDictValueDO> toInsert = ListUtils.filter(values, value -> value.getId() == null);

		dbHelper.insertBatchWithoutReturnId(toInsert);
		dbHelper.update(toUpdate);

		return true;
	}
}
