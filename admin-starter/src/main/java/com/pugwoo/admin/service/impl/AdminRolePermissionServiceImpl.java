package com.pugwoo.admin.service.impl;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminRoleDO;
import com.pugwoo.admin.entity.AdminRoleUrlDO;
import com.pugwoo.admin.entity.AdminUrlDO;
import com.pugwoo.admin.entity.AdminUrlIgnoreDO;
import com.pugwoo.admin.entity.AdminUserRoleDO;
import com.pugwoo.admin.enums.UrlTypeEnum;
import com.pugwoo.admin.service.AdminRolePermissionService;
import com.pugwoo.admin.vo.AdminRoleVO;
import com.pugwoo.admin.vo.AdminUrlChildrenVO;
import com.pugwoo.admin.vo.AdminUrlDTO;
import com.pugwoo.admin.vo.AdminUrlTreeVO;
import com.pugwoo.admin.vo.MenuDTO;
import com.pugwoo.admin.vo.RoleUrlRelatedRoleVO;
import com.pugwoo.admin.vo.RoleUrlRelatedUrlVO;
import com.pugwoo.admin.vo.UserActuallyPermissionDTO;
import com.pugwoo.admin.vo.UserRoleRelatedRoleVO;
import com.pugwoo.admin.vo.UserRoleRelatedUserVO;
import com.pugwoo.admin.vo.UserRoleUrlJoinVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.tree.TreeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Service
public class AdminRolePermissionServiceImpl implements AdminRolePermissionService {

	@Autowired @Qualifier("adminDBHelper")
	private DBHelper dbHelper;

//==========user_role
	
	@Override
	public List<UserRoleRelatedUserVO> getUserListByRoleId(Long roleId) {
		if (roleId == null) {return new ArrayList<>();}
		return dbHelper.getAll(UserRoleRelatedUserVO.class, "where role_id=?", roleId);
	}

	@Override
	public List<UserRoleRelatedRoleVO> getRoleListByUserId(Long userId) {
		if (userId == null) {return new ArrayList<>();}
		return dbHelper.getAll(UserRoleRelatedRoleVO.class, "where user_id=?", userId);
	}

	@Override @Transactional
	public boolean saveByRoleIdAndUserIds(Long roleId, List<Long> userIds) {
		if(roleId == null || userIds == null) {return false;}
		
		List<AdminUserRoleDO> oldRoleUserList = dbHelper.getAll(AdminUserRoleDO.class, "where role_id=?", roleId);
		List<Long> oldRoleUserIds = ListUtils.transform(oldRoleUserList, o -> o.getUserId());

		List<Long> addRoleUserIds = ListUtils.subtract(userIds, oldRoleUserIds); //新增的UserId
		List<Long> deleteRoleUserIds = ListUtils.subtract(oldRoleUserIds, userIds); //删除的UserId

		// 新增List
		if (!addRoleUserIds.isEmpty()) {
			ListUtils.forEach(addRoleUserIds, o -> {
				AdminUserRoleDO temp = new AdminUserRoleDO();
				temp.setRoleId(roleId);
				temp.setUserId(o);
				dbHelper.insert(temp);
			});
		}

		// 删除List
		if (!deleteRoleUserIds.isEmpty()) {
			List<AdminUserRoleDO> deleteUserRoleDOList = 
				ListUtils.filter(oldRoleUserList, o -> deleteRoleUserIds.contains(o.getUserId()));
			dbHelper.delete(deleteUserRoleDOList);
		}
		return true;
	}

	@Override @Transactional
	public boolean saveByUserIdAndRoleIds(Long userId, List<Long> roleIds) {
		if(userId == null || roleIds == null) {return false;}

		List<AdminUserRoleDO> oldUserRoleList = dbHelper.getAll(AdminUserRoleDO.class, "where user_id=?", userId);
		List<Long> oldUserRoleIds = ListUtils.transform(oldUserRoleList, o -> o.getRoleId());

		List<Long> addUserRoleIds = ListUtils.subtract(roleIds, oldUserRoleIds);
		List<Long> deleteUserRoleIds = ListUtils.subtract(oldUserRoleIds, roleIds);

		//新增List
		if (!addUserRoleIds.isEmpty()) {
			ListUtils.forEach(addUserRoleIds, o -> {
				AdminUserRoleDO temp = new AdminUserRoleDO();
				temp.setRoleId(o);
				temp.setUserId(userId);
				dbHelper.insert(temp);
			});
		}
		//删除List
		if (!deleteUserRoleIds.isEmpty()) {
			List<AdminUserRoleDO> deleteUserRoleList =
					ListUtils.filter(oldUserRoleList, o -> deleteUserRoleIds.contains(o.getRoleId()));
			dbHelper.delete(deleteUserRoleList);
		}
		return true;
	}

	@Override
	public int deleteUserRoleByUserId(Long userId) {
		if (userId == null) {return 0;}
		return dbHelper.delete(AdminUserRoleDO.class, "where user_id = ?", userId);
	}

//==========role
	
	@Override
	public PageData<AdminRoleVO> getRolePage(int page, int pageSize, String name,
											 String roleGroup) {
		StringBuilder where = new StringBuilder("where 1=1");
		List<Object> params = new ArrayList<>();
		if(StringUtils.isNotBlank(name)) {
			where.append(" and name like ?");
			params.add("%" + name.trim() + "%");
		}
		if(StringUtils.isNotBlank(roleGroup)) {
			where.append(" and role_group=?");
			params.add(roleGroup);
		}
		where.append(" order by role_group");
		return dbHelper.getPage(AdminRoleVO.class, page, pageSize, where.toString(), params.toArray());
	}

	@Override
	public AdminRoleDO getRoleById(Long roleId) {
		if (roleId == null) {
			return null;
		}
		return dbHelper.getByKey(AdminRoleDO.class, roleId);
	}

	@Override
	public List<AdminRoleDO> getAllRoles() {
		return dbHelper.getAll(AdminRoleDO.class);
	}

	@Override
	public int insertOrUpdateRole(AdminRoleDO adminRoleDO) {
		if (adminRoleDO == null) {
			return -1;
		}
		return dbHelper.insertOrUpdate(adminRoleDO);
	}

	@Override @Transactional
	public int deleteRoleById(Long roleId) {
		if (roleId == null) {return -1;}
		
		int rowDeleteRole = dbHelper.delete(AdminRoleDO.class, "where id=?", roleId);
		if(rowDeleteRole <= 0) {
			return 0;
		}

		int rowDeleteRoleUrl = dbHelper.delete(AdminRoleUrlDO.class, "where role_id=?", roleId);
		int rowDeleteUserRole = dbHelper.delete(AdminUserRoleDO.class, "where role_id=?", roleId);
		return rowDeleteRole + rowDeleteRoleUrl + rowDeleteUserRole;
	}

	@Override
	public boolean isExistRoleName(AdminRoleDO adminRoleDO, String name) {
		if (StringUtils.isBlank(name) || adminRoleDO == null) {
			return true;
		}
		AdminRoleDO existAdminRoleDO = dbHelper.getOne(AdminRoleDO.class, "where name=?", name.trim());
		if (existAdminRoleDO == null) {
		    return false;
        }
		if (Objects.equals(adminRoleDO.getId(), existAdminRoleDO.getId())) {
		    return false;
        }
        return true;
	}

	@Override
	public boolean isExistRoleCode(AdminRoleDO adminRoleDO, String code) {
		if (StringUtils.isBlank(code) || adminRoleDO == null) {
			return false;
		}
		AdminRoleDO existAdminRoleDO = dbHelper.getOne(AdminRoleDO.class, "where code = ?", code.trim());
        if (existAdminRoleDO == null) {
            return false;
        }
        if (Objects.equals(adminRoleDO.getId(), existAdminRoleDO.getId())) {
            return false;
        }
        return true;
	}

//==========role_url
	
	@Override @Transactional
	public boolean saveByUrlIdAndRoleIds(Long urlId, List<Long> roleIds) {
		if (urlId == null || roleIds == null) {return false;}

		// 1. 查询比较得出此次修改新增和删除的角色
		List<Long> oldRoleIds = ListUtils.transform(dbHelper.getAll(AdminRoleUrlDO.class,
				"where url_id=?", urlId), o -> o.getRoleId());
		List<Long> addRoleIds = ListUtils.subtract(roleIds, oldRoleIds);
		List<Long> deleteRoleIds = ListUtils.subtract(oldRoleIds, roleIds);
		if (addRoleIds.isEmpty() && deleteRoleIds.isEmpty()) {
			return true;
		}

		AdminUrlChildrenVO adminUrlVO = dbHelper.getByKey(
				AdminUrlChildrenVO.class, urlId);

		// 2. 处理节点的角色新增
		if (!addRoleIds.isEmpty()) {
			List<Long> urlIds = ListUtils.newArrayList(urlId); // 说明：原来这里是其自身及所有父节点的集合，现在不需要父节点了
			List<AdminRoleUrlDO> adminRoleUrlDOList = getRoleUrlByUrlIds(urlIds);
			List<AdminRoleUrlDO> dbList = ListUtils.filter(
					adminRoleUrlDOList, o -> addRoleIds.contains(o.getRoleId())); // 过滤掉不符合的roleId

			List<Map.Entry<Long, Long>> addUrlRoleIds = ListUtils.cartesianProduct(urlIds, addRoleIds);
			for(Map.Entry<Long, Long> e : addUrlRoleIds) {
				if(!ListUtils.contains(dbList, o ->
						Objects.equals(o.getUrlId(), e.getKey()) && Objects.equals(o.getRoleId(), e.getValue()))) {
					AdminRoleUrlDO temp = new AdminRoleUrlDO();
					temp.setUrlId(e.getKey());
					temp.setRoleId(e.getValue());
					dbHelper.insert(temp);
				}
			}
		}

		// 3. 处理节点及所有子节点的角色删除
		if (!deleteRoleIds.isEmpty()) {
			List<Long> urlIdAndChildrenIds = getSelfAndChildrenID(adminUrlVO);
			List<AdminRoleUrlDO> adminRoleUrlDOList = getRoleUrlByUrlIds(urlIdAndChildrenIds);
			List<AdminRoleUrlDO> deleteList = ListUtils.filter(adminRoleUrlDOList,
					o -> deleteRoleIds.contains(o.getRoleId()));
			ListUtils.forEach(deleteList, o -> dbHelper.delete(o));
		}

		return true;
	}

	@Override
	public boolean saveByRoleIdAndUrlIds(Long roleId, List<Long> urlIds) {
		if(roleId == null || urlIds == null) return false;
		
		List<AdminRoleUrlDO> oldRoleUrlList = dbHelper.getAll(AdminRoleUrlDO.class, "where role_id=?", roleId);
		List<Long> oldRoleUrlIds = ListUtils.transform(oldRoleUrlList, o -> o.getUrlId());

		List<Long> addRoleUrlIds = ListUtils.subtract(urlIds, oldRoleUrlIds);
		List<Long> deleteRoleUrlIds = ListUtils.subtract(oldRoleUrlIds, urlIds);

		// 新增list
		if (!addRoleUrlIds.isEmpty()) {
			ListUtils.forEach(addRoleUrlIds, o -> {
				AdminRoleUrlDO temp = new AdminRoleUrlDO();
				temp.setRoleId(roleId);
				temp.setUrlId(o);
				dbHelper.insert(temp);
			});
		}
		
		// 删除list
		if (deleteRoleUrlIds.size() != 0) {
			List<AdminRoleUrlDO> deleteRoleUrlDOList = ListUtils.filter(
					oldRoleUrlList, o -> deleteRoleUrlIds.contains(o.getUrlId()));
			dbHelper.delete(deleteRoleUrlDOList);
		}
		return true;
	}

	@Override
	public List<RoleUrlRelatedRoleVO> getRoleByUrlId(Long urlId) {
		if (urlId == null) {return new ArrayList<>();}
		List<RoleUrlRelatedRoleVO> list = dbHelper.getAll(RoleUrlRelatedRoleVO.class,
				"where url_id=?", urlId);
		return list;
	}

	@Override
	public List<RoleUrlRelatedUrlVO> getUrlByRoleId(Long roleId) {
		if (roleId == null) {return new ArrayList<>();}
		List<RoleUrlRelatedUrlVO> list = dbHelper.getAll(RoleUrlRelatedUrlVO.class,
				"where role_id = ?", roleId);
		return list;
	}

	private List<Long> getSelfAndChildrenID(AdminUrlChildrenVO adminUrlVO) {
		List<Long> list = new ArrayList<>();
		if(adminUrlVO == null) {return list;}
		list.add(adminUrlVO.getId());
		_getUrlChildrenIds(adminUrlVO.getChildren(), list);
		return list;
	}

	private void _getUrlChildrenIds(List<AdminUrlChildrenVO> childrenVO, List<Long> list) {
		if(childrenVO == null) {return;}
		for(AdminUrlChildrenVO vo : childrenVO) {
			list.add(vo.getId());
			_getUrlChildrenIds(vo.getChildren(), list);
		}
	}

	private List<AdminRoleUrlDO> getRoleUrlByUrlIds(List<Long> urlIds) {
		if (urlIds == null || urlIds.isEmpty()) {
			return new ArrayList<>();
		}
		return dbHelper.getAll(AdminRoleUrlDO.class, "where url_id in (?)", urlIds);
	}

//==========url
	
	@Override
	public AdminUrlDO getUrlById(Long id) {
		if(id == null) {return null;}
		return dbHelper.getByKey(AdminUrlDO.class, id);
	}

	@Override
	public List<AdminUrlTreeVO> getAllUrls() {
		return dbHelper.getAll(AdminUrlTreeVO.class);
	}

	@Override
	public UserActuallyPermissionDTO getAllUrlsByUserId(Long userId) {
		if (userId == null) {
			return new UserActuallyPermissionDTO(new ArrayList<>());
		}

		List<UserRoleUrlJoinVO> urls = dbHelper.getAll(UserRoleUrlJoinVO.class,
				"where t1.user_id=? and (t1.expire_time is null or t1.expire_time<=?)",
				userId, new Date());

		UserActuallyPermissionDTO result = new UserActuallyPermissionDTO();

		List<AdminUrlDO> commonUrls = getCommonUrls(urls);
		result.setUrls(commonUrls);

		return result;
	}

	private List<AdminUrlDO> getCommonUrls(List<UserRoleUrlJoinVO> urls) {
		List<AdminUrlDO> allUrls = new ArrayList<>();

		Set<Long> exists = new HashSet<>();
		for(UserRoleUrlJoinVO url : urls) { // 去重
			AdminUrlDO u = url.getAdminRoleUrlVO().getAdminUrlDO();
			if(u != null && !exists.contains(u.getId())) {
				allUrls.add(u);
				exists.add(u.getId());
			}
		}

		// 2. 增加所有目录，含去重
		List<AdminUrlDO> folders = dbHelper.getAll(AdminUrlDO.class, "where type=?", UrlTypeEnum.FOLDER.getCode());
		for(AdminUrlDO folder : folders) {
			if(!exists.contains(folder.getId())) {
				allUrls.add(folder);
				exists.add(folder.getId());
			}
		}

		// 3. 去掉目录中，其所有子目录没有MENU类型的目录
		Set<Long> pickedFolder = new HashSet<>(); // 选中要加入的目录
		Map<Long, AdminUrlDO> map = ListUtils.toMap(allUrls, o -> o.getId(), o -> o);
		for(AdminUrlDO url : allUrls) {
			if(UrlTypeEnum.MENU.getCode().equalsIgnoreCase(url.getType())) {
				AdminUrlDO cur = url;
				Long parentId = null;
				while((parentId = cur.getParentId()) != null) {
					cur = map.get(parentId);
					if(cur == null || pickedFolder.contains(cur.getId())) {break;}
					if(UrlTypeEnum.FOLDER.getCode().equalsIgnoreCase(cur.getType())) {
						pickedFolder.add(cur.getId());
					}
				}
			}
		}

		Iterator<AdminUrlDO> it = allUrls.iterator();
		while(it.hasNext()) {
			AdminUrlDO adminUrlDO = it.next();
			if(UrlTypeEnum.FOLDER.getCode().equalsIgnoreCase(adminUrlDO.getType())) {
				if(!pickedFolder.contains(adminUrlDO.getId())) {
					it.remove();
				}
			}
		}

		return allUrls;
	}

	@Override
	public boolean updateUrl(AdminUrlDO adminUrlDO) {
		return adminUrlDO != null && dbHelper.update(adminUrlDO) > 0;
	}
	
	@Override
	public List<MenuDTO> genMenu(List<AdminUrlDTO> urls) {
		if(urls == null) return new ArrayList<>();
		
		urls = ListUtils.filter(urls, o -> !UrlTypeEnum.OTHER.getCode().equals(o.getType())); // other类型不要
		List<MenuDTO> menus = ListUtils.transform(urls, o -> {
			MenuDTO menu = new MenuDTO();
			menu.setTitle(o.getName());
			menu.setIcon(o.getIcon());
			menu.setUrl(o.getUrl());
			menu.setAttachment(o);
			return menu;
		});
		
		menus = TreeUtils.genTreeNode(menus);
		_postHandle(menus);
		return menus;
	}
	
	private void _postHandle(List<MenuDTO> menus) {
		Iterator<MenuDTO> it = menus.iterator();
		while(it.hasNext()) {
			MenuDTO menu = it.next();
			if(StringUtils.isBlank(menu.getUrl()) && (menu.getSubs() == null || menu.getSubs().isEmpty())) {
				it.remove(); // 空目录不要
				continue;
			}
			if (menu.getSubs() != null) { // 如果某一节点有子节点，则该节点url失效
				if (StringUtils.isNotBlank(menu.getUrl()) && menu.getSubs().isEmpty()) {
					menu.setSubs(null);
				}
				if (menu.getSubs() != null) {
					menu.setUrl(null);
				}
			}
			if(menu.getSubs() != null && !menu.getSubs().isEmpty()) {
				_postHandle(menu.getSubs());
			}
		}
	}

	@Override
	public List<AdminUrlTreeVO> genUrlTree(List<AdminUrlTreeVO> urls) {
		return TreeUtils.genTreeNode(urls);
	}

	@Override
	public List<WebJsonBean> checkUrlTree(List<AdminUrlTreeVO> treeData) {
		List<WebJsonBean> errors = new ArrayList<>();

		for(AdminUrlTreeVO node : treeData) {
            if (node.getId() == null) { //新增节点判断名称
                if (StringUtils.isBlank(node.getName())) {
                    errors.add(WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR,
                            "存在名称为空的节点"));
                }
            }
			UrlTypeEnum type = UrlTypeEnum.getByCode(node.getType());
			if(type == null) {
				errors.add(WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR,
						"节点【" + node.getName() + "】没有指定类型"));
			}
			if((type == UrlTypeEnum.MENU || type == UrlTypeEnum.OTHER) &&
					node.getChildren() != null && !node.getChildren().isEmpty()) {
				for(AdminUrlTreeVO treeVO : node.getChildren()) {
					UrlTypeEnum subType = UrlTypeEnum.getByCode(treeVO.getType());
					if(subType == UrlTypeEnum.FOLDER || subType == UrlTypeEnum.MENU) {
						errors.add(WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR,
								type.getName() + "类型的节点【" + node.getName() + "】不允许下层有目录或菜单类型节点"));
						break;
					}
				}
			}

			if(node.getChildren() != null && !node.getChildren().isEmpty()) {
				List<WebJsonBean> result = checkUrlTree(node.getChildren());
				errors.addAll(result);
			}
		}
		return errors;
	}

	@Override @Transactional
	public boolean saveUrlTree(List<AdminUrlTreeVO> treeData) {
		List<Long> existIds = new ArrayList<>();
		if (treeData.isEmpty()) {
			dbHelper.delete(AdminUrlDO.class, "where 1=1");
			dbHelper.delete(AdminRoleUrlDO.class, "where 1=1");
		} else {
			_saveTree(existIds, treeData, null);
			dbHelper.delete(AdminUrlDO.class, "where id not in (?)", existIds);
			dbHelper.delete(AdminRoleUrlDO.class, "where url_id not in (?)", existIds);
		}
		return true;
	}

	private void _saveTree(List<Long> existId, List<AdminUrlTreeVO> treeData, Long parentId) {
		if(treeData == null) {return;}
		for(int i = 0; i < treeData.size(); i++) {
			AdminUrlTreeVO tree = treeData.get(i);
			tree.setSeq(i + 1);
			if (parentId == null) { // 因为DBHelper默认对字段为null无更新
				tree.setParentId(0L);
			}else {
				tree.setParentId(parentId);
			}
			dbHelper.insertOrUpdate(tree);
			existId.add(tree.getId());
			_saveTree(existId, tree.getChildren(), tree.getId());
		}
	}

//==========url_ignore
	
	@Override
	public boolean ignoreUrl(String url) {
		if(StringUtils.isBlank(url)) {return false;}
		boolean exist = dbHelper.isExist(AdminUrlIgnoreDO.class, "where url=?", url);
		if (exist) {
			return true;
		}

		AdminUrlIgnoreDO ignoreDO = new AdminUrlIgnoreDO();
		ignoreDO.setUrl(url);
		dbHelper.insert(ignoreDO);
		return true;
	}

	@Override
	public List<AdminUrlIgnoreDO> getAllIgnoreUrls() {
		return dbHelper.getAll(AdminUrlIgnoreDO.class);
	}
}
