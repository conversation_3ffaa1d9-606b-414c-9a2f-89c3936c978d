package com.pugwoo.admin.service;

import java.util.List;

import com.pugwoo.admin.entity.AdminDictDO;
import com.pugwoo.admin.entity.AdminDictValueDO;
import com.pugwoo.admin.vo.AdminDictVO;
import com.pugwoo.dbhelper.model.PageData;

public interface AdminDictService {
	
	/**
	 * 根据字典代号查询该字典的所有值
	 * @param code
	 * @return
	 */
	List<AdminDictValueDO> getDictValues(String code);

	PageData<AdminDictVO> getPage(int page, int pageSize, String name, String code);
	
	/**
	 * code和name要求全局唯一
	 * @param id 如果id提供，那么是它自己的话，则不认为已存在
	 * @param name
	 * @param code
	 * @return
	 */
	boolean isConflict(Long id, String name, String code);
	
	AdminDictDO insertOrUpdate(AdminDictDO adminDictDO);
	
	boolean delete(Long id);
	
	boolean batchInsertUpdateValues(Long id, String code, List<AdminDictValueDO> values);
}
