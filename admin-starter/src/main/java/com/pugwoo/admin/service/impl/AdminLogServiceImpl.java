package com.pugwoo.admin.service.impl;

import com.pugwoo.admin.bean.RedisKeyNamespace;
import com.pugwoo.admin.config.AdminDBHelperConfiguration;
import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.entity.AdminLogDO;
import com.pugwoo.admin.entity.AdminLogExceptionDO;
import com.pugwoo.admin.entity.AdminLogSlowSqlDO;
import com.pugwoo.admin.entity.AdminLogSlowWebDO;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.enums.OperateTypeEnum;
import com.pugwoo.admin.enums.SecurityLevelEnum;
import com.pugwoo.admin.service.AdminLogService;
import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContext;
import com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.RedisLimitParam;
import com.pugwoo.wooutils.redis.RedisLimitPeriodEnum;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.method.HandlerMethod;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Service
public class AdminLogServiceImpl implements AdminLogService {

	@Autowired @Qualifier("adminDBHelper")
	private DBHelper dbHelper;
	@Autowired @Qualifier("adminRedisHelper")
	private RedisHelper redisHelper;
	@Autowired(required = false)
	private HttpServletRequest request;
	@Autowired
	private AdminDBHelperConfiguration dbHelperConfiguration;
	@Resource
	private AdminProperties adminProperties;
	@Resource
	private AdminNotifyService adminNotifyService;

	// 2个线程处理异步写log，特别说明，这个线程数不易过大，不然当写入log的线程阻塞时，它将耗尽数量不多的数据库连接池
	private final ThreadPoolExecutor writeLogThreadPool = ThreadPoolUtils.createThreadPool(
			2, 1000000, 2, "write-admin-log");

	@Override
	public boolean log(String remark, OperateTypeEnum opType, SecurityLevelEnum secLevel) {
		AdminUserLoginContext context = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
		return _log(remark, opType, secLevel, context);
	}

	@Override
	public boolean log(String remark, OperateTypeEnum opType, SecurityLevelEnum secLevel,
			AdminUserLoginContext adminUserLoginContext) {
		return _log(remark, opType, secLevel, adminUserLoginContext);
	}
	
	private boolean _log(String remark, OperateTypeEnum opType, SecurityLevelEnum secLevel,
			AdminUserLoginContext context) {
		AdminLogDO adminLogDO = new AdminLogDO();
		adminLogDO.setCreateTime(new Date());
		adminLogDO.setUpdateTime(new Date());
		if(context != null) {
			if(request != null) {
				adminLogDO.setIp(NetUtils.getRemoteIpForJakarta(request));
			}
			adminLogDO.setUserId(context.getUserId());
			adminLogDO.setUserName(context.getUserName());
		}
		adminLogDO.setRemark(remark);
		if(opType != null) {
			adminLogDO.setOperateType(opType.getCode());
		}
		if(secLevel != null) {
			adminLogDO.setSecurityLevel(secLevel.getCode());
		}

		writeLogThreadPool.submit(() -> {dbHelper.insert(adminLogDO);});
		return true;
	}
	
//========generic start=======
	
	/**根据id获取一条记录*/
	private <T> T _getById(Class<T> clazz, Long logId) {
		if (logId == null) {return null;}
		return dbHelper.getByKey(clazz, logId);
	}

	/**获取分页记录*/
	private <T> PageData<T> _getForPage(Class<T> clazz, Integer page, Integer pageSize,
										Boolean onlyUnRead, List<Date> dateRange) {
		if (page == null) {page = 1;}
		if (pageSize == null) {pageSize = 10;}
		StringBuilder sql = new StringBuilder("where 1=1 ");
		List<Object> params = new ArrayList<>();
		if(dateRange != null) {
			if(!dateRange.isEmpty()) {
				sql.append(" and create_time>=?");
				params.add(dateRange.getFirst());
			}
			if(dateRange.size() > 1) {
				sql.append(" and create_time<=?");
				params.add(dateRange.get(1));
			}
		}
		if (onlyUnRead != null && onlyUnRead) {
			sql.append(" and is_read = ?");
			params.add(false);
		}
		sql.append(" order by id desc");
		return dbHelper.getPage(clazz, page, pageSize,
				sql.toString(), params.toArray());
	}

	/**添加一条记录*/
	private void _add(Object t) {
		try {
			writeLogThreadPool.submit(() -> {dbHelper.insert(t);});
		} catch (Throwable e) {
			log.error("write admin log error", e);
		}
	}

	private boolean _setReadAll(Class<?> clazz) {
	    return dbHelper.updateAll(clazz, "set is_read=?", "where is_read=?", true, false) >= 0;
    }

//========exception start========
	
	@Override
	public AdminLogExceptionDO getExceptionById(Long logId) {
		return _getById(AdminLogExceptionDO.class, logId);
	}

	@Override
	public PageData<AdminLogExceptionDO> getExceptionForPage(Integer page, Integer pageSize, Boolean onlyUnRead, List<Date> dateRange) {
		return _getForPage(AdminLogExceptionDO.class, page, pageSize, onlyUnRead, dateRange);
	}

	@Override
	public void addException(Exception ex, HttpServletRequest request, HandlerMethod handlerMethod) {

		//记录异常到数据库，不记录AdminInnerException异常
		AdminLogExceptionDO adminLogExceptionDO = new AdminLogExceptionDO();
		adminLogExceptionDO.setRead(false);
		adminLogExceptionDO.setCreateTime(new Date());
		adminLogExceptionDO.setUpdateTime(new Date());

		if (request != null) {
			StringBuffer requestURL = request.getRequestURL();
			if (request.getQueryString() != null) {
				requestURL.append("?").append(request.getQueryString());
			}
			String completeURL = requestURL.toString();

			log.error("url:{},referer:{} exception", completeURL, request.getHeader("Referer"), ex);

			adminLogExceptionDO.setRequestMethod(request.getMethod());
			adminLogExceptionDO.setUrl(NetUtils.getFullUrlWithParamForJakarta(request));
			adminLogExceptionDO.setReferer(request.getHeader("Referer"));
			adminLogExceptionDO.setIp(NetUtils.getRemoteIpForJakarta(request));

			// 请求参数 转json, 仅保留前1024个字符
			try {
				Map<String, String[]> parameterMap = request.getParameterMap();
				String paramString = JSON.toJson(parameterMap);
				if (paramString.length() > 1024) {
					paramString = paramString.substring(0, 1024);
				}
				adminLogExceptionDO.setParams(paramString);
			} catch (Throwable e) { //ignore exception
			}

		} else {
			log.error("exception happened without request", ex);
		}

		AdminUserLoginContext context = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
		if (context != null) {
			adminLogExceptionDO.setUserId(context.getUserId());
			adminLogExceptionDO.setUserName(context.getUserName());
		}

		if(handlerMethod != null) {
			String classAndMethod = handlerMethod.getBeanType().getName() + "." +
					handlerMethod.getMethod().getName() + "()";
			adminLogExceptionDO.setClassAndMethod(classAndMethod);
		}

		StringWriter errors = new StringWriter();
		ex.printStackTrace(new PrintWriter(errors));
		adminLogExceptionDO.setExceptionMsg(errors.toString());
		adminLogExceptionDO.setExceptionType(ex.getClass().getSimpleName());

		_add(adminLogExceptionDO);

		sendExceptionNotifyToAdmin("请求发生异常",
				"异常:" + adminLogExceptionDO.getExceptionType()
				+ ",用户:" + adminLogExceptionDO.getUserName()
				+ ",url:" + adminLogExceptionDO.getUrl());
	}

	@Override
	public boolean setExceptionRead(AdminLogExceptionDO adminLogExceptionDO) {
		adminLogExceptionDO.setRead(true);
		return dbHelper.update(adminLogExceptionDO) > 0;
	}

    @Override
    public boolean setExceptionReadAll() {
        return _setReadAll(AdminLogExceptionDO.class);
    }

	@Override
	public void sendExceptionNotifyToAdmin(String title, String content) {
		try {
			// 发送告警消息
			Boolean notifyException = adminProperties.getNotifyException();
			if (notifyException == null || !notifyException) {
				return;
			}

			RedisLimitParam limitParam = new RedisLimitParam(RedisKeyNamespace.MAX_EXCEPTION_NOTIFY_PER_DAY,
					RedisLimitPeriodEnum.DAY, 0);

			Integer maxExceptionNotifyPerDay = adminProperties.getMaxExceptionNotifyPerDay();
			if (maxExceptionNotifyPerDay != null && maxExceptionNotifyPerDay > 0) {
				limitParam.setLimitCount(maxExceptionNotifyPerDay);
				if (redisHelper.getLimitCount(limitParam, "") <= 0) {
					return;
				}
			}

			// 检查是否有管理员配置了邮箱，是则发送
			List<AdminUserDO> users = dbHelper.getAll(AdminUserDO.class, "where disabled=0 and is_admin=1 and email!=''");
			if (ListUtils.isNotEmpty(users)) {
				if (maxExceptionNotifyPerDay != null && maxExceptionNotifyPerDay > 0) {
					if (redisHelper.useLimitCount(limitParam, "") <= 0) {
						return;
					}
				}
				for (AdminUserDO user : users) {
					try {
						adminNotifyService.sendEmail(user.getEmail(),
								adminProperties.getSystemName() + ":" + title, content);
					} catch (Exception e) { // 每个用户发送互不影响
						log.error("send exception msg to {} fail", user.getEmail(), e);
					}
				}
			}
		} catch (Throwable e) {
			log.error("send notify msg fail", e);
		}
	}

	//========exception end==========
    
//========slow web start=========
	@Override
	public void addSlowWeb(AdminLogSlowWebDO slowWebDO) {
		_add(slowWebDO);
	}

	@Override
	public PageData<AdminLogSlowWebDO> getSlowWebForPage(Integer page, Integer pageSize, Boolean onlyUnRead, List<Date> dateRange) {
		return _getForPage(AdminLogSlowWebDO.class, page, pageSize, onlyUnRead, dateRange);
	}

	@Override
	public AdminLogSlowWebDO getSlowWebById(Long logId) {
		return _getById(AdminLogSlowWebDO.class, logId);
	}

	@Override
	public boolean setSlowWebRead(AdminLogSlowWebDO slowWebDO) {
		slowWebDO.setRead(true);
		return dbHelper.update(slowWebDO) > 0;
	}

    @Override
    public boolean setSlowWebReadAll() {
        return _setReadAll(AdminLogSlowWebDO.class);
    }
//========slow web end===========
    
//========slow sql start=========
	@Override
	public Integer addSlowSql(AdminLogSlowSqlDO slowSqlDO) {
		writeLogThreadPool.submit(() -> writeByJdbcTemplate(slowSqlDO));
		return 1;

	}

	private void writeByJdbcTemplate(AdminLogSlowSqlDO slowSqlDO) {
		// 为避免慢sql阈值太小时产生死循环，这里直接用jdbc插入
		JdbcTemplate jdbcTemplate = dbHelperConfiguration.getJdbcTemplate();

		List<Object> params = new ArrayList<>();

        String sql = """
insert into t_admin_log_slow_sql
(`deleted`,`create_time`,`update_time`,`user_id`,`user_name`,`url`,`is_read`,`sql`,`sql_param`,`sql_time`)
values(?,?,?,?,?,?,?,?,?,?)
""";

		params.add(0);
		params.add(slowSqlDO.getCreateTime());
		params.add(slowSqlDO.getUpdateTime());
		params.add(slowSqlDO.getUserId());
		params.add(slowSqlDO.getUserName());
		params.add(slowSqlDO.getUrl());
		params.add(slowSqlDO.getRead());
		String s = slowSqlDO.getSql();
		if (s != null && s.length() > 4096) {
			s = s.substring(0, 4096);
		}
		params.add(s);
		params.add(slowSqlDO.getSqlParam());
		params.add(slowSqlDO.getSqlTime());

		jdbcTemplate.update(sql, params.toArray());
	}

	@Override
	public PageData<AdminLogSlowSqlDO> getSlowSqlForPage(Integer page, Integer pageSize, Boolean onlyUnRead, List<Date> dateRange) {
		return _getForPage(AdminLogSlowSqlDO.class, page, pageSize, onlyUnRead, dateRange);
	}

	@Override
	public AdminLogSlowSqlDO getSlowSqlById(Long logId) {
		return _getById(AdminLogSlowSqlDO.class, logId);
	}

	@Override
	public boolean setSlowSqlRead(AdminLogSlowSqlDO slowSqlDO) {
		slowSqlDO.setRead(true);
		return dbHelper.update(slowSqlDO) > 0;
	}

    @Override
    public boolean setSlowSqlReadAll() {
        return _setReadAll(AdminLogSlowSqlDO.class);
    }
//========slow sql end===========
    
}