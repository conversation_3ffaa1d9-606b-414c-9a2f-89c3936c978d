package com.pugwoo.admin.service.impl;

import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.entity.AdminTaskDO;
import com.pugwoo.admin.entity.AdminTaskLogDO;
import com.pugwoo.admin.service.AdminNotifyService;
import com.pugwoo.admin.service.TaskAlertService;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * 任务告警服务实现类
 */
@Slf4j
@Service
public class TaskAlertServiceImpl implements TaskAlertService {

    @Autowired
    private AdminNotifyService adminNotifyService;
    
    @Autowired
    private AdminProperties adminProperties;

    // 邮箱格式验证正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    @Override
    public void sendTaskFailureAlert(AdminTaskDO task, AdminTaskLogDO taskLog) {
        if (task == null || taskLog == null) {
            log.warn("任务或任务日志为空，无法发送告警邮件");
            return;
        }

        String sendEmail = task.getSendEmail();
        if (StringTools.isBlank(sendEmail)) {
            log.debug("任务 {} 未配置告警邮箱，跳过发送告警邮件", task.getTaskName());
            return;
        }

        if (!isValidEmailFormat(sendEmail)) {
            log.warn("任务 {} 配置的邮箱格式不正确: {}", task.getTaskName(), sendEmail);
            return;
        }

        try {
            String title = buildAlertTitle(task);
            String content = buildAlertContent(task, taskLog);
            
            boolean success = adminNotifyService.sendEmail(sendEmail, title, content);
            if (success) {
                log.info("任务失败告警邮件发送成功，任务: {}, 邮箱: {}", task.getTaskName(), sendEmail);
            } else {
                log.error("任务失败告警邮件发送失败，任务: {}, 邮箱: {}", task.getTaskName(), sendEmail);
            }
        } catch (Exception e) {
            log.error("发送任务失败告警邮件异常，任务: {}, 邮箱: {}", task.getTaskName(), sendEmail, e);
        }
    }

    @Override
    public boolean isValidEmailFormat(String emails) {
        if (StringTools.isBlank(emails)) {
            return false;
        }

        // 支持分号分隔的多个邮箱
        String[] emailArray = emails.split(";");
        for (String email : emailArray) {
            email = email.trim();
            if (StringTools.isBlank(email)) {
                continue;
            }
            
            // 支持钉钉和企业微信格式
            if (email.endsWith("@dd.com") || email.endsWith("@qywx.com")) {
                continue;
            }
            
            // 验证普通邮箱格式
            if (!EMAIL_PATTERN.matcher(email).matches()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 构建告警邮件标题
     */
    private String buildAlertTitle(AdminTaskDO task) {
        String systemName = adminProperties.getSystemName();
        if (StringTools.isBlank(systemName)) {
            systemName = "系统";
        }
        return String.format("%s - 任务执行失败告警: %s", systemName, task.getTaskName());
    }

    /**
     * 构建告警邮件内容
     */
    private String buildAlertContent(AdminTaskDO task, AdminTaskLogDO taskLog) {
        StringBuilder content = new StringBuilder();
        content.append("任务编码:").append(task.getTaskCode());
        content.append(",执行时间:").append(task.getCreateTime());
        content.append(",执行耗时:").append(taskLog.getCostMs());
        content.append(",错误信息:").append(taskLog.getErrorMsg());
        
        return content.toString();
    }
}
