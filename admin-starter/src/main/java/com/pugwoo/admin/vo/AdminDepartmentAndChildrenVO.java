package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminDepartmentDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

/**
 * description:
 *      部门节点及其子部门
 * <AUTHOR>
 * @date 2018-05-03
 */
@Data
public class AdminDepartmentAndChildrenVO extends AdminDepartmentDO {

    @RelatedColumn(localColumn = "id", remoteColumn = "parent_id")
    private List<AdminDepartmentAndChildrenVO> children;

}
