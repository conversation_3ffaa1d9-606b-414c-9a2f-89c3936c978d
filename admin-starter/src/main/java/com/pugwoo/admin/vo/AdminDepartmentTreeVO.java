package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminDepartmentDO;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.wooutils.tree.ITreeNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门树形结构
 * <AUTHOR>
 * @date 2018-05-02
 */
@Data
public class AdminDepartmentTreeVO extends AdminDepartmentDO implements ITreeNode {
	
	@RelatedColumn(localColumn = "id", remoteColumn = "department_id")
	private List<AdminUserDO> users;

    private List<AdminDepartmentTreeVO> children;
    
	public int getUserCount() {
		return users == null ? 0 : users.size();
	}

	@Override
	public String getNodeId() {
		return super.getId() == null ? "" : super.getId().toString();
	}

	@Override
	public String getNodeParentId() {
		return super.getParentId() == null ? "" : super.getParentId().toString();
	}

    @Override
    public List<AdminDepartmentTreeVO> getChildren() {
        return this.children;
    }

    @Override
    public void initChildren() {
        children = new ArrayList<>();
    }

}
