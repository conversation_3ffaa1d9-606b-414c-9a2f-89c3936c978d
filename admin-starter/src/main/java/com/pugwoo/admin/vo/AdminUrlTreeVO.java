package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminUrlDO;
import com.pugwoo.wooutils.tree.ITreeNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * url树形结构
 */
@Data
public class AdminUrlTreeVO extends AdminUrlDO implements ITreeNode {

	private List<AdminUrlTreeVO> children;
	
	private boolean isScanned; // 是否是扫描的
	
	private boolean isInScan; // 扫描中是否有此链接
	
	@Override
	public String getNodeId() {
		return super.getId() == null ? "" : super.getId().toString();
	}

	@Override
	public String getNodeParentId() {
		return super.getParentId() == null ? "" : super.getParentId().toString();
	}
	
	@Override
	public List<AdminUrlTreeVO> getChildren() {
		return children;
	}
	
	@Override
	public void initChildren() {
		if(this.children == null) this.children = new ArrayList<>();
	}

}
