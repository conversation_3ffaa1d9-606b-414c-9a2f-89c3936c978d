package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminDepartmentDO;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-05-03
 */
@Data
public class AdminUserRelatedDepartmentVO extends AdminUserDO {

    @RelatedColumn(localColumn = "department_id", remoteColumn = "id")
    private AdminDepartmentDO departmentDO;

    public String getDepartmentName() {
    	return departmentDO == null ? "" : departmentDO.getName();
    }

}
