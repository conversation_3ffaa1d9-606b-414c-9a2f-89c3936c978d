package com.pugwoo.admin.vo;

import com.pugwoo.wooutils.tree.ITreeNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class MenuDTO implements ITreeNode {

	private String title;
	
	private String icon;
	
	private String url;
	
	private List<MenuDTO> subs; // subs为null表示没有，subs不为null时，url值无效
	
	transient private AdminUrlDTO attachment; // 不参与json序列化
	
	@Override
	public String getNodeId() {
		return attachment.getId() == null ? "" : attachment.getId().toString();
	}

	@Override
	public String getNodeParentId() {
		return attachment.getParentId() == null ? "" : attachment.getParentId().toString();
	}

	@Override
	public List<? extends ITreeNode> getChildren() {
		return subs;
	}

	@Override
	public void initChildren() {
		this.subs = new ArrayList<>();
	}
	
	@Override
	public Integer getSeq() {
		return attachment.getSeq();
	}

}
