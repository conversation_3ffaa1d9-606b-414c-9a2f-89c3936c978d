package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminRoleDO;
import com.pugwoo.admin.entity.AdminRoleUrlDO;
import com.pugwoo.admin.entity.AdminUrlDO;
import com.pugwoo.admin.entity.AdminUserRoleDO;
import com.pugwoo.dbhelper.annotation.JoinLeftTable;
import com.pugwoo.dbhelper.annotation.JoinRightTable;
import com.pugwoo.dbhelper.annotation.JoinTable;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.dbhelper.enums.JoinTypeEnum;
import lombok.Data;

@Data
@JoinTable(joinType = JoinTypeEnum.JOIN, on = "t1.role_id=t2.role_id")
public class UserRoleUrlJoinVO {

	@Data
	public static class AdminRoleUrlVO extends AdminRoleUrlDO {
		
		@RelatedColumn(localColumn = "url_id", remoteColumn = "id")
		private AdminUrlDO adminUrlDO;

		@RelatedColumn(localColumn = "role_id", remoteColumn = "id")
		private AdminRoleDO adminRoleDO;
	}

	@JoinLeftTable
	private AdminUserRoleDO adminUserRoleDO;
	
	@JoinRightTable
	private AdminRoleUrlVO adminRoleUrlVO;

	public String getRoleCode() {
		AdminRoleDO adminRoleDO = adminRoleUrlVO.getAdminRoleDO();
		if (adminRoleDO == null || adminRoleDO.getCode() == null) {
			return "";
		}
		return adminRoleDO.getCode();
	}

}
