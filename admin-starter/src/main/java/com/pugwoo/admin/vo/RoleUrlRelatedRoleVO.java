package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminRoleDO;
import com.pugwoo.admin.entity.AdminRoleUrlDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-04-20
 */
@Data
public class RoleUrlRelatedRoleVO extends AdminRoleUrlDO {

    @RelatedColumn(localColumn = "role_id", remoteColumn = "id")
    private AdminRoleDO adminRoleDO;

}
