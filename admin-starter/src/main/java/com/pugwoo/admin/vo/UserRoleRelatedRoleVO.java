package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminRoleDO;
import com.pugwoo.admin.entity.AdminUserRoleDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-04-27
 */
@Data
public class UserRoleRelatedRoleVO extends AdminUserRoleDO {

    @RelatedColumn(localColumn = "role_id", remoteColumn = "id")
    private AdminRoleDO adminRoleDO;

}
