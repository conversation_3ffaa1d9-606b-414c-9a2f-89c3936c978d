package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminRoleDO;
import com.pugwoo.admin.entity.AdminRoleUrlDO;
import com.pugwoo.admin.entity.AdminUrlDO;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.entity.AdminUserRoleDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

@Data
public class AdminRoleVO extends AdminRoleDO {

	@Data
	public static class AdminRoleUrlAndUrlVO extends AdminRoleUrlDO {

		@RelatedColumn(localColumn = "url_id", remoteColumn = "id")
		private AdminUrlDO adminUrlDO;

		public String getType() {
			return adminUrlDO == null ? "" : adminUrlDO.getType();
		}

	}

	@Data
	public static class AdminUserRoleAndUserVO extends AdminUserRoleDO {

		@RelatedColumn(localColumn = "user_id", remoteColumn = "id")
		private AdminUserDO adminUserDO;

	}

	@RelatedColumn(localColumn = "id", remoteColumn = "role_id")
	private List<AdminRoleUrlAndUrlVO> urls;
	
	@RelatedColumn(localColumn = "id", remoteColumn = "role_id")
	private List<AdminUserRoleAndUserVO> users;

}
