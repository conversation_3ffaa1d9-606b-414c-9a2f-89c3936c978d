package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.entity.AdminUserRoleDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-04-24
 */
@Data
public class UserRoleRelatedUserVO extends AdminUserRoleDO{

    @RelatedColumn(localColumn = "user_id", remoteColumn = "id")
    private AdminUserDO adminUserDO;

}
