package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminUrlDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

import java.util.List;

/**
 * description:
 *      URL节点的所有父类及所有子类
 * <AUTHOR>
 * @date 2018-04-24
 */
@Data
public class AdminUrlChildrenVO extends AdminUrlDO {
	
    @RelatedColumn(localColumn = "id", remoteColumn = "parent_id")
    private List<AdminUrlChildrenVO> children;

}
