package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminRoleUrlDO;
import com.pugwoo.admin.entity.AdminUrlDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-04-24
 */
@Data
public class RoleUrlRelatedUrlVO extends AdminRoleUrlDO{

    @RelatedColumn(localColumn = "url_id", remoteColumn = "id")
    private AdminUrlDO adminUrlDO;

}
