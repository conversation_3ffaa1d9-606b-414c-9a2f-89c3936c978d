package com.pugwoo.admin.vo;

import com.pugwoo.admin.entity.AdminUrlDO;
import lombok.Data;

/**
 * AdminUrlDO的部分，将缓存到redis，抽取出来是为了减少redis内存占用
 */
@Data
public class AdminUrlDTO {
	
	private Long id;
	
	private Long parentId;
	
	private String type;
	
	private String url;
	
	private String name;
	
	private String icon;
	
	private Integer seq;

	public static AdminUrlDTO from(AdminUrlDO adminUrlDO) {
		AdminUrlDTO adminUrlDTO = new AdminUrlDTO();
		adminUrlDTO.setId(adminUrlDO.getId());
		adminUrlDTO.setParentId(adminUrlDO.getParentId());
		adminUrlDTO.setType(adminUrlDO.getType());
		adminUrlDTO.setUrl(adminUrlDO.getUrl());
		adminUrlDTO.setName(adminUrlDO.getName());
		adminUrlDTO.setIcon(adminUrlDO.getIcon());
		adminUrlDTO.setSeq(adminUrlDO.getSeq());
		return adminUrlDTO;
	}

}
