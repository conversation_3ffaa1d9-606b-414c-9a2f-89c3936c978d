package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-05-04
 */
@Data
@Table("t_admin_log_slow_sql")
public class AdminLogSlowSqlDO extends AdminCoreDO {

    /**用户id<br/>Column: [user_id]*/
    @Column("user_id")
    private Long userId;

    /**用户名<br/>Column: [user_name]*/
    @Column("user_name")
    private String userName;

    /**请求url，含参数<br/> Column: [url]*/
    @Column("url")
    private String url;

    /**是否已经查看<br/>Column: [is_read]*/
    @Column("is_read")
    private Boolean read;

    /**查询语句<br/>Column: [sql]*/
    @Column(value = "sql", maxStringLength = 4096)
    private String sql;

    /**查询参数<br/>
     *   需将参数列表 List<Object> 转化为String存储
     * Column: [sql_param]
     */
    @Column("sql_param")
    private String sqlParam;

    /**查询时间<br/>Column: [sql_time]*/
    @Column("sql_time")
    private Long sqlTime;

}
