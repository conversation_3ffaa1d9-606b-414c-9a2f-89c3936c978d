package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * url对应的角色控制表
 */
@Data
@Table("t_admin_role_url")
public class AdminRoleUrlDO extends AdminBaseDO {

    // 角色id
    @Column(value = "role_id")
    private Long roleId;

    // url id
    @Column(value = "url_id")
    private Long urlId;

}
