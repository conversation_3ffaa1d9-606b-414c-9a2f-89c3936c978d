package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-05-02
 */
@Data
@Table("t_admin_department")
public class AdminDepartmentDO extends AdminBaseDO{

    /**部门名称*/
    @Column("name")
    private String name;

    /**父级id，0为最顶层*/
    @Column("parent_id")
    private Long parentId;

    /**描述*/
    @Column("description")
    private String description;

    /**排序*/
    @Column("seq")
    private Integer seq;

    /**webkey 由前端生成的webKey*/
    @Column("webkey")
    private String webkey;

}
