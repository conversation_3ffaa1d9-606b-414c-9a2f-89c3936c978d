package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * 操作记录表
 * <AUTHOR>
 */
@Data
@Table("t_admin_log")
public class AdminLogDO extends AdminCoreDO {
	
	/**操作用户id*/
	@Column("user_id")
	private Long userId;
	
	/**操作用户名*/
	@Column("user_name")
	private String userName;
	
	/**操作安全级别 SecurityLevelEnum*/
	@Column("security_level")
	private String securityLevel;
	
	/**操作类型 OperateTypeEnum */
	@Column("operate_type")
	private String operateType;
	
	/**操作用户ip*/
	@Column(value = "ip", maxStringLength = 256)
	private String ip;
	
	/**操作说明*/
	@Column("remark")
	private String remark;
	
}
