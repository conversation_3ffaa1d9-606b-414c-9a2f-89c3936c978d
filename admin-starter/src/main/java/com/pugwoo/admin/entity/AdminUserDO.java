package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * 管理员用户DO
 * <AUTHOR>
 */
@Data
@Table("t_admin_user")
public class AdminUserDO extends AdminBaseDO {

    /**是否已禁用*/
    @Column(value = "disabled")
    private Boolean disabled;
	
	/**登录用户名*/
	@Column("user_name")
	private String userName;
	
	/**密码*/
	@Column("password")
	private String password;
	
	/**是否管理员*/
	@Column("is_admin")
	private Boolean isAdmin;
	
	/**手机号*/
	@Column("phone")
	private String phone;
	
	/**电子邮箱email*/
	@Column("email")
	private String email;
	
	/**真实姓名*/
	@Column("real_name")
	private String realName;
	
	/**公司*/
	@Column("company")
	private String company;
	
	/**部门*/
	@Column("department_id")
	private Long departmentId;
	
	/**职位*/
	@Column("position")
	private String position;

	/**备注*/
	@Column("remark")
	private String remark;
	
	/**个性签名*/
	@Column("signature")
	private String signature;

	/**来源
	 * @see com.pugwoo.admin.enums.UserSourceEnum
	 */
	@Column("source")
	private String source;

}
