package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("t_admin_role")
public class AdminRoleDO extends AdminBaseDO {

	@Column("code")
	private String code; // 角色编码，用于系统代码层标识
	
	@Column("name")
	private String name; // 角色名称

	@Column("description")
	private String description; // 角色描述
	
	@Column("role_group")
	private String roleGroup; // 角色分组
	
}
