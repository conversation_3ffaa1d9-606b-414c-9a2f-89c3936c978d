package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.util.Map;

@Data
@Table("t_admin_dict_value")
public class AdminDictValueDO extends AdminBaseDO {

    // 字典id
    @Column(value = "dict_id")
    private Long dictId;

    // 字典代号，冗余
    @Column(value = "dict_code")
    private String dictCode;

    // 值名称
    @Column(value = "name")
    private String name;

    // 值代号，会被程序引用，小心修改
    @Column(value = "code")
    private String code;

    // 排序
    @Column(value = "seq")
    private Integer seq;

    // 描述
    @Column(value = "description")
    private String description;
    
    // 额外信息，这里用json存
    @Column(value = "extra", isJSON = true)
    private Map<String, Object> extra;
    
}
