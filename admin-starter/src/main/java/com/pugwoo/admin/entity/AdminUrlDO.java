package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("t_admin_url")
public class AdminUrlDO extends AdminBaseDO {

	// 父级，目录和菜单的父级一定是目录。约定parentId为null或0表示根目录下的
    @Column(value = "parent_id")
    private Long parentId;

    // 权限类型：文件夹、菜单、其它，枚举：UrlTypeEnum
    @Column(value = "type")
    private String type;

    // url，当目录时为空，菜单时必须是明确url，其它可以是url正则表达式
    @Column(value = "url")
    private String url;

    // 标题
    @Column(value = "name")
    private String name;
    
    // 图标 css class
    @Column(value = "icon")
    private String icon;

    // 描述
    @Column(value = "description")
    private String description;

    // 排序，从小到大
    @Column(value = "seq")
    private Integer seq;
    
    // 由前端生成的唯一key，非必须
    @Column(value = "webkey")
    private String webkey;

}
