package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-04-28
 */
@Data
@Table("t_admin_log_exception")
public class AdminLogExceptionDO extends AdminCoreDO {

    /**用户id<br/>Column: [user_id]*/
    @Column("user_id")
    private Long userId;

    /**用户名<br/>Column: [user_name]*/
    @Column("user_name")
    private String userName;

    /**类名+方法名<br/>Column: [class_method]*/
    @Column("class_method")
    private String classAndMethod;

    @Column("request_method")
    private String requestMethod;

    /**请求url，含参数<br/> Column: [url]*/
    @Column("url")
    private String url;

    /**referer<br/> Column: [referer]*/
    @Column("referer")
    private String referer;

    /**请求Body <br/>Column: [params]*/
    @Column("params")
    private String params;

    /**请求ip<br/>Column: [ip]*/
    @Column(value = "ip", maxStringLength = 256)
    private String ip;

    /**异常类型<br/>Column: [type]*/
    @Column("type")
    private String exceptionType;

    /**异常信息<br/>Column: [msg]*/
    @Column(value = "msg", maxStringLength = 20000)
    private String exceptionMsg;

    /**是否已经查看<br/>Column: [is_read]*/
    @Column("is_read")
    private Boolean read;

}
