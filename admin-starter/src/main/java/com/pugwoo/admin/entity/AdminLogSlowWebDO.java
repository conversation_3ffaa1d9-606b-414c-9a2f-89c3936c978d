package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-05-04
 */
@Data
@Table("t_admin_log_slow_web")
public class AdminLogSlowWebDO extends AdminCoreDO {

    /**用户id<br/>Column: [user_id]*/
    @Column("user_id")
    private Long userId;

    /**用户名<br/>Column: [user_name]*/
    @Column("user_name")
    private String userName;

    @Column("request_method")
    private String requestMethod;

    /**请求url，含参数<br/> Column: [url]*/
    @Column("url")
    private String url;

    /**referer<br/> Column: [referer]*/
    @Column("referer")
    private String referer;

    /**请求ip<br/>Column: [ip]*/
    @Column(value = "ip", maxStringLength = 256)
    private String ip;

    /**是否已经查看<br/>Column: [is_read]*/
    @Column("is_read")
    private Boolean read;

    /**请求-响应时间<br/>Column: [request_time]*/
    @Column("request_time")
    private Long requestTime;

}
