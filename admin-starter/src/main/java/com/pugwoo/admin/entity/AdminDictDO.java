package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * 数据字典
 */
@Data
@Table("t_admin_dict")
public class AdminDictDO extends AdminBaseDO {

    // 字典名称
    @Column(value = "name")
    private String name;

    // 字典代号，会被程序引用，修改小心
    @Column(value = "code")
    private String code;

    // 描述信息
    @Column(value = "description")
    private String description;

}
