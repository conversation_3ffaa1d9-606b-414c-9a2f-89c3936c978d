package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 记录任务的执行情况表
 */
@Data
@ToString
@Table("t_admin_task_log")
public class AdminTaskLogDO extends AdminCoreDO {

    /** 关联的任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 耗时(毫秒)<br/>Column: [cost_ms] */
    @Column(value = "cost_ms")
    private Integer costMs;

    /** 触发类型<br/>Column: [trigger_type] */
    @Column(value = "trigger_type")
    private String triggerType;

    /** 任务名称<br/>Column: [task_name] */
    @Column(value = "task_name", maxStringLength = 255)
    private String taskName;

    /** 任务编号，任务的唯一标识，可以指定，没有指定时值为class_name.method_name<br/>Column: [task_code] */
    @Column(value = "task_code",maxStringLength = 400)
    private String taskCode;

    /** 执行的类的名称<br/>Column: [class_name] */
    @Column(value = "class_name", maxStringLength = 255)
    private String className;

    /** 执行的方法名称<br/>Column: [method_name] */
    @Column(value = "method_name", maxStringLength = 128)
    private String methodName;

    /** 如果是定时任务有cron表达式，则记录这里<br/>Column: [cron_expression] */
    @Column(value = "cron_expression")
    private String cronExpression;

    /** 如果任务有固定的频率，fixed和delay都记录在这里<br/>Column: [fix_rate_ms] */
    @Column(value = "fix_rate_ms")
    private Integer fixRateMs;

    /** 任务执行状态<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 运行的机器ip<br/>Column: [run_ip] */
    @Column(value = "run_ip", maxStringLength = 128)
    private String runIp;

    /** 任务参数<br/>Column: [args] */
    @Column(value = "args", maxStringLength = 1024)
    private String args;

    /** 任务执行超时时间（秒）<br/>Column: [timeout_second] */
    @Column(value = "timeout_second")
    private Integer timeoutSecond;

    /** 任务报错信息<br/>Column: [error_msg] */
    @Column(value = "error_msg", insertValueScript = "''", maxStringLength = 65000)
    private String errorMsg;

}