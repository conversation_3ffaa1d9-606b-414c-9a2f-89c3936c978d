package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminCoreDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Table("t_admin_task")
public class AdminTaskDO extends AdminCoreDO  {

    /** 任务名称<br/>Column: [task_name] */
    @Column(value = "task_name")
    private String taskName;

    /** 任务编号，任务的唯一标识，可以指定，没有指定时值为class_name.method_name<br/>Column: [task_code] */
    @Column(value = "task_code")
    private String taskCode;

    /** 控制该定时任务是否禁用，值为1为禁用，null或0不禁用<br/>Column: [ctrl_disabled] */
    @Column(value = "ctrl_disabled")
    private Boolean ctrlDisabled;

    /** 任务备注<br/>Column: [note] */
    @Column(value = "note")
    private String note;

    /** 分号隔开，错误时发送邮件；复用后也支持钉钉/企业微信<br/>Column: [send_email] */
    @Column(value = "send_email")
    private String sendEmail;

    /** 任务当前是否在运行<br/>Column: [is_running] */
    @Column(value = "is_running")
    private Boolean isRunning;

    /** 执行的类的名称<br/>Column: [class_name] */
    @Column(value = "class_name")
    private String className;

    /** 执行的方法名称<br/>Column: [method_name] */
    @Column(value = "method_name")
    private String methodName;

    /** 如果是定时任务有cron表达式，则记录这里<br/>Column: [cron_expression] */
    @Column(value = "cron_expression")
    private String cronExpression;

    /** 如果任务有固定的频率，fixed和delay都记录在这里<br/>Column: [fix_rate_ms] */
    @Column(value = "fix_rate_ms")
    private Integer fixRateMs;

    /** 任务执行超时时间（秒）<br/>Column: [timeout_second] */
    @Column(value = "timeout_second")
    private Integer timeoutSecond;

    /** 最后一次是否运行成功<br/>Column: [is_last_success] */
    @Column(value = "is_last_success")
    private Boolean isLastSuccess;

    /** 最后一次运行时间<br/>Column: [last_time] */
    @Column(value = "last_time")
    private LocalDateTime lastTime;

    /** 最后一次运行失败时间<br/>Column: [last_error_time] */
    @Column(value = "last_error_time")
    private LocalDateTime lastErrorTime;

    /** 成功运行次数<br/>Column: [count_success] */
    @Column(value = "count_success")
    private Integer countSuccess;

    /** 失败运行次数<br/>Column: [count_error] */
    @Column(value = "count_error")
    private Integer countError;

}