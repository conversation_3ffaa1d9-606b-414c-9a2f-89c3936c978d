package com.pugwoo.admin.entity;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.util.Date;

@Data
@Table("t_admin_user_role")
public class AdminUserRoleDO extends AdminBaseDO {
	
    // admin userId
    @Column(value = "user_id")
    private Long userId;

    // 角色id
    @Column(value = "role_id")
    private Long roleId;

    // 过期时间，空则表示用不过期
    @Column(value = "expire_time")
    private Date expireTime;

}
