package com.pugwoo.admin.web.interceptor;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.service.AdminLogService;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 校验的异常处理器，对应于Controller的例子，一共有3种异常
 * @date 2018-08-02 创建，2019年4月19日 16:33:09优化
 */
@Slf4j
@RestControllerAdvice /*这个等价于@ControllerAdvice+@ResponseBody*/
public class ValidationExceptionHandler {

    @Autowired
    private AdminLogService logService;

    private volatile long lastConcurrentSendTime = 0;

    /**AdminInnerException异常统一在这里处理*/
    @ExceptionHandler(AdminInnerException.class)
    public WebJsonBean handleAdminInnerException(AdminInnerException ex, HttpServletRequest request,
                                                 HandlerMethod handlerMethod) {
        int code = ex.getCode();
        if(code < 0) {
            logService.addException(ex, request, handlerMethod);
        }

        if (code == AdminErrorCode.EXCEED_CONCURRENT_LIMIT.getCode()) {
            // 推送告警，每分钟只告警一次
            boolean shouldSend = false;
            synchronized (this) {
                if (System.currentTimeMillis() - lastConcurrentSendTime> 60 * 1000) {
                    lastConcurrentSendTime = System.currentTimeMillis();
                    shouldSend = true;
                }
            }
            if (shouldSend) {
                logService.sendExceptionNotifyToAdmin("系统触发并发数限制", ex.getMessage() + "【本消息每分钟最多发送1条，请持续关注系统运行情况】");
            }
        }

        String message = ex.getErrMsg();
        if(message == null) {
            message = ex.getMessage();
        }
        return WebJsonBean.fail(ex.getCode(), message);
    }

    @ExceptionHandler(BindException.class)
    public WebJsonBean bindExceptionHandler(BindException ex) {
        String message = getMessage(ex.getAllErrors());
        if(message == null) {
            message = ex.getMessage();
        }
        return WebJsonBean.fail(AdminErrorCode.ILLEGAL_PARAMETERS, message);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public WebJsonBean handleBindException(MethodArgumentNotValidException ex) {
        String message = getMessage(ex.getBindingResult().getAllErrors());
        if(message == null) {
            message = ex.getMessage();
        }
        return WebJsonBean.fail(AdminErrorCode.ILLEGAL_PARAMETERS, message);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public WebJsonBean handleValidate(ConstraintViolationException ex) {
        return WebJsonBean.fail(AdminErrorCode.ILLEGAL_PARAMETERS, ex.getMessage());
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public WebJsonBean httpNotSupportMethod(HttpRequestMethodNotSupportedException ex) {
        return WebJsonBean.fail(AdminErrorCode.ILLEGAL_PARAMETERS, ex.getMessage());
    }

    /**
     * 组装错误信息
     * @param errors
     * @return 当没有错误时，返回null
     */
    private String getMessage(List<ObjectError> errors) {
        if(errors == null || errors.isEmpty()) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (ObjectError error : errors) {
            if(sb.length() > 0) {sb.append("; ");}
            if(error instanceof FieldError) {
                FieldError fe = (FieldError) error;
                sb.append(fe.getField()).append(fe.getDefaultMessage());
            } else {
                sb.append(error.getDefaultMessage());
            }
        }
        return sb.toString();
    }

}
