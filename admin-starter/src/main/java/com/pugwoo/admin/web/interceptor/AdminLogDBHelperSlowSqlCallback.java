package com.pugwoo.admin.web.interceptor;

import com.pugwoo.admin.entity.AdminLogSlowSqlDO;
import com.pugwoo.admin.service.AdminLogService;
import com.pugwoo.dbhelper.DBHelperSlowSqlCallback;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.NetUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-05-06
 */
@Component
public class AdminLogDBHelperSlowSqlCallback implements DBHelperSlowSqlCallback {

    @Autowired(required = false)
    private HttpServletRequest request;
    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public void callback(long executeMsTime, String sql, List<Object> args, int batchSize) {
        AdminLogSlowSqlDO adminLogSlowSqlDO = new AdminLogSlowSqlDO();

        AdminUserLoginContext context = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
        if (context != null) {
            adminLogSlowSqlDO.setUserId(context.getUserId());
            adminLogSlowSqlDO.setUserName(context.getUserName());
        }
        try {
            adminLogSlowSqlDO.setUrl(NetUtils.getFullUrlWithParamForJakarta(request));
        } catch (Throwable e) { //ignore exception
        }

        adminLogSlowSqlDO.setSql(sql);
        adminLogSlowSqlDO.setSqlTime(executeMsTime);
        String sqlParam = JSON.toJson(args);
        if (sqlParam.length() > 1024) {
            sqlParam = sqlParam.substring(0, 1024);
        }
        adminLogSlowSqlDO.setSqlParam(sqlParam);
        adminLogSlowSqlDO.setRead(false);

        adminLogSlowSqlDO.setCreateTime(new Date());
        adminLogSlowSqlDO.setUpdateTime(new Date());

        AdminLogService adminLogService = applicationContext.getBean(AdminLogService.class);
        adminLogService.addSlowSql(adminLogSlowSqlDO);
    }
}
