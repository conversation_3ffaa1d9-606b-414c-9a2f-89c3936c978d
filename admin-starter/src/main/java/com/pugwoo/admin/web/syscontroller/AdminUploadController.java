package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.IUploadUtils;
import com.pugwoo.admin.utils.NoPermission;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 上传文件，约定前端上传文件的<input type="file" name="file"> name为file
 * 支持同时多个文件上传。
 * 
 * 上传文件名：/年月日/uuid_原始文件名
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin_upload")
public class AdminUploadController {
	
	@Autowired(required = false)
	private IUploadUtils uploadUtils;

	/**
	 * 返回数据是上传的url的数组
	 * @param file
	 */
	@NoPermission
	@RequestMapping(value="/upload")
	public WebJsonBean upload(@RequestParam MultipartFile[] file) throws IOException {
		
		if(uploadUtils == null) {
			return WebJsonBean.fail(AdminErrorCode.NO_UPLOAD_PROVIDER);
		}
		if(file == null || file.length == 0) {
			return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "请选择文件后上传");
		}
		
		String ym = DateUtils.format(new Date(), "yyyyMMdd");
		List<String> urls = new ArrayList<>();
		for(MultipartFile f : file) {
			String originalFilename = f.getOriginalFilename();
			String uuid = UUID.randomUUID().toString().replace("-", "");
			String url = uploadUtils.upload(f.getInputStream(),
					"/" + ym + "/" + uuid + "_" + encode(originalFilename));
			if(url != null) {
				urls.add(url);
			}
		}
		if(urls.isEmpty()) {
			return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "上传失败");
		}
		return WebJsonBean.ok(MapUtils.of("url", urls));
	}
	
	private static String encode(String str) {
		if(str == null || str.isEmpty()) return "";
		return str;
	}
	
}
