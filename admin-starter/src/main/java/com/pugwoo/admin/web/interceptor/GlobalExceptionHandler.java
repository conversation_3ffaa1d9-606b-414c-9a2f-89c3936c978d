package com.pugwoo.admin.web.interceptor;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.admin.bean.ErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.service.AdminLogService;
import com.pugwoo.wooutils.json.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 处理全局mvc的异常。
 *
 * <AUTHOR>
 */
@Component
public class GlobalExceptionHandler implements HandlerExceptionResolver {

    @Autowired
    private AdminLogService logService;
    @Autowired
    private AdminProperties adminProperties;

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler,
                                         Exception ex) {
        response.addHeader("Content-Type", "application/json;charset=UTF-8");

        // 由于AdminInnerException已经在ValidationExceptionHandler处理了，所以这里不会再处理到AdminInnerException
        logService.addException(ex, request, (handler instanceof HandlerMethod) ? (HandlerMethod)handler : null);

        // 仅处理有注解了@ResponseBody且返回值不会byte[]类型的方法，也即json渲染输出的方法
        if(handler instanceof HandlerMethod) {
            ResponseBody responseBody = ((HandlerMethod) handler).getMethodAnnotation(ResponseBody.class);
            RestController restController = ((HandlerMethod) handler).getBeanType().getAnnotation(RestController.class);
            Class<?> returnType = ((HandlerMethod) handler).getMethod().getReturnType();
            if ((responseBody != null || restController != null) && returnType != byte[].class) {
                WebJsonBean<?> webJsonBean = null;
                boolean isShowExceptionDetailMsg = adminProperties.getIsShowExceptionDetailMsg() != null
                        && adminProperties.getIsShowExceptionDetailMsg();
                String exMsg = ex.getMessage();
                if (ex instanceof DataAccessException) { // 各种特殊异常的处理
                    webJsonBean = WebJsonBean.fail(AdminErrorCode.DB_ERROR,
                            isShowExceptionDetailMsg ? AdminErrorCode.DB_ERROR.getName() + ": " + exMsg :
                                    AdminErrorCode.DB_ERROR.getName());
                } else if (ex instanceof ErrorCode) {
                    webJsonBean = WebJsonBean.fail(((ErrorCode) ex).getCode(), ex.getMessage());
                } else {
	                webJsonBean = WebJsonBean.fail(AdminErrorCode.SYSTEM_ERROR,
                            isShowExceptionDetailMsg ? AdminErrorCode.SYSTEM_ERROR.getName() + ": " + exMsg :
                                    AdminErrorCode.SYSTEM_ERROR.getName());
                }

                String json = JSON.toJson(webJsonBean);
                try {
                    PrintWriter writer = response.getWriter();
                    writer.write(json);
                    writer.flush();
                } catch (IOException ignored) {
                } catch (IllegalStateException e) {
                    try {
                        ServletOutputStream out = response.getOutputStream();
                        out.write(json.getBytes());
                        out.flush();
                    } catch (Exception ignored2) {
                    }
                }
                return new ModelAndView(); // 不需要vm渲染
            }
        }

        // 对于其它的错误，由页面渲染
        ModelAndView modelAndView = new ModelAndView("error");
        if (ex instanceof AdminInnerException) {
            modelAndView.addObject("errorCode", ((AdminInnerException) ex).getCode());
            modelAndView.addObject("errorMsg", ex.getMessage());
        } else {
            modelAndView.addObject("errorMsg", ex.getMessage());
        }

        return modelAndView;
    }

}
