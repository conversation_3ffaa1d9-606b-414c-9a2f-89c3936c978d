package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminRoleDO;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.service.AdminRolePermissionService;
import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.vo.UserRoleRelatedRoleVO;
import com.pugwoo.admin.vo.UserRoleRelatedUserVO;
import com.pugwoo.bootwebext.JsonParam;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-04-23
 */
@Permission(value = "AdminUserRoleRead", name = "系统用户角色查看权限")
@RestController
@RequestMapping("admin_user_role")
public class AdminUserRoleController {

    @Autowired
    private AdminRolePermissionService rolePermissionService;
    @Autowired
    private AdminUserService userService;

    @GetMapping("userListByRoleId")
    public WebJsonBean getUserListByRoleId(Long roleId) {
        WebCheckUtils.assertNotNull(roleId, "缺少roleId");
        AdminRoleDO adminRoleDO = rolePermissionService.getRoleById(roleId);
        WebCheckUtils.assertNotNull(adminRoleDO, "roleId=" + roleId + "的记录为空");

        List<UserRoleRelatedUserVO> userRoleRelatedUserVOList = rolePermissionService.getUserListByRoleId(roleId);
        List<AdminUserDO> userDOList = userService.getAllUser();

        //拼装roleUserList
        List<Map<String, Object>> roleUserList;
        roleUserList = ListUtils.transform(userRoleRelatedUserVOList, o -> {
            Map<String, Object> map = new HashMap<>();
            map.put("userId", o.getAdminUserDO().getId());
            map.put("nickname", o.getAdminUserDO().getUserName());
            map.put("realName", o.getAdminUserDO().getRealName());
            return map;
        });

        //拼装allUserList
        List<Map<String, Object>> allUserList = new ArrayList<>(roleUserList);
        List<Map<String, Object>> tempRoleUserList = new ArrayList<>(roleUserList);
        for (AdminUserDO user : userDOList) {
            boolean addFlag = true;
            for (Map<String, Object> map : tempRoleUserList) {
                if (Objects.equals(map.get("userId"), user.getId())) {
                    addFlag = false;
                    tempRoleUserList.remove(map);
                    break;
                }
            }
            if (addFlag) {
                Map<String, Object> map = new HashMap<>();
                map.put("userId", user.getId());
                map.put("nickname", user.getUserName());
                map.put("realName", user.getRealName());
                allUserList.add(map);
            }
        }

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("roleId", roleId);
        result.put("userList", roleUserList);
        result.put("allUserList", allUserList);

        return WebJsonBean.ok(result);
    }

    @Permission(value = "AdminUserRoleWrite", name = "系统用户角色操作权限")
    @PostMapping("saveRoleUsers")
    public WebJsonBean saveRoleUsers(Long roleId, @JsonParam("roleUsers") List<Long> roleUserList){
        WebCheckUtils.assertNotNull(roleId, "缺少roleId");
        AdminRoleDO adminRoleDO = rolePermissionService.getRoleById(roleId);
        WebCheckUtils.assertNotNull(adminRoleDO, "roleId=" + roleId + "的记录为空");

        boolean success = rolePermissionService.saveByRoleIdAndUserIds(roleId, roleUserList);

        if(success) {
            userService.refreshAllUserUrls();
        }

        return success ? WebJsonBean.ok()
                : WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR);
    }

    @GetMapping("roleListByUserId")
    public WebJsonBean getRoleListByUserId(Long userId) {
        WebCheckUtils.assertNotNull(userId, "缺少userId");
        AdminUserDO adminUserDO = userService.getUserById(userId);
        WebCheckUtils.assertNotNull(adminUserDO, "userId = " + userId +" 的记录为空");

        List<UserRoleRelatedRoleVO> userRoleRelatedRoleVOList = rolePermissionService.getRoleListByUserId(userId);
        List<AdminRoleDO> allRoleDOList = rolePermissionService.getAllRoles();

        //拼装userRoleList
        List<Map<String, Object>> userRoleList;
        userRoleList = ListUtils.transform(userRoleRelatedRoleVOList, o -> {
            Map<String, Object> map = new HashMap<>();
            map.put("roleId", o.getRoleId());
            map.put("roleName", o.getAdminRoleDO().getName());
            return map;
        });
        //userRoleIds
        List<Long> userRoleIds = ListUtils.transform(userRoleRelatedRoleVOList, o -> o.getRoleId());

        //拼装allRoleList
        List<Map<String, Object>> allRoleList = new ArrayList<>(userRoleList);
        List<Map<String, Object>> tempRoleUserList = new ArrayList<>(userRoleList);
        for (AdminRoleDO role : allRoleDOList) {
            boolean addFlag = true;
            for (Map<String, Object> map : tempRoleUserList) {
                if (Objects.equals(map.get("roleId"), role.getId())) {
                    addFlag = false;
                    tempRoleUserList.remove(map);
                    break;
                }
            }
            if (addFlag) {
                Map<String, Object> map = new HashMap<>();
                map.put("roleId", role.getId());
                map.put("roleName", role.getName());
                allRoleList.add(map);
            }
        }

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("userRoleIds", userRoleIds);
        result.put("allRoleList", allRoleList);

        return WebJsonBean.ok(result);
    }

    @Permission(value = "AdminUserRoleWrite", name = "系统用户角色操作权限")
    @PostMapping("saveUserRoles")
    public WebJsonBean saveUserRoles(Long userId, @JsonParam("userRoles") List<Long> userRoleList){
        WebCheckUtils.assertNotNull(userId, "缺少userId");

        AdminUserDO adminUserDO = userService.getUserById(userId);
        WebCheckUtils.assertNotNull(adminUserDO, "userId = " + userId + " 的记录为空");

        boolean success = rolePermissionService.saveByUserIdAndRoleIds(userId, userRoleList);

        if (success) {
            userService.refreshAllUserUrls(); // 刷新用户权限
        }

        return success ? WebJsonBean.ok()
                : WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR);
    }
}
