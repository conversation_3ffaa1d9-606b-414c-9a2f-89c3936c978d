package com.pugwoo.admin.web.weblog;


import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.entity.AdminLogWebDO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.string.StringTools;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * 打印请求和响应信息，默认不开启（但是这个filter还是会加载和执行，只是打印log部分被关闭而已）
 *
 * <a href="https://stackoverflow.com/questions/33744875/spring-boot-how-to-log-all-requests-and-responses-with-exceptions-in-single-pl">来源代码</a>
 */
@WebFilter(urlPatterns = {"/*"}, filterName = "commonWebLogFilter")
public class CommonWebLogFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(CommonWebLogFilter.class);

    private static final ThreadLocal<String> requestUuid = new ThreadLocal<>();

    public static ThreadLocal<String> loginUsername = new ThreadLocal<>();

    private final ExecutorService writeLogPool = ThreadPoolUtils.createThreadPool(2, 1000000, 2, "WriteLog");

    @Autowired
    private AdminProperties adminProperties;

    @Autowired(required = false)
    private List<DBHelper> allDBHelpers;

    @Autowired
    @Qualifier("adminDBHelper")
    private DBHelper dbHelper;

    @Value("${admin.jdbc.withRequestUuid:true}")
    private Boolean sqlWithRequestUuid;

    @Autowired(required = false)
    private IWebLogCallback webLogCallback;

    /**
     * 特殊接口打印日志开关，逗号隔开
     */
    @Value("${admin.webLogApis:}")
    private String debugApis;
    private final Map<String, Boolean> debugApiMap = new ConcurrentHashMap<>();

    @Override
    public void init(FilterConfig filterConfig) {
        if (StringTools.isNotBlank(debugApis)) {
            String[] apis = debugApis.split(",");
            for (String api : apis) {
                debugApiMap.put(api.trim(), true);
            }
        }
    }

    /**
     * 获取请求uuid
     */
    public static String getRequestUuid() {
        return requestUuid.get();
    }

    /**
     * 所有http请求进入过滤器
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        String uuid = "";
        try { // 确保trace uuid不影响业务
            uuid = UUID.randomUUID().toString();
            uuid = uuid.substring(uuid.length() - 12);
            requestUuid.set(uuid);
            MDC.put("requestUuid", "「" + uuid + "」");

            if (sqlWithRequestUuid != null && sqlWithRequestUuid) {
                if (ListUtils.isNotEmpty(allDBHelpers)) {
                    DBHelper.setLocalComment("requestUuid:" + uuid);
                }
            }
        } catch (Throwable e) {
            logger.error("generate trace uuid fail", e);
        }

        long start = System.currentTimeMillis();

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 排除掉swagger自身接口
        String servletPath = httpRequest.getServletPath();
        if (servletPath != null && (servletPath.startsWith("/swagger-resources") ||
                servletPath.equalsIgnoreCase("/doc.html") ||
                servletPath.equalsIgnoreCase("/v2/api-docs")||
                servletPath.startsWith("/webjars") ||
                servletPath.startsWith("/actuator") ||
                servletPath.contains("springfox"))) {
            chain.doFilter(request, response);
            return;
        }

        boolean isCallback = adminProperties.getEnableWebLogCallback() != null && adminProperties.getEnableWebLogCallback() && webLogCallback != null;
        boolean isWriteDB = adminProperties.getEnableWebLogToDB() != null && adminProperties.getEnableWebLogToDB();

        String fullUrl = NetUtils.getFullUrlWithParamForJakarta(httpRequest);
        if (isCallback) {
            try {
                webLogCallback.begin(uuid, fullUrl);
            } catch (Throwable e) {
                logger.error("webLogCallback.begin fail, url:{}", fullUrl, e);
            }
        }
        Future<AdminLogWebDO> insertWebDB = null;
        if (isWriteDB) {
            insertWebDB = newLog(fullUrl, uuid);
        }

        RepeatedlyReadRequestWrapper requestWrapper = null;
        WebResponseWrapper responseWrapper = null;

        boolean isDebug = adminProperties.getEnableWebLog() != null && adminProperties.getEnableWebLog();
        if (!isDebug) {
            isDebug = debugApiMap.get(httpRequest.getRequestURI()) != null;
        }

        RequestDataDTO requestDataDTO = null;
        if (isCallback || isWriteDB) {
            requestDataDTO = new RequestDataDTO();
            requestDataDTO.setRequestUuid(uuid);
        }

        StringBuilder log = isDebug ? new StringBuilder() : null;
        if (isDebug || isCallback || isWriteDB) {
            requestWrapper = new RepeatedlyReadRequestWrapper(httpRequest);
            responseWrapper = new WebResponseWrapper(httpResponse);
            log = new StringBuilder();
            logBegin(httpRequest, httpResponse, requestWrapper, log, requestDataDTO, fullUrl);
        }

        try {
            if(isCallback) {
                try {
                    webLogCallback.startProcess(requestDataDTO);
                } catch (Throwable e) {
                    logger.error("webLogCallback.startProcess fail, url:{}", fullUrl, e);
                }
            }
            if (isWriteDB) {
                insertWebDB = updateLog(insertWebDB, requestDataDTO);
            }

            chain.doFilter(requestWrapper != null ? requestWrapper : request,
                    responseWrapper != null ? responseWrapper : response);
            if (responseWrapper != null) {
                responseWrapper.getWriter().flush();
            }
        } finally {
            try { // 确保结束逻辑不影响业务
                // 打印请求结束日志
                long cost = System.currentTimeMillis() - start;
                if (isDebug || isCallback || isWriteDB) {
                    logEnd(httpRequest, httpResponse, responseWrapper, log, cost, requestDataDTO);
                }
                if(isDebug) {
                    logger.info("\n{}", log);
                }
                if (isCallback) {
                    try {
                        webLogCallback.complete(requestDataDTO);
                    } catch (Throwable e) {
                        logger.error("webLogCallback.complete fail, url:{}", fullUrl, e);
                    }
                }
                if (isWriteDB) {
                    updateLogForResp(insertWebDB, requestDataDTO, loginUsername.get());
                }

                MDC.put("requestUuid", ""); // 清除MDC
            } catch (Throwable e) {
                logger.error("post handle CommonWebLogFilter fail", e);
            }
        }
    }

    private Future<AdminLogWebDO> newLog(String fullUrl, String uuid) {
        return writeLogPool.submit(() -> {
            AdminLogWebDO adminLogWebDO = new AdminLogWebDO();
            adminLogWebDO.setUrl(fullUrl);
            adminLogWebDO.setRequestUuid(uuid);
            dbHelper.insert(adminLogWebDO);
            return adminLogWebDO;
        });
    }

    private Future<AdminLogWebDO> updateLog(Future<AdminLogWebDO> f, RequestDataDTO requestDataDTO) {
        return writeLogPool.submit(() -> {
            AdminLogWebDO adminLogWebDO = f.get();
            adminLogWebDO.setClientIp(requestDataDTO.getRemoteIp());
            adminLogWebDO.setRequestMethod(requestDataDTO.getRequestMethod());
            adminLogWebDO.setRequestBody(requestDataDTO.getRequestBody());
            dbHelper.update(adminLogWebDO);
            return adminLogWebDO;
        });
    }

    private void updateLogForResp(Future<AdminLogWebDO> f, RequestDataDTO requestDataDTO, String username) {
        writeLogPool.submit(() -> {
            AdminLogWebDO adminLogWebDO = null;
            try {
                adminLogWebDO = f.get();
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
            adminLogWebDO.setResponseBody(requestDataDTO.getResponseBody());
            adminLogWebDO.setUsername(username);
            adminLogWebDO.setCostMs(requestDataDTO.getCostMs());
            adminLogWebDO.setResponseBody(requestDataDTO.getResponseBody());
            dbHelper.update(adminLogWebDO);
        });
    }

    @AllArgsConstructor
    @Data
    private static class UploadFileData {
        private String name;
        private long fileLength;
    }

    private void logBegin(HttpServletRequest httpRequest, HttpServletResponse httpResponse,
                          RepeatedlyReadRequestWrapper requestWrapper,
                          StringBuilder log, RequestDataDTO requestDataDTO, String fullUrl) {

        String remoteIp = NetUtils.getRemoteIpForJakarta(httpRequest);
        String requestMethod = httpRequest.getMethod();
        String protocol = httpRequest.getProtocol();
        Map<String, String> requestHeaders = getRequestHeaders(httpRequest);
        String boundary = null; // 不为null表示上传
        List<UploadFileData> uploads = new ArrayList<>();

        String multipartHeader = httpRequest.getHeader("content-type");
        if (multipartHeader != null && multipartHeader.startsWith("multipart/form-data")) {
            try {
                boundary = "-------------------------------------------boundary";
                int boundaryIndex = multipartHeader.indexOf("boundary=");
                if(boundaryIndex >= 0) {
                    boundary = multipartHeader.substring(boundaryIndex + 9);
                }
                Collection<Part> parts = httpRequest.getParts();
                for(Part part : parts) {
                    UploadFileData d = new UploadFileData(part.getName(), part.getSize());
                    uploads.add(d);
                }
            } catch (Throwable e) {
                logger.error("get multipart data fail, url:{}", fullUrl, e);
            }
        }

        if (log != null) {
            log.append("┌─────────────────────── Request from ").append(remoteIp).append(" ──────────────────────────\n");
            log.append("│").append(requestMethod).append(" ").append(fullUrl).append(" ").append(protocol).append("\n");
            for (Map.Entry<String, String> e : requestHeaders.entrySet()) {
                log.append("│").append(e.getKey()).append(": ").append(e.getValue()).append("\n");
            }

            if (boundary != null) {
                log.append("│\n");
                for(UploadFileData data : uploads) {
                    log.append("│").append(boundary).append("\n");
                    log.append("│Content-Disposition: form-data; name=\"").append(data.getName()).append("\"\n");
                    log.append("│\n");
                    log.append("│FILE SIZE: ").append(data.getFileLength()).append(" BYTES\n");
                    log.append("│").append(boundary).append("--\n");
                }
            } else if (requestWrapper.isPostQueryString()) { // 解析POST queryString
                log.append("│\n");
                log.append("│").append(buildQueryString(httpRequest)).append("\n");
            } else {
                String body = getBody(requestWrapper);
                if(!body.isEmpty()) {
                    log.append("│\n");
                    log.append(body);
                }
            }
        }

        if (requestDataDTO != null) {
            requestDataDTO.setRemoteIp(remoteIp);
            requestDataDTO.setRequestMethod(requestMethod);
            requestDataDTO.setUrl(fullUrl);
            requestDataDTO.setProtocol(protocol);
            requestDataDTO.setRequestHeaders(requestHeaders);
            if (boundary != null) {
                requestDataDTO.setIsUploadFile(true);
                long size = 0;
                for(UploadFileData data : uploads) {
                    size += data.getFileLength();
                }
                requestDataDTO.setUploadFileSize(size);
            } else if (requestWrapper.isPostQueryString()) { // 解析POST queryString
                requestDataDTO.setIsUploadFile(false);
                requestDataDTO.setRequestBody(buildQueryString(httpRequest));
            } else {
                requestDataDTO.setIsUploadFile(false);
                requestDataDTO.setRequestBody(getBodyRaw(requestWrapper));
            }
        }
    }

    private String buildQueryString(HttpServletRequest httpRequest) {
        Enumeration<String> names = httpRequest.getParameterNames();
        StringBuilder sb = new StringBuilder();
        while(names.hasMoreElements()) {
            String name = names.nextElement();
            String[] values = httpRequest.getParameterValues(name);
            if(values == null || values.length == 0) {
                sb.append(name).append("=").append("&");
            } else {
                for(int i = 0; i < values.length; i++) {
                    String value = values[i].replace("&", "%26");
                    sb.append(name).append("=").append(value).append("&");
                }
            }
        }
        return sb.isEmpty() ? "" : sb.substring(0, sb.length() - 1);
    }

    /**不区分大小写*/
    private static String getHeaderValue(Map<String, String> headers, String key) {
        if (headers == null || key == null) {
            return null;
        }
        for (Map.Entry<String, String> e : headers.entrySet()) {
            if (key.equalsIgnoreCase(e.getKey())) {
                return e.getValue();
            }
        }
        return null;
    }

    private void logEnd(HttpServletRequest httpRequest, HttpServletResponse httpResponse, WebResponseWrapper responseWrapper, StringBuilder log, long cost, RequestDataDTO requestDataDTO) {

        Map<String, String> responseHeaders = getResponseHeaders(httpResponse);
        boolean isBinary;
        long binaryLength = responseWrapper.getLength();
        String responseBody = null;

        // https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Complete_list_of_MIME_types
        String contentType = responseWrapper.getContentType();
        String contentDisposition = getHeaderValue(responseHeaders, "Content-Disposition");
        if(contentType != null && (contentType.startsWith("text/")
                || contentType.contains("html") || contentType.contains("json")
                || contentType.contains("xml")) && StringTools.isBlank(contentDisposition)) {
            isBinary = false;
            responseBody = responseWrapper.getContent();
        } else {
            isBinary = true;
        }

        if (log != null) {
            log.append("├─────────────────────── Response cost ").append(cost)
                    .append(" ms───────────────────────────────\n");
            for (Map.Entry<String, String> e : responseHeaders.entrySet()) {
                log.append("│").append(e.getKey()).append(": ").append(e.getValue()).append("\n");
            }
            log.append("│\n");
            if (isBinary) {
                log.append("│binary data, byte[] length: ").append(binaryLength).append("\n");
            } else {
                String[] lines = StringTools.splitLines(responseBody);
                for(String line : lines) {
                    log.append("│").append(line).append("\n");
                }
            }
            log.append("└─────────────────────────────────────────────────────────────────────────\n");
        }

        if (requestDataDTO != null) {
            requestDataDTO.setCostMs(cost);
            requestDataDTO.setResponseHeaders(responseHeaders);
            requestDataDTO.setIsResponseBinary(isBinary);
            requestDataDTO.setResponseBytesLength(binaryLength);
            if (!isBinary) {
                requestDataDTO.setResponseBody(responseBody);
            }
        }
    }

    private Map<String, String> getRequestHeaders(HttpServletRequest httpRequest) {
        Map<String, String> map = new HashMap<>();

        Enumeration<String> headerNames = httpRequest.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String value = httpRequest.getHeader(headerName);
            map.put(headerName, value);
        }

        return map;
    }

    private Map<String, String> getResponseHeaders(HttpServletResponse httpResponse) {
        Map<String, String> map = new HashMap<>();

        Collection<String> headerNames = httpResponse.getHeaderNames();
        for(String header : headerNames) {
            String value = httpResponse.getHeader(header);
            map.put(header, value);
        }

        return map;
    }

    private String getBodyRaw(HttpServletRequest httpRequest) {
        StringBuilder sb = new StringBuilder();
        BufferedReader br = null;
        try {
            br = httpRequest.getReader();
            String str;
            while ((str = br.readLine()) != null) {
                sb.append(str).append("\n");
            }
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
            } catch (Exception e) {
                // ignore
            }
        }
        return sb.toString();
    }

    private String getBody(HttpServletRequest httpRequest) {
        StringBuilder sb = new StringBuilder();
        BufferedReader br = null;
        try {
            br = httpRequest.getReader();
            String str;
            while ((str = br.readLine()) != null) {
                sb.append("│").append(str).append("\n");
            }
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        } finally {
            try {
                if (br != null) {
                    br.close();
                }
            } catch (Exception e) {
                // ignore
            }
        }
        return sb.toString();
    }

    @Override
    public void destroy() {
    }
}
