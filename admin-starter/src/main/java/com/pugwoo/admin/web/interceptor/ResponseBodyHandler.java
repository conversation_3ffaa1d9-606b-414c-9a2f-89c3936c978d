package com.pugwoo.admin.web.interceptor;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.bootwebext.bean.DownloadBean;
import com.pugwoo.bootwebext.bean.StreamDownloadBean;
import org.springframework.core.MethodParameter;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.HashSet;
import java.util.Set;

/**
 * 统一为返回数据加上code和msg，以后不再需要在controller中写WebJsonBean
 */
@RestControllerAdvice
public class ResponseBodyHandler implements ResponseBodyAdvice<Object> {

    //不支持的返回值类型列表
    private static final Set<Class<?>> NO_SUPPORTED_CLASSES = new HashSet<>();

    static {
        NO_SUPPORTED_CLASSES.add(WebJsonBean.class); // 历史原因，这个类不需要再处理
        NO_SUPPORTED_CLASSES.add(DownloadBean.class); // 自定义的下载文件类，不需要再处理
        NO_SUPPORTED_CLASSES.add(StreamDownloadBean.class); // 自定义的下载文件类，不需要再处理

        NO_SUPPORTED_CLASSES.add(String.class);
        NO_SUPPORTED_CLASSES.add(byte[].class);
        NO_SUPPORTED_CLASSES.add(Resource.class);
        NO_SUPPORTED_CLASSES.add(javax.xml.transform.Source.class);

        NO_SUPPORTED_CLASSES.add(ResponseEntity.class); // Spring原生ResponseEntity，不处理
        NO_SUPPORTED_CLASSES.add(ModelAndView.class);
        NO_SUPPORTED_CLASSES.add(Model.class);
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        if (NO_SUPPORTED_CLASSES.contains(returnType.getParameterType())) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        return WebJsonBean.ok(body);
    }

}
