package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminDictDO;
import com.pugwoo.admin.entity.AdminDictValueDO;
import com.pugwoo.admin.service.AdminDictService;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.vo.AdminDictVO;
import com.pugwoo.bootwebext.JsonParam;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据字典
 */
@Permission(value = "AdminDictRead", name = "数据字典查看权限")
@RestController
@RequestMapping("/admin_dict")
public class AdminDictController {
	
	@Autowired
	private AdminDictService adminDictService;

	@GetMapping("get_page")
	public Map<String, Object> listData(int page, int pageSize, String name, String code) {
		PageData<AdminDictVO> pageData = adminDictService.getPage(page, pageSize, name, code);
		return PageUtils.trans(pageData, o -> {
				Map<String, Object> map = new HashMap<>();
				map.put("id", o.getId());
				map.put("name", o.getName());
				map.put("code", o.getCode());
				map.put("description", o.getDescription());
				map.put("valuesCount", o.getValues().size());
				ListUtils.sortAscNullLast(o.getValues(), a -> a.getSeq());
				map.put("values", ListUtils.transform(o.getValues(), a -> {
					Map<String, Object> m = new HashMap<>();
					m.put("id", a.getId());
					m.put("name", a.getName());
					m.put("code", a.getCode());
					m.put("description", a.getDescription());
					m.put("extra", a.getExtra());
					return m;
				}));
				return map;
		});
	}

	@Permission(value = "AdminDictWrite", name = "数据字典操作权限")
    @PostMapping("/add_or_update")
    public AdminDictDO addOrUpdate(Long id, String name, String code, String description) {
        WebCheckUtils.assertNotBlank(name, "字典名称不能为空");
        WebCheckUtils.assertNotBlank(code, "字典代号不能为空");
        if(adminDictService.isConflict(id, name, code)) {
			throw new AdminInnerException(AdminErrorCode.COMMON_BIZ_ERROR, "字典名称或代号已存在");
        }
        
        AdminDictDO adminDictDO = new AdminDictDO();
        adminDictDO.setId(id);
        adminDictDO.setName(name);
        adminDictDO.setCode(code);
        adminDictDO.setDescription(description);
        
        adminDictDO = adminDictService.insertOrUpdate(adminDictDO);
        return adminDictDO;
    }

	@Permission(value = "AdminDictWrite", name = "数据字典操作权限")
    @PostMapping("/delete")
    public WebJsonBean<?> delete(Long id) {
    	WebCheckUtils.assertNotNull(id, "缺少id");
    	adminDictService.delete(id);
    	return WebJsonBean.ok();
    }

	@Permission(value = "AdminDictWrite", name = "数据字典操作权限")
    @PostMapping("/edit_values")
    public WebJsonBean<?> editValues(Long id, String code, @JsonParam("values") List<AdminDictValueDO> values) {
    	WebCheckUtils.assertNotNull(id, "缺少id");
    	WebCheckUtils.assertNotBlank(code, "缺少code");
    	WebCheckUtils.assertNotNull(values, "缺少值values");
    	
    	values = ListUtils.filter(values, o -> 
    	    StringUtils.isNotBlank(o.getName()) && StringUtils.isNotBlank(o.getCode()));
    	boolean succ = adminDictService.batchInsertUpdateValues(id, code, values);
    	return succ ? WebJsonBean.ok() : WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "保存失败");
    }

}
