package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.entity.AdminLogExceptionDO;
import com.pugwoo.admin.entity.AdminLogSlowSqlDO;
import com.pugwoo.admin.entity.AdminLogSlowWebDO;
import com.pugwoo.admin.service.AdminLogService;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.bootwebext.JsonParam;
import com.pugwoo.dbhelper.model.PageData;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description:
 *
 * <AUTHOR>
 * Date 2018-04-28
 */
@Permission(value = "AdminLogRead", name = "系统运行日志查看权限")
@RestController
@RequestMapping("admin_log")
public class AdminLogController {

    @Autowired
    private AdminLogService logService;
    @Resource
    private AdminProperties adminProperties;

//==========异常 start==========

    /**
     * 获取异常记录信息
     *      时间逆序
     * @param page  页数
     * @param pageSize  每页记录数
     * @param onlyUnRead  是否只看未读
     * @return  记录信息
     */
    @GetMapping("exception/get_page")
    public WebJsonBean<?> getLogExceptions(int page, int pageSize,
            @JsonParam("dateRange") List<Date> dateRange,
            Boolean onlyUnRead) {

        PageData<AdminLogExceptionDO> pageData = logService.getExceptionForPage(page, pageSize, onlyUnRead, dateRange);

        Map<String, Object> result = PageUtils.trans(pageData, o -> {
            Map<String, Object> map = new HashMap<>();
            map.put("logExceptionId", o.getId());
            map.put("userId", o.getUserId());
            map.put("userName", o.getUserName());
            map.put("classMethod", o.getClassAndMethod());
            map.put("requestMethod", o.getRequestMethod());
            map.put("url", o.getUrl());
            map.put("referer", o.getReferer());
            map.put("params", o.getParams());
            map.put("ip", o.getIp());
            map.put("type", o.getExceptionType());
            map.put("msg", o.getExceptionMsg());
            map.put("isRead", o.getRead());
            map.put("createTime", o.getCreateTime());
            map.put("updateTime", o.getUpdateTime());
            return map;
        });

        result.put("notifyException", adminProperties.getNotifyException());
        result.put("maxExceptionNotifyPerDay", adminProperties.getMaxExceptionNotifyPerDay());

        return WebJsonBean.ok(result);
    }

    @PostMapping("exception/read")
    public WebJsonBean<?> setLogExceptionRead(@RequestParam("logExceptionId") Long logId){
        WebCheckUtils.assertNotNull(logId, "logExceptionId参数缺失");

        AdminLogExceptionDO adminLogExceptionDO = logService.getExceptionById(logId);
        WebCheckUtils.assertNotNull(adminLogExceptionDO, "logExceptionId = " + logId + "的记录不存在");

        boolean success = logService.setExceptionRead(adminLogExceptionDO);
        return success ? WebJsonBean.ok() :
             WebJsonBean.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    @PostMapping("exception/readAll")
    public WebJsonBean<?> setLogExceptionReadAll() {
        boolean success = logService.setExceptionReadAll();
        return success ? WebJsonBean.ok()
                : WebJsonBean.fail(AdminErrorCode.SYSTEM_ERROR);
    }

//==========异常 end==========
//==========慢web start==========

    /**
     * 获取异常记录信息
     *      时间逆序
     * @param page  页数
     * @param pageSize  每页记录数
     * @param onlyUnRead  是否只看未读
     * @return  记录信息
     */
    @GetMapping("slowweb/get_page")
    public WebJsonBean<?> getLogSlowWebs(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            @JsonParam("dateRange") List<Date> dateRange,
            Boolean onlyUnRead) {

        PageData<AdminLogSlowWebDO> pageData = logService.getSlowWebForPage(page, pageSize, onlyUnRead, dateRange);

        Map<String, Object> result = PageUtils.trans(pageData, o -> {
            Map<String, Object> map = new HashMap<>();
            map.put("logSlowWebId", o.getId());
            map.put("userId", o.getUserId());
            map.put("userName", o.getUserName());
            map.put("requestMethod", o.getRequestMethod());
            map.put("url", o.getUrl());
            map.put("referer", o.getReferer());
            map.put("ip", o.getIp());
            map.put("requestTime", o.getRequestTime());
            map.put("isRead", o.getRead());
            map.put("createTime", o.getCreateTime());
            map.put("updateTime", o.getUpdateTime());
            return map;
        });

        return WebJsonBean.ok(result);
    }

    @PostMapping("slowweb/read")
    public WebJsonBean<?> setLogSlowWebRead(@RequestParam("logSlowWebId") Long logId){
        WebCheckUtils.assertNotNull(logId, "logSlowWebId参数缺失");

        AdminLogSlowWebDO logSlowWebDO = logService.getSlowWebById(logId);
        WebCheckUtils.assertNotNull(logSlowWebDO, "logSlowWebId = " + logId + "的记录不存在");

        boolean success = logService.setSlowWebRead(logSlowWebDO);
        return success ? WebJsonBean.ok() :
                WebJsonBean.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    @PostMapping("slowweb/readAll")
    public WebJsonBean<?> setLogSlowwebReadAll() {
        boolean success = logService.setSlowWebReadAll();
        return success ? WebJsonBean.ok()
                : WebJsonBean.fail(AdminErrorCode.SYSTEM_ERROR);
    }

//==========慢web end==========
//==========慢sql start==========

    /**
     * 获取异常记录信息
     *      时间逆序
     * @param page  页数
     * @param pageSize  每页记录数
     * @param onlyUnRead  是否只看未读
     * @return  记录信息
     */
    @GetMapping("slowsql/get_page")
    public WebJsonBean<?> getLogSqlWebs(int page, int pageSize,
            @JsonParam("dateRange") List<Date> dateRange,
            Boolean onlyUnRead) {

        PageData<AdminLogSlowSqlDO> pageData = logService.getSlowSqlForPage(page, pageSize, onlyUnRead, dateRange);

        Map<String, Object> result = PageUtils.trans(pageData, o -> {
            Map<String, Object> map = new HashMap<>();
            map.put("logSlowSqlId", o.getId());
            map.put("userId", o.getUserId());
            map.put("userName", o.getUserName());
            map.put("url", o.getUrl());
            map.put("sql", o.getSql());
            map.put("sqlParam", o.getSqlParam());
            map.put("sqlTime", o.getSqlTime());
            map.put("isRead", o.getRead());
            map.put("createTime", o.getCreateTime());
            map.put("updateTime", o.getUpdateTime());
            return map;
        });

        return WebJsonBean.ok(result);
    }

    @PostMapping("slowsql/read")
    public WebJsonBean<?> setLogSlowSqlRead(@RequestParam("logSlowSqlId") Long logId){
        WebCheckUtils.assertNotNull(logId, "logSlowSqlId参数缺失");

        AdminLogSlowSqlDO logSlowSqlDO = logService.getSlowSqlById(logId);
        WebCheckUtils.assertNotNull(logSlowSqlDO, "logSlowSqlId = " + logId + "的记录不存在");

        boolean success = logService.setSlowSqlRead(logSlowSqlDO);
        return success ? WebJsonBean.ok() :
                WebJsonBean.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    @PostMapping("slowsql/readAll")
    public WebJsonBean<?> setLogSlowSqlReadAll() {
        boolean success = logService.setSlowSqlReadAll();
        return success ? WebJsonBean.ok()
                : WebJsonBean.fail(AdminErrorCode.SYSTEM_ERROR);
    }

//==========慢sql end==========
}
