package com.pugwoo.admin.web.interceptor;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.Tracer;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.admin.config.properties.AdminProperties;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class SentinelInterceptor implements HandlerInterceptor {

    private static final ThreadLocal<Entry> ENTRY_HOLDER = new ThreadLocal<>();
    /**
     * 用于记录rule是否已经初始化
     */
    private final ConcurrentHashMap<String, Boolean> ruleInitMap = new ConcurrentHashMap<>();

    @Autowired
    private AdminProperties adminProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if(!(handler instanceof HandlerMethod handlerMethod)) {
            return true; // 不处理，熔断仅处理方法请求
        }

        if (adminProperties.getRequestLimit() == null || adminProperties.getRequestLimit().getEnableConcurrentLimit() == null) {
            log.warn("admin.requestLimit.enableConcurrentLimit is null, please check requestLimit config");
            return true;
        }
        if (!adminProperties.getRequestLimit().getEnableConcurrentLimit()) {
            return true;
        }
        Integer maxConcurrent = adminProperties.getRequestLimit().getMaxConcurrent();
        if (maxConcurrent == null || maxConcurrent <= 0) {
            log.warn("invalid admin.requestLimit.maxConcurrent:{}, please check requestLimit config", maxConcurrent);
            return true;
        }

        String resourceName = handlerMethod.getMethod().toString();
        initDegradeRule(resourceName, maxConcurrent);

        try {
            ENTRY_HOLDER.set(SphU.entry(resourceName));
            return true;
        } catch (BlockException blockException) { // 被限流或降级时的处理
            throw new AdminInnerException(AdminErrorCode.EXCEED_CONCURRENT_LIMIT,
                    "当前请求服务超过系统最大并发数(" + maxConcurrent + ")，请稍后再试，接口：" + resourceName);
        } catch (Exception ex) {
            Tracer.trace(ex);
            return true; // sentinel的其它异常，记录但不影响主业务流程，避免sentinel成为主流程故障
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        Entry entry = ENTRY_HOLDER.get();
        if (entry != null) {
            entry.exit();
            ENTRY_HOLDER.remove();
        }
    }

    private synchronized void initDegradeRule(String resourceName, int maxConcurrent) {
        if (ruleInitMap.containsKey(resourceName)) {
            return;
        }

        synchronized (ruleInitMap) {
            if (ruleInitMap.containsKey(resourceName)) {
                return;
            }
            FlowRule rule = new FlowRule();
            rule.setResource(resourceName);
            rule.setGrade(RuleConstant.FLOW_GRADE_THREAD); // 设置控制模式为线程数
            rule.setCount(maxConcurrent); // 设置最大并发线程数

            FlowRuleManager.loadRules(Collections.singletonList(rule));
            ruleInitMap.put(resourceName, true);
        }
    }
}
