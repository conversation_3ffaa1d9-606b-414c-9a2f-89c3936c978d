package com.pugwoo.admin.web.interceptor;

import lombok.Data;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 自定义登陆处理器，用于实现不同的登陆方式。
 * 当Spring容器提供了CustomLoginHandler的实现类时，将会使用该实现类的登陆方法。
 */
public interface CustomLoginHandler {

    @Data
    class CustomLoginUserDTO {
        /**用户的唯一标识*/
        private String userName;
        /**用户的真实姓名，用于显示*/
        private String realName;
    }

    /**
     * 第三方方式获得当前登陆用户的唯一标识
     *
     * @param request 请求体
     * @param response 返回体
     * @return 登陆的用户名唯一标识
     */
    CustomLoginUserDTO getLoginUser(HttpServletRequest request, HttpServletResponse response);

}
