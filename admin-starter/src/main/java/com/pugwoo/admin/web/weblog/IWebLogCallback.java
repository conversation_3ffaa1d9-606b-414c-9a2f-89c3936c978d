package com.pugwoo.admin.web.weblog;

/**
 * weblog数据回调，将在请求对应事件时调用
 */
public interface IWebLogCallback {

    /**
     * 请求开始，只能获取到请求uuid和url
     * @param requestUuid
     * @param url
     */
    void begin(String requestUuid, String url);

    /**
     * 业务处理开始，此时只有请求的相关信息
     * @param requestDataDTO
     */
    void startProcess(RequestDataDTO requestDataDTO);

    /**
     * 业务处理完成
     * @param requestDataDTO
     */
    void complete(RequestDataDTO requestDataDTO);

}
