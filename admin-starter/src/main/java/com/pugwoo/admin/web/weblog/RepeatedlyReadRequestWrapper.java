package com.pugwoo.admin.web.weblog;

import com.pugwoo.wooutils.io.IOUtils;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * 自定义HttpServletRequest包装类，规避只能读取一次输入流的问题
 *
 * 说明：原来的WebRequestWrapper版本是网上最常见的版本，<br>
 * 但它不支持spring boot 2.2.x，原因是2.2.x以后的版本中，不像2.1.x会调一下request的parseParameter方法
 *
 * 这个版本能支持：
 * 1. GET queryString Spring这边支持对象和参数方式接收
 * 2. POST queryString Spring这边支持对象和参数方式接收
 * 3. POST json request body
 * 4. POST 上传文件
 * 5. GET 下载文件
 */
public class RepeatedlyReadRequestWrapper extends HttpServletRequestWrapper {

    private final byte[] body; // not null

    private final HttpServletRequest rawRequest;

    public RepeatedlyReadRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        this.rawRequest = request;

        // 上传文件和解析POST queryString是特殊的，交给tomcat自己去解析，后面再直接从Parts中拿来打log就好
        // 因此，这里对于大文件的上传，并不会有内存被撑爆的风险
        if (!isMultipart() && !isPostQueryString()) {
            body = IOUtils.readAllAndClose(request.getInputStream());
        } else {
            body = new byte[0];
        }
    }

    /**是否是上传文件*/
    public boolean isMultipart() {
        String contentType = rawRequest.getContentType();
        return contentType != null && contentType.startsWith("multipart/form-data");
    }

    /**是否是POST queryString*/
    public boolean isPostQueryString() {
        String method = rawRequest.getMethod();
        String contentType = rawRequest.getContentType();
        return "POST".equals(method) &&
               contentType != null && contentType.startsWith("application/x-www-form-urlencoded");
    }

    @Override
    public ServletInputStream getInputStream() {
        final ByteArrayInputStream byteArrayIns = new ByteArrayInputStream(body);
        ServletInputStream servletIns = new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }

            @Override
            public int read() throws IOException {
                return byteArrayIns.read();
            }
        };
        return servletIns;
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream(), getCharacterEncoding()));
    }

}