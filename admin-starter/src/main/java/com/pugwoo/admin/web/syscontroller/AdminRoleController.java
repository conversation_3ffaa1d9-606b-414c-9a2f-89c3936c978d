package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminRoleDO;
import com.pugwoo.admin.enums.UrlTypeEnum;
import com.pugwoo.admin.service.AdminRolePermissionService;
import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.vo.AdminRoleVO;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Permission(value = "AdminRoleUrlRead", name = "系统角色权限查看权限")
@RestController
@RequestMapping("/admin_role")
public class AdminRoleController {

    @Autowired
    private AdminRolePermissionService rolePermissionService;
    @Autowired
    private AdminUserService userService;

    @GetMapping("get_page")
    public WebJsonBean listData(
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
            String name, String roleGroup) {
        PageData<AdminRoleVO> pageData = rolePermissionService
                .getRolePage(page, pageSize, name, roleGroup);
        Map<String, Object> data = PageUtils.trans(pageData, o -> {
            Map<String, Object> map = new HashMap<>();
            map.put("roleId", o.getId());
            map.put("name", o.getName());
            map.put("code", o.getCode());
            map.put("description", o.getDescription());
            map.put("roleGroup", o.getRoleGroup());
            map.put("urlCount", o.getUrls().size());
            map.put("urlIds", ListUtils.transform(ListUtils.filter(o.getUrls(), a -> a.getAdminUrlDO() != null),
                    a -> a.getAdminUrlDO().getId()));
            map.put("urlList", ListUtils.transform(
            	ListUtils.filter(o.getUrls(), b -> // 过滤，只显示MENU的
            	   UrlTypeEnum.MENU.getCode().equals(b.getType())), a -> {
                Map<String, Object> m = new HashMap<>();
                if(a.getAdminUrlDO() == null) {return m;}
                m.put("urlName", a.getAdminUrlDO().getName());
                m.put("urlDescription", a.getAdminUrlDO().getDescription());
                return m;
            }));
            map.put("userCount", o.getUsers().size());
            map.put("userIds", ListUtils.transform(o.getUsers(), a -> a.getAdminUserDO().getId()));
            map.put("userList", ListUtils.transform(o.getUsers(), a -> {
                Map<String, Object> m = new HashMap<>();
                if(a.getAdminUserDO() == null) {return m;}
                m.put("userId", a.getAdminUserDO().getId());
                m.put("userName", a.getAdminUserDO().getUserName());
                m.put("realName", a.getAdminUserDO().getRealName());
                return m;
            }));
            return map;
        });
        return WebJsonBean.ok(data);
    }

    /**
     * 增加或编辑角色
     * @param roleId
     * @param name
     * @param code
     * @param description
     * @param roleGroup
     * @return
     */
    @Permission(value = "AdminRoleUrlWrite", name = "系统角色权限操作权限")
    @PostMapping("add_or_edit")
    public WebJsonBean addOrEdit(Long roleId, String name,
    		String code, String description, String roleGroup) {

        name = name.trim();
        code = code.trim();

    	WebCheckUtils.assertNotBlank(name, "角色名称不能为空");

        AdminRoleDO adminRoleDO;
        if (roleId != null) {
            adminRoleDO = rolePermissionService.getRoleById(roleId);
            WebCheckUtils.assertNotNull(adminRoleDO, "roleId=" + roleId + " 的角色不存在");
        } else {
            adminRoleDO = new AdminRoleDO();
        }
        
        //查询name/code是否重复
        boolean isExistName = rolePermissionService.isExistRoleName(adminRoleDO, name);
        if (isExistName) {
            return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR,
            		"角色名称为 " + name + " 的角色已存在");
        }
        boolean isExistCode = rolePermissionService.isExistRoleCode(adminRoleDO, code);
        if (isExistCode) {
            return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR,
            		"角色编号为 " + code + " 的角色已存在");
        }

        adminRoleDO.setName(name);
        adminRoleDO.setCode(code);
        adminRoleDO.setDescription(description);
        adminRoleDO.setRoleGroup(roleGroup);

        int row = rolePermissionService.insertOrUpdateRole(adminRoleDO);

        if(row > 0) {
            userService.refreshAllUserUrls(); // 新增修改角色也刷新权限
        }

        return row > 0 ? WebJsonBean.ok(row)
            : WebJsonBean.fail(AdminErrorCode.SYSTEM_ERROR);
    }

    /**
     * 删除角色
     * @param roleId
     * @return
     */
    @Permission(value = "AdminRoleUrlWrite", name = "系统角色权限操作权限")
    @PostMapping("delete")
    public WebJsonBean deleteRole(Long roleId) {
        if (roleId == null) {
            return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "需要提供角色ID");
        }

        boolean succ = rolePermissionService.deleteRoleById(roleId) > 0;
        if(succ) {
            userService.refreshAllUserUrls();
        }

        return succ ? WebJsonBean.ok() :
                WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "删除失败");
    }
}
