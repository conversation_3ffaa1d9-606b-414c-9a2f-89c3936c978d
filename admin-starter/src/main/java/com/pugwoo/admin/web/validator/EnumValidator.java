package com.pugwoo.admin.web.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Arrays;

/**
 * 检查用户传入的字符串值是否在枚举值中
 * <br/>
 * 注意，这里是匹配枚举的变量名称，而不是自定义的code，因此建议枚举变量名称和code保持一致，才能用这个校验
 */
public class EnumValidator implements ConstraintValidator<EnumValid, String> {

    private Class<?> enumClass;

    @Override
    public void initialize(EnumValid constraintAnnotation) {
        enumClass = constraintAnnotation.enumClass();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()){
            value = "";
        }
        try {
            Enum.valueOf((Class<Enum>) enumClass, value);
            return true;
        } catch (IllegalArgumentException e) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(
                    "Invalid enum value, should be one of " + Arrays.toString(enumClass.getEnumConstants()))
                    .addConstraintViolation();
            return false;
        }
    }

}
