package com.pugwoo.admin.web.tasklog;

import com.pugwoo.admin.entity.AdminTaskDO;
import com.pugwoo.admin.entity.AdminTaskLogDO;
import com.pugwoo.admin.enums.AdminTaskStatusEnum;
import com.pugwoo.admin.service.AdminTaskService;
import com.pugwoo.admin.utils.ClassUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Aspect
@Component
@Slf4j
public class TaskLogAOP {

    /**  采用线程池，主线程执行业务代码，遇到监控的逻辑新开线程去执行，保证事务不共用 */
    private final ExecutorService pool = Executors.newFixedThreadPool(1);

    @Autowired
    private AdminTaskService adminTaskService;

    @Around("@annotation(com.pugwoo.admin.web.tasklog.TaskLog) || @annotation(org.springframework.scheduling.annotation.Scheduled)")
    public Object aroundMethod(ProceedingJoinPoint joinpoint) throws Throwable {

        // 0. 如果是手工触发，那就直接执行，不再记录
        if (TaskLogContext.getIsTriggerByManual()) {
            return joinpoint.proceed();
        }

        //  1. 获取注解内的TaskName 获取切点的方法签名
        MethodSignature signature = (MethodSignature) joinpoint.getSignature();
        //  获取方法名称
        Method method = signature.getMethod();
        Object[] args = joinpoint.getArgs();

        TaskLog taskLog = method.getAnnotation(TaskLog.class);
        Scheduled scheduled = method.getAnnotation(Scheduled.class);

        long start = System.currentTimeMillis();

        //  2. 提前检查任务是否被禁用
        String taskCode = getTaskCode(method, taskLog);
        AdminTaskDO existingTask = adminTaskService.getByTaskCode(taskCode);

        if (existingTask != null && Boolean.TRUE.equals(existingTask.getCtrlDisabled())) {
            // 任务被禁用，直接记录跳过状态并返回
            pool.submit(() -> adminTaskService.recordSkippedTask(method, args, taskLog, scheduled, existingTask));
            return null; // 不执行实际任务，直接返回
        }

        //  3. 前置处理：插入TaskLogDO对象
        Future<AdminTaskLogDO> taskLogDO = pool.submit(() -> adminTaskService.generateNewTaskLog(method, args, taskLog, scheduled, AdminTaskStatusEnum.NEW));

        //  4. 调用接口方法(如果有异常，在下面的try-catch块中捕获、记录后再抛出)
        Object result;
        try {
            // 4.1 在开始执行任务前，将状态更新为RUNNING
            pool.submit(() -> adminTaskService.updateTaskStatusToRunning(taskLogDO));

            //  真正调用接口方法
            result = joinpoint.proceed();
            //  5. 成功调用时记录执行时长，回写DO
            pool.submit(() -> adminTaskService.updateSuccessfulTask(taskLogDO, start));
            //  6. 返回调用结果，监控只做记录
            return result;
        } catch (Throwable e) {
            //  如果catch到了异常，执行失败并获取对应的报错栈信息，回写Log对象
            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer, true));
            //  更新异常记录对象
            pool.submit(() -> adminTaskService.updateFailedTask(taskLogDO, writer.toString(), start));
            //  继续抛出异常，监控只做记录
            throw e;
        }
    }

    /**
     * 获取任务代码
     */
    private String getTaskCode(Method method, TaskLog taskLog) {
        if (taskLog != null && StringTools.isNotBlank(taskLog.taskCode())) {
            return taskLog.taskCode();
        } else {
            return method.getDeclaringClass().getName() + "." + ClassUtils.getMethodSignature(method);
        }
    }

}
