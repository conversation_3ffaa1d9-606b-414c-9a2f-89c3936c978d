package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminDepartmentDO;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.vo.AdminDepartmentAndChildrenVO;
import com.pugwoo.admin.vo.AdminDepartmentTreeVO;
import com.pugwoo.bootwebext.JsonParam;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Permission(value = "AdminUserDeptRead", name = "系统用户部门查看权限")
@RestController
@RequestMapping("/admin_user/department")
public class AdminUserDeptController {

	@Autowired
	private AdminUserService userService;

	/**
	 * 部门管理树形信息
	 * @return
	 */
	@GetMapping("get_tree")
	public WebJsonBean getDepartMentTree(){
		return WebJsonBean.ok(userService.getDepartmentTree());
	}

	/**
	 * 保存部门信息
	 * @param id
	 * @param name
	 * @param description
	 * @return
	 */
	@Permission(value = "AdminUserDeptWrite", name = "系统用户部门操作权限")
	@PostMapping("/save_node")
	public WebJsonBean saveNode(Long id, String name, String description) {
		WebCheckUtils.assertNotNull(id, "缺少id，无法保存");
		WebCheckUtils.assertNotBlank(name, "部门名称必须提供");
		name = name.trim();
		
		AdminDepartmentDO departmentDO = userService.getDepartmentById(id);
		WebCheckUtils.assertNotNull(departmentDO, "部门不存在");
		departmentDO.setName(name);
		departmentDO.setDescription(description);

		boolean success = userService.updateDepartment(departmentDO);
		return success ? WebJsonBean.ok()
				: WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "保存失败");
	}

	/**
	 * 保存树形节点
	 * @param departments
	 * @return
	 */
	@Permission(value = "AdminUserDeptWrite", name = "系统用户部门操作权限")
	@PostMapping("/save_tree")
	public WebJsonBean saveTree(@JsonParam("treeData") List<AdminDepartmentTreeVO> departments) {
		WebCheckUtils.assertNotNull(departments, "缺少树形数据");

		WebJsonBean checkTree = userService.checkDepartmentTree(departments);
		if (checkTree.getCode() != 0){
			return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, checkTree.getMsg());
		}
		boolean succ = userService.saveDepartmentTree(departments);
		return succ ? WebJsonBean.ok() : WebJsonBean.fail(
				AdminErrorCode.COMMON_BIZ_ERROR, "保存失败");
	}

	@Permission(value = "AdminUserDeptWrite", name = "系统用户部门操作权限")
	@PostMapping("/save_user_department")
	public WebJsonBean saveUserDepartment(Long userId, Long departmentId) {
		WebCheckUtils.assertNotNull(userId, "缺少userId，无法保存");
		WebCheckUtils.assertNotNull(departmentId, "缺少departmentId，无法保存");
		boolean success = userService.saveUserDepartment(userId, departmentId);
		return success ? WebJsonBean.ok()
				: WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "保存失败");
	}

	@GetMapping("/get_users")
	public WebJsonBean getDepartmentUsers(Long departmentId) {
		WebCheckUtils.assertNotNull(departmentId, "缺少departmentId，无法查询");
		List<AdminUserDO> userDOList = userService.getUserByDepartmentId(departmentId);
		List<Map<String, Object>> userList = ListUtils.transform(userDOList, o -> {
			Map<String, Object> user = new HashMap<>();
			user.put("userName", o.getUserName());
			user.put("userId", o.getId());
			user.put("realName", o.getRealName());
			return user;
		});

		AdminDepartmentAndChildrenVO adminDepartmentAndChildrenVO =
				userService.getDepartmentAndChildrenVO(departmentId);
		List<Long> selfAndChildrenIds = userService.getSelfAndChildrenIds(adminDepartmentAndChildrenVO);
		List<Long> childrenIds = ListUtils.subtract(selfAndChildrenIds, ListUtils.newArrayList(departmentId));
		List<AdminUserDO> childrenUserDOList = userService.getUserByDepartmentIds(childrenIds);
		List<Map<String, Object>> childrenUserList = ListUtils.transform(childrenUserDOList, o -> {
			Map<String, Object> user = new HashMap<>();
			user.put("userName", o.getUserName());
			user.put("userId", o.getId());
            user.put("realName", o.getRealName());
			return user;
		});

		Map<String, Object> result = new HashMap<>();
		result.put("departmentId", departmentId);
		result.put("userList", userList);
		result.put("childrenUserList", childrenUserList);

		return WebJsonBean.ok(result);
	}
	
}
