package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.RedisKeyNamespace;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.enums.OperateTypeEnum;
import com.pugwoo.admin.enums.SecurityLevelEnum;
import com.pugwoo.admin.service.AdminLogService;
import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.utils.CaptchaUtils;
import com.pugwoo.admin.utils.NotRequireLogin;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContext;
import com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor;
import com.pugwoo.wooutils.net.CookieUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.RedisLimitParam;
import com.pugwoo.wooutils.redis.RedisLimitPeriodEnum;
import com.pugwoo.wooutils.string.StringTools;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.UUID;

@Controller // 下面有渲染方法
@RequestMapping("admin_login")
public class AdminLoginController {

    private static final Integer REDIS_CAPTCHA_EXPIRE = 60;

    @Autowired
    @Qualifier("adminRedisHelper")
    private RedisHelper redisHelper;
    @Autowired
    private AdminLogService logService;
    @Autowired
    private AdminUserService adminUserService;

    /** 没有操作后，登录token超时时间，默认为3天 */
    @Value("${admin.token.expireSecond:259200}")
    public Integer tokenExpireTime;

    @NotRequireLogin
    @ResponseBody @PostMapping("/login")
    public WebJsonBean<?> login(HttpServletResponse response, String username, String password,
                             String r, String captcha) {
        WebCheckUtils.assertNotBlank(username, "请提供用户名");
        username = username.trim();
        WebCheckUtils.assertNotNull(password, "请提供密码");
        if (isExceedPasswordTry(username)) {
            WebCheckUtils.assertNotBlank(captcha, "请提供验证码");
            WebCheckUtils.assertNotBlank(r, "请提供验证码随机值");
        }

        if (isExceedPasswordTry(username)) {
            String _capthca = redisHelper.getString(RedisKeyNamespace.CAPTCHA + r);
            if (!captcha.equalsIgnoreCase(_capthca)) {
                redisHelper.remove(RedisKeyNamespace.CAPTCHA + r);
                return WebJsonBean.fail(AdminErrorCode.WRONG_CAPTCHA);
            }
            redisHelper.remove(RedisKeyNamespace.CAPTCHA + r);
        }

        AdminUserDO user = adminUserService.getUserByUsername(username);

        if (user == null) {
            wrongPasswordCount(username);
            return WebJsonBean.fail("请检查用户名或密码是否正确");
        } else {
            String md5 = DigestUtils.md5DigestAsHex(password.getBytes());
            if (!md5.equalsIgnoreCase(user.getPassword())) {
                wrongPasswordCount(username);
                return WebJsonBean.fail("请检查用户名或密码是否正确");
            }

            if (user.getDisabled() != null && user.getDisabled()) {
                return WebJsonBean.fail("该用户已被禁用，不允许登录");
            }
        }

        // 登录成功打cookie
        String token = UUID.randomUUID().toString().replace("-", "");
        AdminUserLoginContext loginContext = adminUserService.buildAdminUserLoginContext(user);

        redisHelper.setObject(RedisKeyNamespace.ADMIN_LOGIN_TOKEN + token,
                tokenExpireTime, loginContext);

        logService.log("用户登录", OperateTypeEnum.LOGIN, SecurityLevelEnum.COMMON, loginContext);

        CookieUtils.addCookieForJakarta(response, AdminUserLoginInterceptor.cookieTokenName, token, null, 0);
        return WebJsonBean.ok();
    }

    @NotRequireLogin
    @ResponseBody @PostMapping("/logout")
    public WebJsonBean<?> logout(HttpServletRequest request, HttpServletResponse response) {
        logService.log("用户退出", OperateTypeEnum.LOGOUT, SecurityLevelEnum.COMMON);
        String token = CookieUtils.getCookieValueForJakarta(request, AdminUserLoginInterceptor.cookieTokenName);
        redisHelper.remove(RedisKeyNamespace.ADMIN_LOGIN_TOKEN + token);
        CookieUtils.removeCookieForJakarta(response, AdminUserLoginInterceptor.cookieTokenName, null);

        return WebJsonBean.ok();
    }

    /**是否需要验证码，验证码是按用户名来的*/
    @ResponseBody
    @GetMapping("/is_need_captcha")
    @NotRequireLogin
    public WebJsonBean<?> isNeedCaptcha(String username) {
        return WebJsonBean.ok(isExceedPasswordTry(username));
    }

    /**
     * 获得一个验证码，必须传入一个随机字符串r，以便校验时可以找到
     */
    @NotRequireLogin
    @RequestMapping("/captcha")
    public void crimg(HttpServletResponse response, OutputStream out, String r) throws IOException {
        setResponseHeaders(response);
        String code = CaptchaUtils.genCaptcha(null, 4, out);
        out.close();
        if (StringUtils.isNotEmpty(r)) {
            redisHelper.setString(RedisKeyNamespace.CAPTCHA + r, REDIS_CAPTCHA_EXPIRE, code);
        }
    }

    private void setResponseHeaders(HttpServletResponse response) {
        response.setContentType("image/png");
        response.setHeader("Cache-Control", "no-cache, no-store");
        response.setHeader("Pragma", "no-cache");
        long time = System.currentTimeMillis();
        response.setDateHeader("Last-Modified", time);
        response.setDateHeader("Date", time);
        response.setDateHeader("Expires", time);
    }

    private final RedisLimitParam limitParam = new RedisLimitParam(
            RedisKeyNamespace.LIMIT_WRONG_PASSWORD,
            RedisLimitPeriodEnum.DAY,
            5 // 一天五次
    );

    /**是否超过了*/
    private boolean isExceedPasswordTry(String username) {
        if (StringTools.isBlank(username)) {
            return false;
        }
        return !redisHelper.hasLimitCount(limitParam, username);
    }

    private void wrongPasswordCount(String username) {
        if (StringTools.isBlank(username)) {
            return;
        }
        redisHelper.useLimitCount(limitParam, username);
    }

}
