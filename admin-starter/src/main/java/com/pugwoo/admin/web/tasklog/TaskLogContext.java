package com.pugwoo.admin.web.tasklog;

public class TaskLogContext {

    private static final ThreadLocal<Boolean> isTriggerByManual = new ThreadLocal<>();

    public static boolean getIsTriggerByManual() {
        return isTriggerByManual.get() != null && isTriggerByManual.get();
    }

    public static void setIsTriggerByManual(boolean isTriggerByManual) {
        TaskLogContext.isTriggerByManual.set(isTriggerByManual);
    }

}
