package com.pugwoo.admin.web.interceptor;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.AdminInnerException;
import com.pugwoo.admin.bean.RedisKeyNamespace;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.entity.AdminLogSlowWebDO;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.enums.AdminSecurityLevelEnum;
import com.pugwoo.admin.enums.UrlTypeEnum;
import com.pugwoo.admin.enums.UserSourceEnum;
import com.pugwoo.admin.service.AdminLogService;
import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.utils.LoginAnnotation;
import com.pugwoo.admin.utils.PermissionAnnotation;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.vo.AdminUrlDTO;
import com.pugwoo.admin.web.weblog.CommonWebLogFilter;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.CookieUtils;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 判断admin用户登录态拦截器.
 * <br>
 * 注：登录token可以单机存放，也可以分布式存放到redis。
 * <br>
 * url权限配置表【每分钟】更新一次。关于url角色配置规则：
 * <br>
 * 1) url支持AntMatcher表达式
 * 2) 只要uri（从根目录开始/）匹配上了配置的url表达式，那么对应的角色即生效
 * 3) 如果url匹配上多个表达式和角色，所有的角色都生效，不存在优先级模式。
 * <br>
 * 说明：计算慢web请求也放在这个拦截器中。
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AdminUserLoginInterceptor implements HandlerInterceptor {

    @Autowired
    private AdminProperties adminProperties;

    /**cookie token名称*/
    public static final String cookieTokenName = "admin_token";
    private static final ThreadLocal<AdminUserLoginContext> userLoginContext = new ThreadLocal<>();
    private static final ThreadLocal<Long> startTime = new ThreadLocal<>();

    @Autowired @Qualifier("adminRedisHelper")
    private RedisHelper redisHelper;
    @Autowired
    private AdminLogService logService;
    @Autowired(required = false)
    private CustomLoginHandler customLoginHandler;
    @Autowired
    private AdminUserService adminUserService;

    /**
     * 获得当前登录用户信息
     * @throws AdminInnerException 没有登录时抛出未登录异常
     */
    public static AdminUserLoginContext getAdminUserLoginContext() {
        AdminUserLoginContext context = userLoginContext.get();
        if(context == null || context.getUserId() == null) {
            throw new AdminInnerException(AdminErrorCode.NOT_LOGIN);
        }
        return context;
    }

    /**
     * 获得当前可能的用户登陆态，如果没有登录则返回null，不会抛出异常
     */
    public static AdminUserLoginContext getPossibleAdminUserLoginContext() {
        try {
            return userLoginContext.get();
        } catch (Exception e) { // ignore
            return null;
        }
    }

    /**获得可能的登录用户userId，没有返回null
     * 说明：这个方法一般给DO类获得当前的登陆用户id用的，虽然提示没有使用，但实际上有用
     */
    @SuppressWarnings("unused")
    public static Long getPossibleUserId() {
        AdminUserLoginContext context = getPossibleAdminUserLoginContext();
        return context == null ? null : context.getUserId();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        startTime.set(System.currentTimeMillis());

        // 不处理配置忽略的链接
        if(StringUtils.isNotBlank(adminProperties.getIgnoreLoginInterceptor())) {
            String contextPath = NetUtils.getContextPathForJakarta(request);
            String path = NetUtils.getUrlPathForJakarta(request);
            String pathWithoutContextPath = path;
            if(path != null && path.startsWith(contextPath)) {
                pathWithoutContextPath = path.substring(contextPath.length());
            }

            String[] ignores = adminProperties.getIgnoreLoginInterceptor().split(",");
            for(String ignore : ignores) {
                if(StringUtils.isBlank(ignore)) {
                    continue;
                }
                if(pathWithoutContextPath != null && pathWithoutContextPath.startsWith(ignore)) {
                    return true;
                }
            }
        }

        boolean isRequireLogin  = LoginAnnotation.isRequireLogin(handler, true);

        Boolean enableCSRFCheck = adminProperties.getEnableCSRFCheck();
        if(enableCSRFCheck != null && enableCSRFCheck && !WebCheckUtils.csrfPassed(request, handler, isRequireLogin)) {
            response.setHeader("Content-type", "application/json;charset=UTF-8");
            response.getWriter().write(JSON.toJson(
                    WebJsonBean.fail(AdminErrorCode.PERMISSION_DENIED, "CSRF PERMISSION DENIED")));
            return false;
        }

        userLoginContext.set(null); // 先清掉登录态
        AdminUserLoginContext user = null;

        // 尽可能获得用户的登陆态，即便不需要登录
        String token = CookieUtils.getCookieValueForJakarta(request, cookieTokenName);
        if (StringUtils.isNotBlank(token)) {
            user = redisHelper.getObject(getTokenRedisKey(token), AdminUserLoginContext.class);
            if (user != null) {
                adminUserService.renewalToken(token);
                userLoginContext.set(user);
            }
        }

        if (user != null) {
            CommonWebLogFilter.loginUsername.set(user.getUserName());
        }

        if(!isRequireLogin) {return true;} // 不需要登录

        // 如果有自定义登录处理器，那么使用自定义登录处理器
        if (customLoginHandler != null) {
            CustomLoginHandler.CustomLoginUserDTO customLoginUser = customLoginHandler.getLoginUser(request, response);
            if (customLoginUser != null) {
                // 检查用户是否已经注册了，如果还没有注册，则自动完成注册
                // TODO【低优】检查用户注册的性能较差，后续有真正第三方登录时再考虑优化，放到redis中，本次不做
                AdminUserDO adminUserDO = adminUserService.getUserByUsername(customLoginUser.getUserName());
                if (adminUserDO == null) {
                    // 注册
                    adminUserDO = new AdminUserDO();
                    adminUserDO.setUserName(customLoginUser.getUserName());
                    adminUserDO.setRealName(customLoginUser.getRealName());
                    adminUserDO.setDisabled(false);
                    adminUserDO.setIsAdmin(false);
                    adminUserDO.setSource(UserSourceEnum.CUSTOM.getCode());
                    adminUserService.addOrUpdate(adminUserDO);
                }

                // 转成AdminUserLoginContext TODO【低优】构造中的权限信息可以优化，不需要每次都构造
                user = adminUserService.buildAdminUserLoginContext(adminUserDO);
                userLoginContext.set(user);
                return true;
            }
        }

        if(user == null) {
            if(handler instanceof HandlerMethod) { // 对ajax请求或下载额外处理
                ResponseBody responseBody = ((HandlerMethod) handler).getMethodAnnotation(ResponseBody.class);
                if(responseBody != null) {
                    response.setHeader("Content-type", "application/json;charset=UTF-8");
                    response.getWriter().write(JSON.toJson(WebJsonBean.fail(AdminErrorCode.NOT_LOGIN)));
                    doLogSlow(request);
                    return false;
                }
            }
            request.getRequestDispatcher("/admin_login/login").forward(request, response);
            doLogSlow(request);
            return false;
        }

        String contextPath = NetUtils.getContextPathForJakarta(request);

        if(!checkUserPermission(user, request.getRequestURI(), contextPath, handler)) {
            //处理 接口响应
            if(handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                boolean isResponseBody = handlerMethod.getMethodAnnotation(ResponseBody.class) != null;
                boolean isRestController = handlerMethod.getBeanType().getAnnotation(RestController.class) != null;

                if (isResponseBody || isRestController) {
                    response.setHeader("Content-type", "application/json;charset=UTF-8");
                    response.getWriter().write(JSON.toJson(
                            WebJsonBean.fail(AdminErrorCode.PERMISSION_DENIED, "您没有权限访问该接口，请联系管理员")));
                    doLogSlow(request);
                    return false;
                }
            }
            // 处理 其他响应
            request.getRequestDispatcher("/admin_login/no_permission").forward(request, response);
            doLogSlow(request);
            return false;
        }

        return true;
    }

    /**
     * 检查用户是否拥有当前访问的url的权限
     *
     * @param user 登陆用户上下文
     * @param uri 从根目录/开始的uri，要注意，url结尾处带/和不带/到spring mvc是一样的
     * @return 返回true则有权限，false则没有权限
     */
    private boolean checkUserPermission(AdminUserLoginContext user, String uri, String contextPath,
                                        Object handler) {
        if(user == null || user.getUserId() == null || StringTools.isEmpty(uri)) {
            return false;
        }

        AdminSecurityLevelEnum adminSecurityLevel =
                AdminSecurityLevelEnum.getByCode(adminProperties.getSecurityLevel());

        if (adminSecurityLevel == AdminSecurityLevelEnum.NONE) { return true; } // 不限制模式

        if(handler instanceof HandlerMethod && user.isAdmin()) { // 管理员可以访问
            if(((HandlerMethod) handler).getBeanType().getName().startsWith("com.pugwoo.admin")) {
                return true;
            }
        }

        if (adminSecurityLevel == AdminSecurityLevelEnum.LOOSE) { //宽松模式 == 只限制页面，不限制ajax
            if (handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                boolean isResponseBody = handlerMethod.getMethodAnnotation(ResponseBody.class) != null;
                boolean isRestController = handlerMethod.getBeanType().getAnnotation(RestController.class) != null;
                if (isResponseBody || isRestController) { return true; }
            }
        }

        // Permission和url校验权限共存
        if(uri.endsWith("/")) {
            uri = uri.substring(0, uri.length() - 1);
        }
        if(uri.isEmpty() || Objects.equals(uri, contextPath)) { // 首页(菜单)不需要做权限控制
            return true;
        }

        PermissionAnnotation.PermissionDTO permission = PermissionAnnotation.getPermission(handler);
        if(permission != null && permission.getPermission().isEmpty()) {
            return true; // 不需要权限校验
        }

        List<AdminUrlDTO> userUrlDTOs = user.getUrls();
        return check(userUrlDTOs, uri, permission);
    }

    private boolean check(List<AdminUrlDTO> urlDTOs, String uri,
                          PermissionAnnotation.PermissionDTO permission) {
        if (urlDTOs == null || urlDTOs.isEmpty()) {
            return false;
        }

        for (AdminUrlDTO userUrlDTO : urlDTOs) {
            UrlTypeEnum urlType = UrlTypeEnum.getByCode(userUrlDTO.getType());
            if (urlType == null) {
                continue;
            }
            String userUrl = userUrlDTO.getUrl();
            if(StringUtils.isBlank(userUrl)) {continue;} // 没有配置的url继续匹配
            switch (urlType) {
                case OTHER:	// 正则 + equals ， 此处不break
                    if(permission != null) {
                        if (permission.getPermission().matches("^" + userUrl + "$")) {
                            return true;
                        }
                    }
                    if(uri.matches("^" + userUrl + "$")) {
                        return true;
                    }
                    // 不break
                case MENU:	// equals
                    if(permission != null) { // 这里!=null表示有注解，这一行是必须的
                        if (Objects.equals(permission.getPermission(), userUrl)) {
                            return true;
                        }
                    }
                    if (Objects.equals(uri, userUrl)) {
                        return true;
                    }
                    break;
                default:
                    break;
            }
        }

        return false;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {

        // 使得vm渲染中的静态资源路径和具体哪个应用根路径无关
        String contextPath = request.getContextPath();
        if(contextPath != null) {
            while(contextPath.startsWith("//")) {
                contextPath = contextPath.substring(1);
            }
        }

        String resourceContextPath = "";
        if (adminProperties.getUseCDNResource() != null && adminProperties.getUseCDNResource()) {
            resourceContextPath = "https://static-1251050007.cos.ap-guangzhou.myqcloud.com/";
        } else {
            resourceContextPath = contextPath;
        }

        if(modelAndView != null) {
            modelAndView.addObject("_contextPath_", contextPath);
            modelAndView.addObject("_resourceContextPath_", resourceContextPath);
        } else {
            request.setAttribute("_contextPath_", contextPath);
            request.setAttribute("_resourceContextPath_", resourceContextPath);
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        doLogSlow(request);
        userLoginContext.remove();
    }

    private void doLogSlow(HttpServletRequest httpRequest) {
        Integer webSlowMs = adminProperties.getSlowWebTimeMs();

        if(webSlowMs == null) { // 被关闭掉
            return;
        }
        Long start = startTime.get();
        if (start == null) {
            log.error("log slow web start time is null");
            return;
        }
        long cost = System.currentTimeMillis() - start;

        if (cost > webSlowMs) {
            try {
                String url = NetUtils.getFullUrlWithParamForJakarta(httpRequest);
                log.warn("slow web request, cost:{}ms, url:{}", cost, url);

                Boolean logWebSlowToDB = adminProperties.getLogWebSlowToDB();
                if(logWebSlowToDB != null && logWebSlowToDB) {
                    AdminLogSlowWebDO slowWebDO = new AdminLogSlowWebDO();
                    slowWebDO.setCreateTime(new Date());
                    slowWebDO.setUpdateTime(new Date());
                    AdminUserLoginContext context = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
                    if (context != null) {
                        slowWebDO.setUserId(context.getUserId());
                        slowWebDO.setUserName(context.getUserName());
                    }
                    slowWebDO.setUrl(url);
                    slowWebDO.setReferer(httpRequest.getHeader("Referer"));
                    slowWebDO.setIp(NetUtils.getRemoteIpForJakarta(httpRequest));
                    slowWebDO.setRequestMethod(httpRequest.getMethod());
                    slowWebDO.setRequestTime(cost);
                    slowWebDO.setRead(false);

                    logService.addSlowWeb(slowWebDO);
                }
            } catch (Throwable e) {
                log.error("log slow web fail", e);
            }
        }
    }

    private static String getTokenRedisKey(String token) {
        return RedisKeyNamespace.ADMIN_LOGIN_TOKEN + token;
    }
}