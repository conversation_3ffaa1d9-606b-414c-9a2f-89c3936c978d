package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminUserDO;
import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.utils.NoPermission;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.vo.AdminUserRelatedDepartmentVO;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContext;
import com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor;
import com.pugwoo.bootwebext.JsonParam;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 用户相关模块，推荐每个模块以自己的url前缀命名，方便权限管理
 * <AUTHOR>
 */
@Permission(value = "AdminUserRead", name = "系统用户查看权限")
@RestController
@RequestMapping("/admin_user")
public class AdminUserController {
	
	@Autowired
	private AdminUserService userService;

	@GetMapping("get_page")
	public WebJsonBean listData(int page, int pageSize, String keyword, 
			@JsonParam("departmentIds") List<Long> departmentIds,
			@JsonParam("dateRange") List<Date> dateRange) throws Exception {
		Date createStarttime = dateRange != null && dateRange.size() > 0 ? dateRange.get(0) : null;
		Date createEndTime = dateRange != null && dateRange.size() > 1 ? dateRange.get(1) : null;

		PageData<AdminUserRelatedDepartmentVO> pageData = userService.getUserWithDepartments(
				page, pageSize, keyword, departmentIds, createStarttime, createEndTime);
		
		Map<String, Object> data = PageUtils.trans(pageData, o -> {
				Map<String, Object> map = new HashMap<>(); // 这是最规范的写法
				map.put("id", o.getId());
				map.put("createTime", o.getCreateTime());
				map.put("userName", o.getUserName());
				map.put("realName", o.getRealName());
				map.put("phone", o.getPhone());
				map.put("email", o.getEmail());
				map.put("departmentId", o.getDepartmentId());
				map.put("position", o.getPosition());
				map.put("remark", o.getRemark());
				map.put("signature", o.getSignature());
				map.put("departmentName", o.getDepartmentName());
				map.put("company", o.getCompany());
				map.put("isAdmin", o.getIsAdmin() == null ? false : o.getIsAdmin());
				map.put("disabled", o.getDisabled() == null ? false : o.getDisabled());
				return map;
		});
		return WebJsonBean.ok(data);
	}
	
	/**用于user-select组件*/
	@NoPermission @GetMapping("query_user")
	public WebJsonBean queryUser(String q, String id, @JsonParam("ids") List<String> ids) {
		List<Long> idList = new ArrayList<>();
		Long _id = NumberUtils.parseLong(id);
		if(_id != null) {
			idList.add(_id);
		}
		if(ids != null) {
			for(String str : ids) {
				_id = NumberUtils.parseLong(str);
				if(_id != null) {
					idList.add(_id);
				}
			}
		}
		List<AdminUserDO> users = userService.queryUser(idList, q, 10);
		List<Map<String, Object>> data = ListUtils.transform(users, o ->
		    MapUtils.of("id", o.getId(),
		    		    "userName", o.getUserName(),
		    		    "realName", o.getRealName()));
		return WebJsonBean.ok(data);
	}
	
	/**添加或编辑用户*/
	@Permission(value = "AdminUserWrite", name = "系统用户编辑权限")
	@PostMapping("add_or_edit")
	public WebJsonBean addOrEditJson(
			Long id, String userName, String password,
			String realName, String phone, String email,
			Long department, String position, String remark,
			String company, Boolean isAdmin, Boolean disabled) { 
		    // 这是最规范最安全的写法，项目可因效率而直接填DO
		userName = userName.trim();

		AdminUserDO adminUserDO;
		if(id != null) {
			adminUserDO = userService.getUserById(id);
			WebCheckUtils.assertNotNull(adminUserDO, "用户不存在");
		} else {
			WebCheckUtils.assertNotBlank(userName, "请提供用户名");
			WebCheckUtils.assertNotBlank(password, "请提供密码");
			if (userService.isExistUsername(userName)) {
				return WebJsonBean.fail(AdminErrorCode.ILLEGAL_PARAMETERS, "用户名 " + userName +"已存在");
			}
			adminUserDO = new AdminUserDO();
		}

		if(StringUtils.isNotBlank(userName)) { // 用户名不允许清空
			adminUserDO.setUserName(userName);
		}
		if(StringUtils.isNotBlank(password)) { // 密码不允许清空
			String md5 = DigestUtils.md5DigestAsHex(password.getBytes());
			adminUserDO.setPassword(md5);
		}
		
		adminUserDO.setRealName(realName);
		adminUserDO.setPhone(phone);
		adminUserDO.setEmail(email);
		adminUserDO.setDepartmentId(department);
		adminUserDO.setPosition(position);
		adminUserDO.setRemark(remark);
		adminUserDO.setCompany(company);
		adminUserDO.setIsAdmin(isAdmin);
		adminUserDO.setDisabled(disabled);
		
		if(disabled != null && disabled && id != null) {
			userService.removeUserLoginContext(id);
		}
		
		userService.addOrUpdate(adminUserDO);
		if (adminUserDO.getId() != null) {
		    userService.updateUserLoginContext(adminUserDO);
        }
		return WebJsonBean.ok();
	}

	@Permission(value = "AdminUserDelete", name = "系统用户删除权限")
	@PostMapping("delete")
	public WebJsonBean delete(Long userId) {
		WebCheckUtils.assertNotNull(userId, "需要提供用户id");
		boolean succ = userService.deleteUserById(userId);
		userService.removeUserLoginContext(userId);
		return succ ? WebJsonBean.ok() :
			WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "删除失败");
	}

    @NoPermission @GetMapping("/get_my_info")
    public WebJsonBean myInfo() {
    	AdminUserLoginContext context = AdminUserLoginInterceptor.getAdminUserLoginContext();
    	AdminUserDO userDO = userService.getUserById(context.getUserId());
    	return WebJsonBean.ok(MapUtils.of("userName", userDO.getUserName(),
    			                           "realName", userDO.getRealName(),
    			                           "phone", userDO.getPhone(),
    			                           "email", userDO.getEmail(),
    			                           "signature", userDO.getSignature()));
    }

	@NoPermission @PostMapping("/edit_my_info")
    public WebJsonBean updateInfo(String realName, String phone, String email, String signature,
    		String password, String repeatPassword) {
    	WebCheckUtils.assertNotBlank(realName, "请填写真实姓名");
    	Long userId = AdminUserLoginInterceptor.getAdminUserLoginContext().getUserId();
    	AdminUserDO userDO = new AdminUserDO();
    	userDO.setId(userId);
    	userDO.setRealName(realName);
    	userDO.setPhone(phone);
    	userDO.setEmail(email);
    	userDO.setSignature(signature);
    	if(StringUtils.isNotEmpty(password)) {
    		if(!Objects.equals(password, repeatPassword)) {
    			return WebJsonBean.fail(AdminErrorCode.ILLEGAL_PARAMETERS, "输入的两次密码不一致");
    		}
			String md5 = DigestUtils.md5DigestAsHex(password.getBytes());
			userDO.setPassword(md5);
    	}
    	boolean succ = userService.addOrUpdate(userDO);
    	return succ ? WebJsonBean.ok() :
    		WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "修改个人信息失败");
    }
}
