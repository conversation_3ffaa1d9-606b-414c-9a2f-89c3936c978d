package com.pugwoo.admin.web.weblog;

import lombok.Data;

import java.util.Map;

/**
 * 请求数据
 */
@Data
public class RequestDataDTO {

    /**请求的唯一标识*/
    private String requestUuid;
    /**远程客户端ip地址*/
    private String remoteIp;
    /**请求方法 GET/POST等*/
    private String requestMethod;
    /**请求url，含queryString参数*/
    private String url;
    /**请求协议*/
    private String protocol;
    /**请求头部*/
    private Map<String, String> requestHeaders;
    /**是否是上传文件，当是上传文件时，请求体不提供，只提供上传文件大小*/
    private Boolean isUploadFile;
    /**如果是上传文件，此为上传的文件的大小*/
    private Long uploadFileSize;
    /**请求体*/
    private String requestBody;
    /**请求耗时的毫秒数*/
    private Long costMs;
    /**返回头部*/
    private Map<String, String> responseHeaders;
    /**返回的是否是二进制数据，如果是，则没有responseBody，只有responseBytesLength*/
    private Boolean isResponseBinary;
    /**返回的字节数*/
    private Long responseBytesLength;
    /**返回数据正文*/
    private String responseBody;

}
