package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminUrlDO;
import com.pugwoo.admin.entity.AdminUrlIgnoreDO;
import com.pugwoo.admin.enums.UrlTypeEnum;
import com.pugwoo.admin.service.AdminRolePermissionService;
import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.utils.LoginAnnotation;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.PermissionAnnotation;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.vo.AdminUrlTreeVO;
import com.pugwoo.bootwebext.JsonParam;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;
import org.springframework.web.util.pattern.PathPattern;

import jakarta.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.Map.Entry;

@Permission(value = "AdminRoleUrlRead", name = "系统角色权限查看权限")
@RestController @RequestMapping("/admin_url")
public class AdminUrlController {
	
	private static final String notLogNodeName = "============未录入系统的URL（不含无需鉴权的URL），请逐个拖拽到上面目录中============";

    @Autowired
	private AdminRolePermissionService rolePermissionService;
    @Autowired
    private AdminUserService userService;
	@Autowired(required = false)
	private RequestMappingHandlerMapping requestMappingHandlerMapping;

	@GetMapping("tree_data")
	public WebJsonBean<?> getTreeData(HttpServletRequest req, boolean scan) {
		List<AdminUrlTreeVO> allUrls = rolePermissionService.getAllUrls();
		List<AdminUrlTreeVO> treeData = rolePermissionService.genUrlTree(allUrls);
		
		if(scan && requestMappingHandlerMapping != null) { // 扫描系统的未录入URL
			Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
			AdminUrlTreeVO unLogged = new AdminUrlTreeVO();
			unLogged.setType(UrlTypeEnum.FOLDER.getCode());
			unLogged.setName(notLogNodeName);
			unLogged.initChildren();
			unLogged.setScanned(true);
			
			String context = NetUtils.getContextPathForJakarta(req);
			List<AdminUrlIgnoreDO> ignoreUrlsList = rolePermissionService.getAllIgnoreUrls();
			List<String> ignoreUrls = ListUtils.transform(ignoreUrlsList, o -> o.getUrl());
			List<String> allUrlsStr = ListUtils.transform(allUrls, o -> o.getUrl());
			
			Set<String> scannedUrls = new HashSet<>();
			for(Entry<RequestMappingInfo, HandlerMethod> e : handlerMethods.entrySet()) {
				HandlerMethod handlerMethod = e.getValue();
				boolean isResponseBody = handlerMethod.getMethodAnnotation(ResponseBody.class) != null;
				boolean isRestController = handlerMethod.getBeanType().getAnnotation(RestController.class) != null;

				PermissionAnnotation.PermissionDTO permission = PermissionAnnotation.getPermission(handlerMethod);
				if(permission != null && (isResponseBody || isRestController)) { // 如果是接口，则使用权限注解的方式纳入扫描
					// 把permission对应的url也加进来
					if (e.getKey().getPatternsCondition() != null) {
						for(String p: e.getKey().getPatternsCondition().getPatterns()) {
							scannedUrls.add(context + p);
						}
					} else if (e.getKey().getPathPatternsCondition() != null) {
						for (PathPattern pp : e.getKey().getPathPatternsCondition().getPatterns()) {
							scannedUrls.add(context + pp.getPatternString());
						}
					}

					if(StringTools.isBlank(permission.getPermission())) { // 不需要权限的链接不在扫描范围之内
						continue;
					}
					if(scannedUrls.contains(permission.getPermission())) { // 对于相同名称的权限，只加入一个
                        continue;
					}
                    scannedUrls.add(permission.getPermission());

				    if(ignoreUrls.contains(permission.getPermission())) {continue;}
					if(allUrlsStr.contains(permission.getPermission())) {continue;} // 过滤掉已经存在的

					AdminUrlTreeVO urlVO = new AdminUrlTreeVO();
					urlVO.setType(UrlTypeEnum.OTHER.getCode());
					urlVO.setUrl(permission.getPermission());
					if(StringTools.isNotBlank(permission.getName())) {
                        urlVO.setName(permission.getName());
                    } else {
					    urlVO.setName(permission.getPermission());
                    }

					urlVO.setScanned(true);
					urlVO.setWebkey(UUID.randomUUID().toString().replaceAll("-", ""));
					unLogged.getChildren().add(urlVO);
				} else {
					Set<String> patterns;
					if (e.getKey().getPatternsCondition() != null) {
						patterns = e.getKey().getPatternsCondition().getPatterns();
						for(String p: patterns) {
							scannedUrls.add(context + p);
						}
					} else if (e.getKey().getPathPatternsCondition() != null) {
						Set<PathPattern> tmp = e.getKey().getPathPatternsCondition().getPatterns();
						patterns = new HashSet<>();
						for (PathPattern pp : tmp) {
							patterns.add(pp.getPatternString());
							scannedUrls.add(context + pp.getPatternString());
						}
					} else {
						patterns = new HashSet<>();
					}

					// 如果权限为空且名称为空，则不加入
					if(permission != null && StringTools.isBlank(permission.getName())
							&& StringTools.isBlank(permission.getPermission())) {
						continue;
					}

					if (!LoginAnnotation.isRequireLogin(handlerMethod, true)) {continue;}  //过滤掉不需要登录的url
					for(String p : patterns) {
						if (Objects.equals(p, "/")) {continue;} //过滤掉首页url
						String url = context + p;
						if(ignoreUrls.contains(url)) {continue;}
						if(allUrlsStr.contains(url)) {continue;} // 过滤掉已经存在的url

						String type = (isResponseBody||isRestController ? UrlTypeEnum.OTHER : UrlTypeEnum.MENU).getCode();

						AdminUrlTreeVO urlVO = new AdminUrlTreeVO();
						urlVO.setType(type);
						urlVO.setUrl(url);
						String method = e.getKey().getMethodsCondition().toString();
						method = "[]".equals(method) ? "" : " " + method;

						// 对于菜单url，查询一下是否有permission名称
						if(permission != null && StringTools.isNotBlank(permission.getName())) {
							urlVO.setName(permission.getName());
						} else {
							urlVO.setName(url + method);
						}

						urlVO.setScanned(true);
						urlVO.setWebkey(UUID.randomUUID().toString().replaceAll("-", ""));
						unLogged.getChildren().add(urlVO);
					}
				}
			}
			
			for(AdminUrlTreeVO urlVO : allUrls) { // 对于treeData中可以被扫描到的url，标记一下
				String url = urlVO.getUrl();
				if(StringUtils.isNotBlank(url)) {
					if(url.toLowerCase().startsWith("http://") || url.toLowerCase().startsWith("https://")) {
						urlVO.setInScan(true); // 排除掉http https开头的，外部系统链接认为可以被扫描到
						continue;
					}
				}
				urlVO.setInScan(scannedUrls.contains(urlVO.getUrl()));
			}
			if(!unLogged.getChildren().isEmpty()) {
				treeData.add(unLogged);
			}
		}
		
		return WebJsonBean.ok(treeData);
	}

    @Permission(value = "AdminRoleUrlWrite", name = "系统角色权限操作权限")
	@PostMapping("save_node")
	public WebJsonBean saveNode(Long id, String name, String type,
			String url, String icon, String description) {

	    name = name.trim();

		WebCheckUtils.assertNotNull(id, "缺少id，无法保存");
		WebCheckUtils.assertNotBlank(name, "名称必须提供");
		WebCheckUtils.assertNotBlank(type, "类型必须提供");
		AdminUrlDO adminUrlDO = rolePermissionService.getUrlById(id);
		WebCheckUtils.assertNotNull(adminUrlDO, "节点不存在");

		adminUrlDO.setName(name);
		adminUrlDO.setUrl(url);
		adminUrlDO.setType(type);
		adminUrlDO.setIcon(icon);
		adminUrlDO.setDescription(description);
		boolean succ = rolePermissionService.updateUrl(adminUrlDO);

		if(succ) {
			userService.refreshAllUserUrls();
		}

		return succ ? WebJsonBean.ok() : WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, "保存失败");
	}

    @Permission(value = "AdminRoleUrlWrite", name = "系统角色权限操作权限")
	@PostMapping("save_tree")
	public WebJsonBean saveTree(@JsonParam("treeData") List<AdminUrlTreeVO> urls) {
		WebCheckUtils.assertNotNull(urls, "缺少树形数据");
		
		Iterator<AdminUrlTreeVO> it = urls.iterator();
		while(it.hasNext()) {
			AdminUrlTreeVO vo = it.next();
			if(vo != null && vo.isScanned() &&
				UrlTypeEnum.FOLDER.getCode().equals(vo.getType())) { // 移除扫描节点目录
				it.remove();
			}
		}
		
		List<WebJsonBean> checkTree = rolePermissionService.checkUrlTree(urls);
		if (!checkTree.isEmpty()){
			return WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR, checkTree);
		}
		boolean succ = rolePermissionService.saveUrlTree(urls);

		if(succ) {
			userService.refreshAllUserUrls();
		}

		return succ ? WebJsonBean.ok() : WebJsonBean.fail(
				AdminErrorCode.COMMON_BIZ_ERROR, "保存失败");
	}

    @Permission(value = "AdminRoleUrlWrite", name = "系统角色权限操作权限")
	@PostMapping("/ignore_url")
	public WebJsonBean ignoreUrl(String url) {
		rolePermissionService.ignoreUrl(url);
		return WebJsonBean.ok();
	}
}
