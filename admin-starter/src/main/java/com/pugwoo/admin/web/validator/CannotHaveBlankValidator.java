package com.pugwoo.admin.web.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * 所有的验证者都需要实现ConstraintValidator接口，
 * 接口包含一个初始化事件方法
 *      和一个判断是否合法的方法。
 */
public class CannotHaveBlankValidator implements ConstraintValidator<CannotHaveBlank, String> {

	/**
	 * 初始化事件方法
	 */
    @Override
    public void initialize(CannotHaveBlank constraintAnnotation) {
    }

	/**
	 * 判断是否合法的方法
	 */
	@Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        //null时不进行校验
        if (value != null && value.contains(" ")) {
//            //获取默认提示信息
//            String defaultConstraintMessageTemplate = context.getDefaultConstraintMessageTemplate();
//            //禁用默认提示信息
//            context.disableDefaultConstraintViolation();
//            //设置提示语
//            context.buildConstraintViolationWithTemplate("can not contains blank").addConstraintViolation();
            return false;
        }
        return true;
    }
}