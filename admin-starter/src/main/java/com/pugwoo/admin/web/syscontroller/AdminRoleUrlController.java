package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.entity.AdminRoleDO;
import com.pugwoo.admin.entity.AdminUrlDO;
import com.pugwoo.admin.enums.UrlTypeEnum;
import com.pugwoo.admin.service.AdminRolePermissionService;
import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.utils.WebCheckUtils;
import com.pugwoo.admin.vo.RoleUrlRelatedRoleVO;
import com.pugwoo.admin.vo.RoleUrlRelatedUrlVO;
import com.pugwoo.bootwebext.JsonParam;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-04-19
 */
@Permission(value = "AdminRoleUrlRead", name = "系统角色权限查看权限")
@RestController
@RequestMapping("admin_role_url")
public class AdminRoleUrlController {

    @Autowired
    private AdminRolePermissionService rolePermissionService;
    @Autowired
    private AdminUserService userService;

    /**
     * 根据urlId获取用户角色列表
     *      {
     *          "allRoles":[{"code":,"roleId":,"roleName":}] // 所有的角色
     *          "urlRoles":[roleId]
     *      }
     * @param urlId
     * @return
     */
    @GetMapping("roleListByUrlId")
    public WebJsonBean getAllRoleByUrl(Long urlId) {
        WebCheckUtils.assertNotNull(urlId, "缺少urlId");
        AdminUrlDO adminUrlDO = rolePermissionService.getUrlById(urlId);
        WebCheckUtils.assertNotNull(adminUrlDO, "urlId=" + urlId + "的记录为空");

        List<AdminRoleDO> adminRoleDOList = rolePermissionService.getAllRoles();
        List<RoleUrlRelatedRoleVO> roleUrlRelatedRoleVOList =
                rolePermissionService.getRoleByUrlId(urlId);

        List<Map<String, Object>> allRoles = ListUtils.transform(adminRoleDOList, o -> {
            Map<String, Object> map = new HashMap<>();
            map.put("roleId", o.getId());
            map.put("roleName", o.getName());
            map.put("code", o.getCode());
            return map;
        });

        Map<String, Object> result = new HashMap<>();
        result.put("allRoles", allRoles);
        result.put("urlRoles", ListUtils.transform(
                roleUrlRelatedRoleVOList, o -> o.getAdminRoleDO().getId()));

        return WebJsonBean.ok(result);
    }

    /**
     *  更新url对应的角色列表
     *  规则：某一个节点 url_id 对于原来的数据，
     *       如果roleList中有减少的角色，则需要对该url及其所有子节点都减少该角色
     * @param urlId
     * @param urlRolelist
     * @return
     */
    @Permission(value = "AdminRoleUrlWrite", name = "系统角色权限操作权限")
    @PostMapping("saveUrlRoles")
    public WebJsonBean saveUrlRoles(Long urlId,
    		@JsonParam("urlRolelist") List<Long> urlRolelist) {
        WebCheckUtils.assertNotNull(urlId, "缺少urlId");
        AdminUrlDO adminUrlDO = rolePermissionService.getUrlById(urlId);
        WebCheckUtils.assertNotNull(adminUrlDO, "urlId=" + urlId + "的记录为空");

        boolean success = rolePermissionService.saveByUrlIdAndRoleIds(urlId, urlRolelist);
        if(success) {
            userService.refreshAllUserUrls();
        }
        return success ? WebJsonBean.ok()
                : WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR);
    }

    /**
     * 根据用户角色获取菜单/其他类型url的id
     * @param roleId
     * @return
     */
    @GetMapping("getTreeUrlIdsByRole")
    public WebJsonBean getTreeLastNodeUrlIdsByRole(Long roleId) {
        WebCheckUtils.assertNotNull(roleId, "缺少roleId");
        AdminRoleDO adminRoleDO = rolePermissionService.getRoleById(roleId);
        WebCheckUtils.assertNotNull(adminRoleDO, "roleId=" + roleId + "的记录为空");
        List<RoleUrlRelatedUrlVO> roleUrlRelatedUrlVOList = rolePermissionService.getUrlByRoleId(roleId);

        //roleId 所拥有的 菜单/其他类型的id
        List<Long> roleUrlsMenuAndOthersIds = new ArrayList<>();
        for (RoleUrlRelatedUrlVO roleUrlRelatedUrlVO : roleUrlRelatedUrlVOList) {
            if(roleUrlRelatedUrlVO.getAdminUrlDO() == null) {
                continue;
            }
            UrlTypeEnum type = UrlTypeEnum.getByCode(roleUrlRelatedUrlVO.getAdminUrlDO().getType());
            if (type != UrlTypeEnum.FOLDER) {
                roleUrlsMenuAndOthersIds.add(roleUrlRelatedUrlVO.getAdminUrlDO().getId());
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("roleId", roleId);
        result.put("checkIds", roleUrlsMenuAndOthersIds);

        return WebJsonBean.ok(result);

    }

    @Permission(value = "AdminRoleUrlWrite", name = "系统角色权限操作权限")
    @PostMapping("saveRoleUrls")
    public WebJsonBean saveRoleUrls(Long roleId, @JsonParam("roleUrls") List<Long> roleUrlList) {
        WebCheckUtils.assertNotNull(roleId, "缺少roleId");
        //检查roleId
        AdminRoleDO adminRoleDO = rolePermissionService.getRoleById(roleId);
        WebCheckUtils.assertNotNull(adminRoleDO, "roleId=" + roleId + "的记录为空");

        boolean success = rolePermissionService.saveByRoleIdAndUrlIds(roleId, roleUrlList);

        if(success) {
            userService.refreshAllUserUrls();
        }

        return success ? WebJsonBean.ok()
                : WebJsonBean.fail(AdminErrorCode.COMMON_BIZ_ERROR);
    }
}
