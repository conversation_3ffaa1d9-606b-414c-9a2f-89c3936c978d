package com.pugwoo.admin.web.interceptor;

import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * 自动注入 AdminUserLoginContext<br/>
 * controller接口参数 xxx(AdminUserLoginContext adminUserLoginContext) {...} <br/>
 * 如果用户登录，直接注入用户登录信息，如果用户未登录为null
 * @date 2018-08-02
 */
@Component
public class AdminUserLoginContextArgumentResolver implements HandlerMethodArgumentResolver {

	@Override
	public boolean supportsParameter(MethodParameter parameter) {
		Class<?> clazz = parameter.getParameterType(); // 获取参数类型
		return clazz == AdminUserLoginContext.class;   // 如果类型符合，则返回true进行处理
	}

	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
			NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
		return AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
	}
}
