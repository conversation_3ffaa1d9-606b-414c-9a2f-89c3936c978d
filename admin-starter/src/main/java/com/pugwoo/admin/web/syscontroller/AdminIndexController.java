package com.pugwoo.admin.web.syscontroller;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.config.properties.AdminProperties;
import com.pugwoo.admin.service.AdminRolePermissionService;
import com.pugwoo.admin.utils.NoPermission;
import com.pugwoo.admin.vo.AdminUrlDTO;
import com.pugwoo.admin.vo.MenuDTO;
import com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor;
import com.pugwoo.wooutils.collect.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
public class AdminIndexController {

	@Autowired
	private AdminProperties adminProperties;

	@Autowired
	private AdminRolePermissionService rolePermissionService;

    @NoPermission @GetMapping("/index_menu")
	public WebJsonBean<?> indexMenu() {
		String userName = AdminUserLoginInterceptor.getAdminUserLoginContext().getUserName();
		List<AdminUrlDTO> urls = AdminUserLoginInterceptor.getAdminUserLoginContext().getUrls();
		List<MenuDTO> menus = rolePermissionService.genMenu(urls);

		Map<String, Object> data = MapUtils.of(
				"userName", userName,
				"systemName", adminProperties.getSystemName(),
				"menus", menus);
		return WebJsonBean.ok(data);
	}
	
}
