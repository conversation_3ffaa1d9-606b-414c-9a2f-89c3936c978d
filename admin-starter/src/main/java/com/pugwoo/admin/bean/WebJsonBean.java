package com.pugwoo.admin.bean;

import com.pugwoo.admin.web.weblog.CommonWebLogFilter;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 吐出到前端并序列化为json的bean。默认成功。
 * 
 * <AUTHOR>
 */
@Data
public class WebJsonBean<T> implements Serializable {

	@Serial
	private static final long serialVersionUID = 9016568358919456278L;

	/** 返回码 */
	private int code;
	
	/** 返回码消息 */
	private String msg;
	
	/** 返回数据 */
	private T data;

	/**请求uuid*/
	private String reqUuid;

	/**
	 * 默认是成功
	 */
	public WebJsonBean() {
		this.code = AdminErrorCode.SUCCESS.getCode();
		this.msg = AdminErrorCode.SUCCESS.getName();
		this.reqUuid = CommonWebLogFilter.getRequestUuid();
	}

	/**所有的消息都带上请求uuid*/
	public String getMsg() {
		if (reqUuid == null || reqUuid.isEmpty()) {
			return this.msg;
		} else {
			return this.msg + " [" + this.reqUuid + "]";
		}
	}

	/**
	 * 从ResultBean转换为WebJsonBean
	 */
	public static <T> WebJsonBean<T> of(ResultBean<T> resultBean) {
		WebJsonBean<T> webJsonBean = new WebJsonBean<>();
		webJsonBean.setCode(resultBean.getCode());
		webJsonBean.setMsg(resultBean.getMsg());
		webJsonBean.setData(resultBean.getData());
		return webJsonBean;
	}

	public static WebJsonBean<?> ok() {
		return ok(null);
	}

	public static <T> WebJsonBean<T> ok(T data) {
		WebJsonBean<T> webJsonBean = new WebJsonBean<>();
		webJsonBean.data = data;
		return webJsonBean;
	}

	public static WebJsonBean<?> fail(int code, String errorMsg) {
		WebJsonBean<?> webJsonBean = new WebJsonBean<>();
		webJsonBean.setCode(code);
		webJsonBean.setMsg(errorMsg);
		return webJsonBean;
	}

	public static WebJsonBean<?> fail(ErrorCode code) {
		return fail(code, null, null);
	}

	public static WebJsonBean<?> fail(ErrorCode code, String msg) {
		return fail(code, null, msg);
	}

	/**默认使用错误码COMMON_BIZ_ERROR == 4*/
	public static WebJsonBean<?> fail(String msg) {
		return fail(AdminErrorCode.COMMON_BIZ_ERROR, null, msg);
	}

	public static <T> WebJsonBean<T> fail(ErrorCode code, T data) {
		return fail(code, data, null);
	}

	public static <T> WebJsonBean<T> fail(int code, String errorMsg, T data) {
		WebJsonBean<T> webJsonBean = new WebJsonBean<>();
		webJsonBean.setCode(code);
		webJsonBean.setMsg(errorMsg);
		webJsonBean.setData(data);
		return webJsonBean;
	}

	public static <T> WebJsonBean<T> fail(ErrorCode code, T data, String msg) {
		WebJsonBean<T> webJsonBean = new WebJsonBean<>();
		if(code == null) {
			webJsonBean.code = -1;
			webJsonBean.msg = msg != null ? msg : "系统未知错误";
		} else {
			webJsonBean.code = code.getCode();
			webJsonBean.msg = msg != null ? msg : code.getName();
		}
		webJsonBean.data = data;
		return webJsonBean;
	}

}
