package com.pugwoo.admin.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * Admin内部通用异常，该异常不会记录到db中.<br>
 * 特别说明：业务系统请不要使用该异常，而是自己定义自己的异常，只要实现ErrorCode接口即可
 * <AUTHOR> 2016年10月27日 15:24:06
 */
@Getter
public class AdminInnerException extends RuntimeException {

	@Serial
	private static final long serialVersionUID = 7411054704108959547L;

	@AllArgsConstructor
	@NoArgsConstructor
    @Data
	public static class DefaultErrorCode implements ErrorCode, Serializable {
		@Serial
		private static final long serialVersionUID = 7411054704108959548L;

		private int code;
		private String name;
	}

	/**异常错误信息*/
	private final String errMsg;
	/**异常错误码*/
	private final DefaultErrorCode errorCode;
	
	public AdminInnerException(ErrorCode errorCode) {
		super(errorCode == null ? null : errorCode.getName());
		if(errorCode == null) {
			errorCode = AdminErrorCode.SYSTEM_ERROR;
		}
		this.errMsg = errorCode.getName();
		this.errorCode = new DefaultErrorCode(errorCode.getCode(), errorCode.getName());
	}
	
	public AdminInnerException(ErrorCode errorCode, String errMsg) {
		super(errMsg);
		if(errorCode == null) {
			errorCode = AdminErrorCode.SYSTEM_ERROR;
		}
		this.errMsg = errMsg == null ? errorCode.getName() : errMsg;
		this.errorCode = new DefaultErrorCode(errorCode.getCode(), errorCode.getName());
	}

	public int getCode() {
		return errorCode == null ? -1 : errorCode.getCode();
	}

}
