package com.pugwoo.admin.bean;

/**
 * redis的key前缀命名空间，所有的rediskey都放在这里管理，绝对不允许出现相同前缀key
 * <AUTHOR>
 */
public interface RedisKeyNamespace {

	/**后台管理系统登录token*/
	String ADMIN_LOGIN_TOKEN = "ADMIN_TOKEN-";
	
	/**图形验证码*/
	String CAPTCHA = "CAPTCHA-";

	/**重试密码错误次数*/
	String LIMIT_WRONG_PASSWORD = "LIMIT_WRONG_PASSWORD-";

	/**限制一天发送的最大的异常消息数*/
	String MAX_EXCEPTION_NOTIFY_PER_DAY = "MAX_EXCEPTION_NOTIFY_PER_DAY-";
}
