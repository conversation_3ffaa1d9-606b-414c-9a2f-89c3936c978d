package com.pugwoo.admin.bean;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.util.Date;

/**
 * 数据库表核心字段：自增id，软删除，创建时间，修改时间
 */
@Data
public class AdminCoreDO {

	/**自增id，唯一主键*/
	@Column(value = "id", isAutoIncrement = true, isKey = true)
	private Long id;
	
	/**软删除标记，false未删除，true已删除*/
	@Column(value = "deleted", softDelete = {"0", "1"})
	private Boolean deleted;
	
	/**创建时间，插入记录时自动设置*/
	@Column(value = "create_time", setTimeWhenInsert = true)
	private Date createTime;
	
	/**更新时间，更新记录时自动设置*/
	@Column(value = "update_time",
			setTimeWhenUpdate = true,
			setTimeWhenInsert = true,
	        setTimeWhenDelete = true)
	private Date updateTime;

}
