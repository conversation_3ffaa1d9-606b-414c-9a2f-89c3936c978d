package com.pugwoo.admin.bean;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

/**
 * 支持用户操作记录的核心表字段：创建人id，修改人id。
 * 在整个admin体系内，实现了AdminBaseDO的DO都会自动设置上上述字段
 */
@Data
public class AdminBaseDO extends AdminCoreDO {

	/**创建人id*/
	@Column(value = "create_user_id",
			insertValueScript = "com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor.getPossibleUserId()")
	private Long createUserId;

	/**修改人id*/
	@Column(value = "update_user_id",
	        updateValueScript = "com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor.getPossibleUserId()",
	        deleteValueScript = "com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor.getPossibleUserId()")
	private Long updateUserId;

}
