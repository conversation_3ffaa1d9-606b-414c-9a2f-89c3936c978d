package com.pugwoo.admin.bean;

import lombok.Getter;

/**
 * 错误码：0成功，<0系统错误，>0业务错误
 * <AUTHOR> 2016年10月27日 15:23:58
 */
@Getter
public enum AdminErrorCode implements ErrorCode {
	
	SYSTEM_ERROR(-1, "系统未知错误"),
	DB_ERROR(-2, "数据库异常"),
	REDIS_ERROR(-3, "缓存服务器异常"),
	SOA_ERROR(-4, "SOA异常"),
	
	SUCCESS(0, "成功"),
	
	NOT_LOGIN(1, "未登录"),
	MISSING_PARAMETERS(2,"缺少参数"),
	ILLEGAL_PARAMETERS(3,"参数错误"),
	COMMON_BIZ_ERROR(4, "一般业务异常"), // 【该异常需带上中文的错误说明】
	WRONG_CAPTCHA(5, "验证码错误或已失效"),
	PERMISSION_DENIED(6, "没有权限"),
	EXCEED_LIMIT(7, "超过限额"),
	NO_UPLOAD_PROVIDER(8, "没有配置上传存储服务"),
	EXCEED_CONCURRENT_LIMIT(9, "超过并发数限制"),
	
	// 1000 以上为分段的业务错误代码
	
	// 说明：AdminErrorCode由admin管理，使用方自行维护自己的ErrorCode，只要实现ErrorCode接口即可
	
	;
	
	private final int code;
	
	private final String name;
	
	AdminErrorCode(int code, String name) {
		this.code = code;
		this.name = name;
	}

}
