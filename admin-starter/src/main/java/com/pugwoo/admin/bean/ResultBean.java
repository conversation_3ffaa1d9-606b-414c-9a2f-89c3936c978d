package com.pugwoo.admin.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @param <T>
 */
@Data
public class ResultBean<T> implements Serializable {

	@Serial
	private static final long serialVersionUID = 5186564158918076484L;

	/** 返回码 */
	private int code;

	/** 返回码消息 */
	private String msg;

	/** 返回数据 */
	private T data;

	public ResultBean() {
		this.code = AdminErrorCode.SUCCESS.getCode();
		this.msg = AdminErrorCode.SUCCESS.getName();
	}

	public static ResultBean<?> ok() {
		return ok(null);
	}

	public static <T> ResultBean<T> ok(T data) {
		ResultBean<T> resultBean = new ResultBean<>();
		resultBean.setData(data);
		return resultBean;
	}

	public static <T> ResultBean<T> fail(ErrorCode code) {
		return fail(code, null, null);
	}

	public static <T> ResultBean<T> fail(ErrorCode code, T data) {
		return fail(code, data, null);
	}

	public static <T> ResultBean<T> fail(ErrorCode code, String msg) {
		return fail(code, null, msg);
	}

	/**
	 * 错误信息
	 * @param msg 自定义的错误信息
	 */
    public static <T> ResultBean<T> fail(ErrorCode code, T data, String msg) {
		ResultBean<T> resultBean = new ResultBean<>();
		if(code == null) {
			resultBean.setCode(-1);
			resultBean.setMsg(msg != null ? msg : "系统未知错误");
		} else {
			resultBean.setCode(code.getCode());
			resultBean.setMsg(msg != null ? msg : code.getName());
		}
		resultBean.setData(data);
		return resultBean;
	}

	/**
	 * 是否成功，判断依据是返回码code是否等于AdminErrorCode.SUCCESS的code
	 */
	@JsonIgnore
	public boolean isSuccess() {
		return code == AdminErrorCode.SUCCESS.getCode();
	}

}
