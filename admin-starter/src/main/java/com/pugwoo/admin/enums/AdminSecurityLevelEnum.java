package com.pugwoo.admin.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2018-05-30
 */
@Getter
public enum AdminSecurityLevelEnum {

    STRICT("STRICT", "严格模式", "根据配置限制所有的url，默认模式"),
    LOOSE("LOOSE", "宽松模式", "放行所有ajax的url请求，其他按照严格模式标准校验"),
    NONE("NONE", "不限制模式", "不做任何限制，放行所有的请求");

    private String code;

    private String name;

    private String description;

    AdminSecurityLevelEnum(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public static AdminSecurityLevelEnum getByCode(String code) {
        for(AdminSecurityLevelEnum e : AdminSecurityLevelEnum.values()) {
            if(Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return STRICT;  //默认模式
    }

}
