package com.pugwoo.admin.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 任务触发类型枚举
 */
@Getter
public enum AdminTaskTriggerTypeEnum {

    /**定时任务触发*/
    SCHEDULED("SCHEDULED", "定时任务触发"),
    /**页面触发*/
    MANUAL("MANUAL", "页面触发");

    final private String code;
    final private String name;

    AdminTaskTriggerTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AdminTaskTriggerTypeEnum getByCode(String code) {
        for (AdminTaskTriggerTypeEnum e : AdminTaskTriggerTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        AdminTaskTriggerTypeEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}
