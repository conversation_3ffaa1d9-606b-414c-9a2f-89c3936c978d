package com.pugwoo.admin.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum OperateTypeEnum {

	LOGIN("LOGIN", "登录"),
	LOGOUT("LOGOUT", "退出"),
	OTHER("OTHER", "其它");
	
	private String code;
	
	private String name;
	
	OperateTypeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}
	
	public static OperateTypeEnum getByCode(String code) {
		for(OperateTypeEnum e : OperateTypeEnum.values()) {
			if(Objects.equals(code, e.getCode())) {
				return e;
			}
		}
		return null;
	}
	
}
