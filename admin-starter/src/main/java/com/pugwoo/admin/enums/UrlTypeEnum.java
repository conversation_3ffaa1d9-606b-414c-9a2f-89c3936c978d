package com.pugwoo.admin.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * t_admin_url 类型枚举
 */
@Getter
public enum UrlTypeEnum {

	/**文件夹*/
	FOLDER("FOLDER", "文件夹"),
	/**菜单，其url必须是准确的url，将显示在左侧菜单栏中*/
	MENU("MENU", "菜单"),
	/**其它链接，其url可以是正则表达式，不会显示在左侧菜单栏中*/
	OTHER("OTHER", "其它")
	;

	private String code;

	private String name;

	UrlTypeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public static UrlTypeEnum getByCode(String code) {
		for (UrlTypeEnum e : UrlTypeEnum.values()) {
			if (Objects.equals(e.getCode(), code)) {
				return e;
			}
		}
		return null;
	}

	public static String getNameByCode(String code) {
		UrlTypeEnum e = getByCode(code);
		return e == null ? null : e.getName();
	}

}
