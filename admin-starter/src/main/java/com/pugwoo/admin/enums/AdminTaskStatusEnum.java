package com.pugwoo.admin.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum AdminTaskStatusEnum {

    NEW("NEW", "新建"),
    RUNNING("RUNNING", "运行中"),
    SUCCESS("SUCCESS", "成功"),
    FAIL("FAIL", "失败"),
    SKIPPED("SKIPPED", "跳过");

    final private String code;
    final private String name;

    AdminTaskStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AdminTaskStatusEnum getByCode(String code) {
        for (AdminTaskStatusEnum e : AdminTaskStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        AdminTaskStatusEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}