package com.pugwoo.admin.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * t_admin_user 来源枚举
 */
@Getter
public enum UserSourceEnum {
    /** 本地数据 */
    LOCAL("LOCAL", "本地数据"),

    CUSTOM("CUSTOM", "第三方登陆"),

    ;

    private String code;

    private String name;

    UserSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static UserSourceEnum getByCode(String code) {
        for (UserSourceEnum e : UserSourceEnum.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        UserSourceEnum e = getByCode(code);
        return e == null ? null : e.getName();
    }
}
