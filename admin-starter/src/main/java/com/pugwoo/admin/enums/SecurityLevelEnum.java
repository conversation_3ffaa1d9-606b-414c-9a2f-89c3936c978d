package com.pugwoo.admin.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum SecurityLevelEnum {

	COMMON("COMMON", "正常"),
	CAREFUL("CAREFUL", "谨慎"),
	SECRET("SECRET", "机密"),
	DANGER("DANGER", "危险操作");
	
	private String code;
	
	private String name;
	
	SecurityLevelEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}
	
	public static SecurityLevelEnum getByCode(String code) {
		for(SecurityLevelEnum e : SecurityLevelEnum.values()) {
			if(Objects.equals(code, e.getCode())) {
				return e;
			}
		}
		return null;
	}
	
}
