
v2.1.4 - [enhance] weblog不记录/actuator
       - [add] TaskLog增加记录@Scheduled的定时任务，只要注解了@TaskLog或@Scheduled都会记录

2025年7月9日
v2.1.3 - [upgrade] 升级orm到1.7.6
       - [add] 默认增加spring-boot-starter-actuator和prometheus依赖，打开actuator需要应用自行配置management.endpoints.web.exposure.include=info,health,prometheus

2025年6月9日
v2.1.2 - [upgrade] upgrade woo-utils
       - [enhance] 发送邮件支持用逗号隔开

2025年6月6日
v2.1.1 - [add] AIUtils支持图片作为输入

2025年5月13日
v2.1.0 - [add] 增加AIUtils
       - [upgrade] 升级nimble-orm

2025年4月29日
v2.0.24 - [upgrade] 升级nimble-orm

2025年4月23日
v2.0.23 - [fix] 修复WebResponseWrapper的内存泄漏问题

2025年4月22日
v2.0.22 - [upgrade] upgrade woo-utils

2025年4月22日
v2.0.21 - [fix] 修复WebResponseWrapper的内存泄漏问题

2025年4月21日
v2.0.20 - [add] 增加程序重启告警，默认关闭

2025年4月10日
v2.0.19 - [upgrade] 升级redis-helper至1.5.4

2025年3月14日
v2.0.18 - [enhance] 换一种更简单的方式实现：adminDBHelper默认设置为primary=true，同时允许配置设置为primary=false

2025年3月13日
v2.0.17 - [enhance] 升级woo-utils和webext，将servlet-api依赖改成provided
        - [enhance] adminDBHelper默认设置为primary=true，同时允许配置设置为primary=false

2025年1月14日
v2.0.16 - [add] 增加sentinel熔断机制，默认限制每个接口方法最大并发数为70

2024年11月27日
v2.0.15 - [upgrade] 升级spring boot到3.4.0，升级orm和woo-utils/redis-helper
        - [add] 增加异常告警功能，当请求和定时任务发生异常时，主动告警配置了邮箱的管理员【升级须知，记得配置管理员邮箱】

2024年9月23日
v2.0.14 - [enhance] vue http的请求支持finally callback

2024年9月3日
v2.0.13 - [enhance] 去掉css缩放，以适应新版chrome缩放的变化

2024年9月2日
v2.0.12 - [enhance] 适应新版本的chrome的zoom属性在iframe中被继承，导致里面的页面再次被缩小的问题

2024年7月24日
v2.0.11 - [add] lombok配置增加lombok.toString.callSuper=CALL，这样DTO的打印可以加上父类信息
        - [enhance] web log规避上传大文件时，文件内容读取到内存，可能导致内存撑爆的问题
        - [add] 增加CannotHaveBlank、EnumValid校验器
        - [upgrade] 升级orm redis-helper到最新版本

2024年5月25日
v2.0.10 - [upgrade] 升级nimble-orm/redis-helper/woo-utils至最新版

2024年5月20日
v2.0.9 - [upgrade] 升级woo-utils，修复ThreadPoolUtils内存泄露的问题
       - [del] 移除skywalking依赖

2024年5月13日
v2.0.8 - [upgrade] 升级woo-utils和nimble-orm

2024年4月23日
v2.0.7 - [add] 支持表头sticky黏住，效果非常好，感谢https://github.com/Lruihao/el-table-sticky

2024年4月22日
v2.0.6 - [fix] 修复在Spring Boot 3中，alibaba ttl和MDCAdapter下MDC实际没有生效的问题，解决方案是去掉并使用手工MDCUtils工具类来
       - [add] 增加AdminNotifyService

2024年4月12日
v2.0.5 - [upgrade] 升级腾讯云cos sdk，解决maven检查提示有漏洞的问题
       - [enhance] 进入首页时，无论如何展示菜单

2024年4月10日
v2.0.4 - [enhance] 缩放比例调整为80%
       - [enhance] 左侧菜单如果是收起状态，那么打开新tab也是收起状态

2024年4月7日
v2.0.3 - [fix] 修复t_admin_log_slow_sql插入sql太长的问题
       - [enhance] 缩放页面一定比例

2024年4月5日
v2.0.2 - [fix] 修复t_admin_log_slow_sql插入sql太长的问题
       - [add] 支持按esc来显示和隐藏菜单

2024年2月13日
v2.0.1 - [fix] 修复velocity starter IDE正常但是打包成jar包后模板异常的问题，升级注意：
         1) 所有的模板的velocity开头的路径，要把velocity去掉，然后自行测试一下
         2) velocity模版中的$velocityCount要换成$foreach.count

2024年2月6日
v2.0.0 - [upgrade] 升级为spring boot 3，升级注意：
         1) redis的配置要从spring.redis改成spring.data.redis
         2) 所有javax.servlet和javax.validation及可能其他的javax开头的import要改成jakarta

2024年2月6日
v1.3.2 - [enhance] 错误信息pin住，不自动消失
       - [upgrade] 升级webext-spring-boot-starter以便后续升级到spring boot3
       - [upgrade] 其它pom的升级，该版本为spring 2.x的最后一个版本

2023年11月22日
v1.3.1 - [fix] 修复扫描url的问题

2023年11月4日
v1.3.0 - [upgrade] 升级到jdk 21，因为spring boot 3不兼容spring boot2的starter，老的像velocity不支持，故不升级到spring boot 3
       - [add] 增加log打印时线程池requestUuid的传递支持
       - [add] 定时任务增加traceUuid支持

2023年9月28日
v1.2.6 - [upgrade] 升级spring boot到2.7.16以支持jdk21, com.pugwoo系列升级到最新版本

2023年8月29日
v1.2.5 - [upgrade] element-ui升级到2.15.14; common-utils.js压缩
       - [add] 重新支持js/css文件放到cos，支持配置是否启用，默认不启用

2023年8月26日
v1.2.4 - [add] 支持企业微信发送消息

2023年7月13日
v1.2.3 - [add] 增加@TaskLog切面记录定时任务执行记录
       - [add] 支持将所有的网络请求记录到数据库t_admin_log_web中

2023年7月8日
v1.2.2 - [enhance] 优化缓存续期的性能，1分钟续期1次
       - [add] 支持自定义异常实现ErrorCode后，可以自定义异常的返回码
       - [add] 支持钉钉发送消息

2023年6月8日
v1.2.1 - [enhance] 允许停止慢url提示
       - [upgrade] 升级woo-utils -> 1.1.12; redis-helper -> 1.2.7

2023年6月5日
v1.2.0 - [add] 对于网络错误，增加自定义的回调，这样可以全覆盖一个网络请求的所有回调
       - [simplify] 简化返回值，不需要再包装一层WebJsonBean了，会直接由切面处理
       - [del] 移除对LDAP的支持
       - [add] 支持自定义登陆鉴权，通过实现CustomLoginHandler接口
       - [del] 移除clearUserLoginContext方法，不需要调用者调用
       - [upgrade] 升级redis-helper->1.2.6;nimble-orm->1.5.3

2023年5月22日
v1.1.5 - [fix] 修复查看log时的sql问题

2023年5月21日
v1.1.4 - [fix] 升级webext-spring-boot-starter到0.1.1版本，修复LocalDateTime的序列化问题

2023年4月17日
v1.1.3 - [upgrade] 升级nimble-orm -> 1.5.0/redis-helper->1.2.5
       - [upgrade] element-ui升级到2.15.13; vue升级到2.7.14

2023年4月8日
v1.1.2 - [upgrade] 升级spring boot->2.7.10/nimble-orm->1.4.5/woo-utils->1.1.9/redis-helper->1.2.4
       - [del] 移除RedisHelperInMemoryImpl，以后redis是强依赖，不再支持内存模式

2022年10月12日
v1.1.1 - [fix] 修复当发送邮件没有设置text和content时，发送报错的问题
       - [enhance] err-log只记录ERROR级别，不再记录WARN级别
       - [fix] 修复升级到spring boot 2.4+之后扫描spring mvc url获取不到的问题

2022年8月30日
v1.1.0 - [add] 增加skywalking的支持，使用skywalking的traceId作为日志和orm的requestUuid
       - [fix] 修复登录验证码输入不了的问题
       - [upgrade] springboot->2.7.3;woo-utils->1.1.2;redis-helper->1.2.3;nimble-orm->1.3.4
       - [upgrade] element-ui升级到2.15.9; vue升级到2.7.10; vue-resource升级到1.5.3

2022年6月24日
v1.0.4 - [upgrade] 升级maven依赖

2022年6月24日
v1.0.3 - [fix] 修复从spring mvc查url时的空指针异常

2022年2月3日
v1.0.2 - [add] 日志打印request uuid，方便日志查询
       - [enhance] 添加lombok.config，消除继承类@Data的告警
       - [upgrade] spring boot升级到2.5.9; woo-utils升级到1.0.8; redis-helper升级到1.1.5
       - [add] WebJsonBean返回体增加reqUuid；msg消息带上reqUuid

2022年1月18日
v1.0.1 - [fix] 修复查询权限时，传参不对的问题
       - [upgrade] spring boot升级到2.5.8; webext升级到0.1.0; woo-utils升级到1.0.6; redis-helper升级到1.1.4; nimble-orm升级到1.1.4
       - [modify] 异步记录日志使用本地异步线程池代替redis消息队列

2021年7月20日
v1.0.0 - [del] 【重要】删除admin的RedisHelperProperties，不再支持admin.redis配置，请使用spring.redis配置
       - [enhance] AdminLogSlowWebDO ip字段的长度限定为最大256，并设置在DO上
       - [upgrade] spring boot升级到2.4.8，其它小升级
       - [enhance] admin配置用Properties和注释文档管理起来
       - [enhance] admin的redis接收消息设置超时时间为30秒

2021年6月11日
v0.9.7 - [fix] 升级nimble-orm到1.0.1，修复Date对象只能拿到日期的问题

2021年5月5日
v0.9.6 - [upgrade] 升级spring boot到2.3.10，升级nimble-orm到1.0.0；
       - [upgrade] element升级到2.15.1；vue升级到2.6.12；vue-resource升级到1.5.2
       - [del] 移除腾讯云cos上的element前端文件，因为现在带宽速度可以了，而且有缓存，这种方式不必要了

2020年11月6日
v0.9.5 - [modify] DBHelperConfiguration重命名为 AdminDBHelperConfiguration，config下面的几个配置也都加上admin前缀
       - [fix] 修复之前弄IWebLogCallback而导致没有打印POST queryString的问题
       - [upgrade] 升级spring boot到2.3.5
       - [del] 删除角色的独立权限标记，现在不知道为何当初有这个需求了，也没有项目用到，本次删除在一次commit中删除，以后有需要再来
       - [enhance] 登录时，对一个账户而言，每天5次输错密码才会提示验证码

2020年8月24日
v0.9.4 - [add] 增加异常日志独立打印到error.log文件
       - [add] 增加com.pugwoo.admin.SpringContext类，方便arthas获取

2020年7月10日
v0.9.3 - [add] common-utils.js增加alert弹框提示
       - [upgrade] spring升级到2.2.8，woo-utils升级到0.9.0
       - [del] 去掉favicon

2020年6月22日
v0.9.2 - [upgrade] 升级nimble-orm到0.9.8,解决了插入反查id错乱的问题
       - [improve] 前端common-utils提示错误消息兼容msg和message两种字段名称

2020年5月28日
v0.9.1 - [upgrade] 资源本地化（默认），也支持cdn方式
       - [improve] 升级element到2.13.2

2020年5月12日
v0.9.0 - [fix] 修复当慢sql时间过短时，死循环的问题（解决方案：写入慢sql的记录的数据库操作不受慢sql拦截器影响）
       - [improve] 使用nimble-orm脚本的形式代替拦截器，相当于admin-template不强制使用拦截器
       - [improve] 使用redis消息队列方式异步写log
       - [improve] 慢web记录从filter移到拦截器来做，这样才可以获取到登录用户信息（去掉原类和方法信息的记录）
       - [upgrade] 升级pom中的依赖

2020年3月13日
v0.8.7 - [add] 打印日志增加callback回调支持，增加request uuid生成支持
       - [fix] 修复RepeatedlyReadRequestWrapper getReader当body为null是空指针问题

2020年3月4日
v0.8.6 - [fix] 修复url抛异常的时候weblog打印不正确的bug，修复velocity渲染没有打weblog的问题
       - [update] nimble-orm升级到0.9.6，升级spring boot到2.2.5

2020年1月20日
v0.8.5 - [fix] 修复慢url提示异常getTimeout2
       - [fix] 优化异常的json显示处理和db日志

2020年1月18日
v0.8.4 - [fix] 修复当用户没有配置redis时，还使用redis连接的问题
       - [fix] 修复LDAP验证密码的问题
       - [update] 升级spring boot到2.2.3，woo-utils到0.8.6

2020年1月13日
v0.8.3 - [fix] 修复打印日志工具不打印POST queryString内容的问题，经过测试，现在spring boot 2.0.x 2.1.x 2.2.x都没有问题
       - [add] 增加独立角色权限功能，支持需要特定组合权限的需求

2019年12月26日
v0.8.2 - [add] 多数据源支持，mysql和redis都支持，详见README配置说明
       - [add] WebJsonBean支持指定code和errmsg进行构造
       - [improve] 优化AdminInnerException的异常处理
       - [del] 去掉frame页面内跳转时更新url的功能，该功能会导致返回键要按两次

2019年12月6日
v0.8.1 - [improve] 为定时任务配置ScheduleConfigure加上名称，以免和其它starter冲突
       - [improve] 升级element到2.13

2019年12月1日
v0.8.0 - [improve] element弹框点击外面灰色或鼠标弹框内点击然后滑动到框外时弹框消失，去掉这个
       - [improve] 全部改成前后端分离方式
       - [del] 默认去掉csrf校验，只靠Get/Post的区分来保证；也可以手动开启csrf增加安全性
       - [improve] 自动刷新用户权限

2019年11月13日
v0.7.9 - [fix] 上传文件保存的名称中去掉encode，encode会导致下载不了文件

2019年11月12日
v0.7.8 - [fix] 彻底修复spring 2.2.x打印请求参数为null的问题

2019年11月11日
v0.7.7 - [fix] 修复spring 2.2.x在开启请求打印时，上传文件错误的问题

2019年10月28日
v0.7.6 - [update] 更新pom，升级springboot到2.2.0，升级woo-utils
       - [improve] 增加csrf检查开关，默认开启；对于上传文件且不需要登录态的，不检查csrf

2019年10月4日
v0.7.5 - [del] 去掉Environment的isLocal方法，不再区分，本地也控制csrf
       - [update] 更新pom

2019年7月23日
v0.7.4 - [improve] 异常记录增加错误码<0的AdminInnerException
       - [add] 增加对http方法不支持的错误提示
       - [add] 增加日志格式的自动配置，增加压缩，修改日志格式
       - [add] 默认自动开启分布式锁和高速缓存@Synchronized和@HiSpeedCache
       - [improve] 优化慢web记录，默认慢速阈值改成3秒（原来1秒）并支持配置，记录慢sql改成异步
       - [add] 默认启动定时任务，并把线程大小设置为10
       - [add] 增加web打印log的功能，默认关闭

2019年5月31日
v0.7.3 - [modify] 将csrf检查从woo-utils中移动到WebCheckUtils中，到时候woo-utils将删除该功能
       - [improve] AdminInnerException入参改为接口ErrorCode

2019年5月14日
v0.7.2 - [improve] 整理建表sql和初始化数据sql
       - [add] 增加LDAP支持

2019年5月6日
v0.7.1 - [improve] 使用权限注解表达权限
       - [fix] 修复升级到springboot 2.1.x之后，因为引入了mysql驱动8.x版本导致的时区问题

2019年4月23日
v0.7.0 - [fix] 修复权限目录在多层空目录时，依然显示的问题；优化权限缓存，空目录就不存redis了
       - [del] 移除Environment工具中isWindows方法和默认环境不限制权限的设置
       - [code clean] 所有getter/setter使用lombok注解形式
       - [improve] 优化全局异常处理器处理逻辑，正常处理3种异常
       - [fix] 抽取图形验证码工具，规避多线程安全问题
       - [improve] 优化登录时密码输入错误的体验，自动清除验证码和刷新验证码

2019年4月11日
v0.6.8 - [modify] 去掉ResultBean继承WebJsonBean，方法改成和WebJsonBean相同，WebJsonBean增加of方法，WebJsonBean的success改成ok

2019年4月9日
v0.6.7 - [update] 升级若干jar包依赖
       - [del] 移除项目中的前端css/js文件，放到腾讯云cos了
       - [update] 升级vue到2.6.10，升级element到2.7.2

2019年2月19日
v0.6.6 - [add] 支持腾讯云指定域名，即支持绑定自定义域名

2018年12月13日
v0.6.5 - [update] 使用腾讯云cos放js和css，升级elementui vue版本

2018年10月20日
v0.6.4 - [improve] 修改WebJsonBean和ResultBean的构造方式，改成工厂模式，更加清晰直观

2018年9月17日
v0.6.3 - [fix] 升级webext-spring-boot-starter到0.3.0，修复String转Date对自定义POJO无效的问题

2018年9月15日
v0.6.2 - [fix] 修复验证码第一次输入总是错误的问题,前端总会对同一个验证的图片请求2或3次(页面第一次加载时),通过延迟来处理

2018年9月8日
v0.6.1 - [del] 不再提供logback.xml，使用springBoot默认的配置方式，详见admin-starter-example的properties文件
         [update] vue version 2.5.17
         [update] element-ui version 2.4.6
         [update] vue-resource version 1.5.1
         [update] 升级dbhelper到0.9.0

2018年9月5日
v0.6.0 - [del] 去掉AdminBaseDO的createUserName和updateUserName两个字段，注意数据库变更情况

2018年9月5日
v0.5.4 - [improve] 对于Spring提供的Controller，认为不需要登录来处理
       - [improve] 升级spring boot版本为2.0.4

2018年8月2日
v0.5.3 - [improve] 增加groovy测试demo，优化BindException处理

2018年7月24日
v0.5.2 - [fix] 修复dbhelper慢速callback spring循环依赖问题
       - [update] 升级dbhelper至0.8.7版本

2018年7月23日
v0.5.1 - [fix] 修复starter中bean名称配置错的问题

2018年7月17日
v0.5.0 - [add] 增加spring boot版本的admin框架

2018年7月15日
v0.4.2 - [improve] 登录拦截器完成时，清除登录上下文context

2018年7月2日
v0.4.1 - [improve] 左侧菜单栏按住Ctrl时直接打开无左侧菜单的页面
       - [add] 增加发送邮件工具

2018年6月30日
v0.4.0 - [improve] PageUtils.trans的属性名称由list改成data，保持和PageData属性值一致

2018年6月29日
v0.3.6 - [fix] 修复全局异常处理器对api的判断依据: 缺少@RestController

2018年6月27日
v0.3.5 - [improve] 对于不存在的链接，只对其它类型标红，不对菜单标红
       - [fix] 修复url无法选择类型的问题
       - [improve] 没有设置url的其它或菜单类型，可以转变为目录类型
       - [improve] url右编辑框fixed在页面右边

2018年6月18日
v0.3.4 - [improve] 对于上传时中文名称的，使用urlencode一下
       - [fix] 更新woo-utils到0.4.4，该版本修复上传文件的CSRF校验问题

2018年6月17日
v0.3.3 - [improve] 优化登录逻辑，支持redirect跳转

2018年6月17日
v0.3.2 - [improve] 去掉common-utils中的Nav, 改用iframe外层主动监听iframe变化来改变父窗口url

2018年6月16日
v0.3.1 - [improve] 优化common-utils中setTimeout第一个参数是字符串的写法为function
       - [del] 将elex、wangeditor、china-area-utils放到演示中，由用户自行添加

2018年6月14日
v0.3.0 - [update] elex升级到2.4.1版本，element-ui升级到2.4.1版本

2018年6月12日
v0.2.9 - [update] common-utils中Utils的goUrl openUrl changeUrl方法放到Nav中

2018年6月11日
v0.2.8 - [update] nimble-orm升级至0.8.5

2018年6月10日
v0.2.7 - [add] 字典编辑值时支持上下调整顺序
       - [add] 中国行政区级联选择器组件
       - [improve] ResultBean增加isSucc方法和几个更方便的构造方法
       - [improve] 当Resource请求返回没有登录时，刷新当前页面以登录

2018年6月7日
v0.2.6 - [fix] 修复user-select重置没有清除值的问题
       - [improve] 优化TreeUtils的使用,clean code
       - [add] url树形结构增加全部展开/收起按钮
       - [add] 当配置的url不在系统扫描列表中，则红色提示
       - [improve] url树保存后，仍保持原来的节点展开
       - [add] 部门树形结构后面显示该部门的人数
       - [add] 用户信息增加email和个人签名，个人资料编辑页也加上
       - [improve] 角色编号提示不可随意修改

2018年6月6日
v0.2.5 - [update] nimble-orm升至0.8.4,woo-utils至0.4.2,spring-mvc-conf至0.2.5, spring至4.3.17.RELEASE等
       - [add] WebCheckUtils增加assertRegexMatch和assertPhone
       - [improve] 修复首页菜单左下角几个按钮隐藏时跳动的问题
       - [add] 增加个人编辑页
       - [add] common-utils中加入Utils.changeUrl方法，允许单独修改父级iframe url
       - [fix] 修复tabs切换后刷新，title前缀没显示的问题
       
2018年6月5日
v0.2.4 - [add] WebCheckUtils增加清除基础信息的方法clearBaseInfo
       - [improve] 优化user-select，增加了缓存，解决多个组件一次请求多次的问题
       - [add] 增加Form.emailValidator校验
       - [add] 提供Utils.goUrl/openUrl工具，解决iframe页面间跳转的问题; a8e9944c0c是admin-template-by-pugwoo的md5前缀

2018年6月5日
v0.2.3 - [fix] 修复common-utils中telValidator指错的问题
       - [fix] 去掉数据库拦截器拦截新增时间、人、修改时间、修改人，导致对updateWithNull更新有问题的问题

2018年6月4日
v0.2.2 - [fix] 修复user-select组件当传入的值未定义时的处理

2018年6月4日
v0.2.1 - [fix] 修复user-select组件的清空和选中问题，支持多个值的初始化，解决回显值必须是字符串的限制
       - [improve] 数据库拦截器限定新增时间、人、修改时间、修改人不能由用户自行制定
       
2018年6月4日
v0.2.0 - [add] 开发环境中vue使用非min版本
       - [improve] ErrorCode修改为接口，AdminWebJsonBean改成 WebJsonBean，AdminResultBean改成ResultBean

2018年6月1日
v0.1.7 - [add] 增加数据字典功能，增加了表t_admin_dict和t_admin_dict_value
       - [improve] 相关页面优化

2018年5月31日
v0.1.6 - [improve] 修改左侧菜单栏颜色
       - [add] 增加数据库连接testOnBorrow配置，解决本地开发数据库老是僵死的问题
       - [improve] 将下载演示移到admin-demo中，优化页面
       - [improve] 删除用户则清除该用户所有登陆态

2018年5月31日
v0.1.5 - [add] 权限管理增加STRICT、LOOSE、NONE模式
       - [improve] 其它页面优化
       - [fix] 修复去掉用户管理员身份后，redis还存在管理员身份的问题

2018年5月29日
v0.1.4 - [add] 腾讯云存储对象替代七牛云，AdminUploadController加入admin中，支持多文件上传
       - [improve] 自动扫描：过滤掉不需要登录的url和首页
       
2018年5月27日
v0.1.3 - [add] 增加查询用户的自动完成工具
       - [modify] 修改用户列表查询为查询用户名、真实姓名、手机号的模糊搜索

2018年5月25日
v0.1.2 - [update] 升级dbhelper到0.8.3

2018年5月23日
v0.1.1 - [improve] IReadableDO改名为IAdminReadableDO
       - [improve] 优化页面左侧菜单颜色，换掉收起的箭头

2018年5月19日
v0.1.0 - [init] 基本可用版本，基本框架，含用户(部门)、权限(角色/URL)、监控3个模块
       - [init] 版本: spring4.3.16.RELEASE, vue2.5.16, elementUI2.3.9