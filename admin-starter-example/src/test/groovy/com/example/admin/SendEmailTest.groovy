package com.example.admin

import com.pugwoo.admin.config.AdminMailConfiguration
import com.pugwoo.admin.utils.mail.MailDTO
import com.pugwoo.admin.utils.mail.MailUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import spock.lang.Specification

import javax.mail.MessagingException

/**
 * @date 2018-07-25
 */
@SpringBootTest(classes = AdminMailConfiguration.class)
class SendEmailTest extends Specification{

    @Autowired
    private MailUtils mailUtils;

    def "sendEmail"() {
        setup: "set base info"
        List<String> mailList = new ArrayList<>();
        mailList.add("<EMAIL>");
        String title = "邮箱标题";
        String text = "邮箱内容" + System.currentTimeMillis();

        and: "create mailDTO and set properties"
        MailDTO mailDTO = new MailDTO()
        mailDTO.setRecvEmails(mailList)
        mailDTO.setSubject(title);
        mailDTO.setText(text);

        when: "send mail"
        mailUtils.sendMail(mailDTO)

        then: "assert exception"
        notThrown(MessagingException)

    }
}
