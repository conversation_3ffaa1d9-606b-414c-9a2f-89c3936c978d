package com.example.admin.spocktest

import spock.lang.Specification

/**
 * @date 2018-07-25
 */
public class BlockDemoTest extends Specification {

    /**
     * setup    初始化
     * when     执行 ↓
     * then     判断 ↑ 成对使用
     * expect   简化版 when+then
     * cleanup  finally，如资源关闭等
     * where
     */
    // @Unroll 见下方where说明
//    @Unroll
    def "blocks demo"() {
//===============setup block
        // setup也可以写成given，
        // 在这个block中会放置与这个测试函数相关的初始化程序
        setup:
        def stack = new Stack()
        def elem = "push me"
//===============断言
        // 在then或expect中会默认assert所有返回值是boolean型的顶级语句。
        // 如果要在其它地方增加断言，需要显式增加assert关键字
        assert stack.empty

//===============when and then block
        // when与then需要搭配使用，
        // 在when中执行待测试的函数，
        // 在then中判断是否符合预期
        when:
        stack.push(elem)

        then:
        !stack.empty
        stack.size() == 1
        stack.peek() == elem

//===============异常断言
        // 如果要验证有没有抛出异常，可以用thrown()
        // 如果要验证没有抛出某种异常，可以用notThrown()
        // 要获取抛出的异常对象，可以用以下语法
        when:
        stack.pop()

        then:
//        thrown(EmptyStackException)
        notThrown(EmptyStackException)
        stack.empty

//===============获取抛出的异常对象
        when:
        stack.pop()

        then:
        def e = thrown(EmptyStackException)
        e.cause == null

//===============expect blocks
        // 精简版的 when+then
        when:
        def x = Math.max(a, b)

        then:
        x == c
        // ↓ 简化
//        expect:
//        Math.max(1,2) == 2
//        Math.max(a, b) == c     // ↓
//===============where blocks
        // 如果方法上没有@Unroll注释，则相当于for执行三次
        // 如果方法上有@Unroll注释，则该方法会执行三次
        where:
        a|b||c                  // ↑
        3|5||5
        7|0||7
        0|0||0

//===============cleanup blocks
        // 函数退出前做一些清理工作，如关闭资源等
    }

    // where 的另一种写法
    def "another where demo"() {
        expect:
        bigger == result

        where:
        a|_
        3|_
        7|_
        0|_

        b << [5,0,0]

        bigger = a>b?a:b

        result << [5,7,0]
    }
}
