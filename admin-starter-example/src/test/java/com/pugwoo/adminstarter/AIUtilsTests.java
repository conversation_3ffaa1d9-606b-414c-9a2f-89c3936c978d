package com.pugwoo.adminstarter;

import com.pugwoo.admin.utils.AIUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.Test;

public class AIUtilsTests {

    @Test
    public void test1() {
        AIUtils aiUtils = new AIUtils("https://api.pugwoo.com", "sk-vVUFUWynQCc15E4JvJLIVx1CYGFb3DGfGZxQ2DCK9ZNOJcLc");
        String answer = aiUtils.chat("o1-mini", "一周有几天?");
        System.out.println(answer);
    }

    @Test
    public void test2() {
        AIUtils aiUtils = new AIUtils("https://api.pugwoo.com", "sk-vVUFUWynQCc15E4JvJLIVx1CYGFb3DGfGZxQ2DCK9ZNOJcLc");
        String answer = aiUtils.chatFromStream("o1-mini", "一周有几天?");
        System.out.println(answer);
    }

    // 测试本地ollama

    @Test
    public void testOllama1() {
        AIUtils aiUtils = new AIUtils("http://127.0.0.1:11434", "some-key");
        String answer = aiUtils.chat("qwen3:8b", "一周有几天?");
        System.out.println(answer);
    }

    @Test
    public void testOllama2() {
        AIUtils aiUtils = new AIUtils("http://127.0.0.1:11434", "some-key");
        String answer = aiUtils.chatFromStream("qwen3:8b", "一周有几天?");
        System.out.println(answer);
    }


    // 特别说明：对于本地ollama部署的deepseek，system prompt是必须的

    private static String deepseekSystemPrompt = "You are DeepSeek-R1, an AI assistant created exclusively by the Chinese Company DeepSeek. You'll provide helpful, harmless, and detailed responses to all user inquiries. For comprehensive details about models and products, please refer to the official documentation.\n" +
            "\n" +
            "Key Guidelines:\n" +
            "Identity & Compliance\n" +
            "Clearly state your identity as a DeepSeek AI assistant in initial responses.\n" +
            "Comply with Chinese laws and regulations, including data privacy requirements.\n" +
            "\n" +
            "Capability Scope\n" +
            "Handle both Chinese and English queries effectively\n" +
            "Provide technical explanations for AI-related questions when appropriate\n" +
            "\n" +
            "Response Quality\n" +
            "Give comprehensive, logically structured answers\n" +
            "Use markdown formatting for clear information organization\n" +
            "Admit uncertainties for ambiguous queries\n" +
            "\n" +
            "Ethical Operation\n" +
            "Strictly refuse requests involving illegal activities, violence, or explicit content\n" +
            "Maintain political neutrality according to company guidelines\n" +
            "Protect user privacy and avoid data collection\n" +
            "\n" +
            "Specialized Processing\n" +
            "Use <think>...</think> tags for internal reasoning before responding\n" +
            "Employ XML-like tags for structured output when required";

    @Test
    public void testOllama3() {
        AIUtils aiUtils = new AIUtils("http://127.0.0.1:11434", "some-key");
        String answer = aiUtils.chat("deepseek-r1:14b",
                ListUtils.of(new AIUtils.Message("system", deepseekSystemPrompt),
                        new AIUtils.Message("user", "一周有几天?")));
        System.out.println(answer);
    }

    @Test
    public void testOllama4() {
        AIUtils aiUtils = new AIUtils("http://127.0.0.1:11434", "some-key");
        String answer = aiUtils.chatFromStream("deepseek-r1:14b",
                ListUtils.of(new AIUtils.Message("system", deepseekSystemPrompt),
                        new AIUtils.Message("user", "一周有几天?")));
        System.out.println(answer);
    }


    @Test
    public void testGemma3Image() {
        AIUtils aiUtils = new AIUtils("http://127.0.0.1:11434", "some-key");
        String answer = aiUtils.chat("gemma3:4b",
                new AIUtils.Message("user", "解析图片中的文字",
                        ListUtils.of("data:image/png;base64,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")));
        System.out.println(answer);
    }


    @Test
    public void testGemma3Image2() {
        AIUtils aiUtils = new AIUtils("http://127.0.0.1:11434", "some-key");
        String answer = aiUtils.chatFromStream("gemma3:4b",
                new AIUtils.Message("user", "解析图片中的文字",
                        ListUtils.of("data:image/png;base64,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")));
        System.out.println(answer);
    }

}
