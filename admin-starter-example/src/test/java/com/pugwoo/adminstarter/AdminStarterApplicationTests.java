package com.pugwoo.adminstarter;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.example.admin.AdminStarterApplication;
import com.pugwoo.admin.service.AdminUserService;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AdminStarterApplication.class)
public class AdminStarterApplicationTests {
	
	@Autowired
	private AdminUserService adminUserService;

	@Test
	public void contextLoads() {
	    System.out.println(adminUserService);
	}

}
