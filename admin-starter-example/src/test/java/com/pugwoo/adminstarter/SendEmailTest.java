package com.pugwoo.adminstarter;

import com.pugwoo.admin.config.AdminMailConfiguration;
import com.pugwoo.admin.utils.mail.MailDTO;
import com.pugwoo.admin.utils.mail.MailUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.mail.MessagingException;
import java.util.ArrayList;
import java.util.List;

/**
 * @date 2018-07-25
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AdminMailConfiguration.class)
public class SendEmailTest {

	@Autowired
	private MailUtils mailUtils;

	@Test
	public void sendEmail() {

		List<String> mailList = new ArrayList<>();
		mailList.add("<EMAIL>");

		String title = "邮箱标题";
		String text = "邮箱内容1111";

		MailDTO mailDTO = new MailDTO();
		mailDTO.setRecvEmails(mailList);
		mailDTO.setSubject(title);
		mailDTO.setText(text);
		try {
			mailUtils.sendMail(mailDTO);
		} catch (MessagingException e) {

		}

	}
}
