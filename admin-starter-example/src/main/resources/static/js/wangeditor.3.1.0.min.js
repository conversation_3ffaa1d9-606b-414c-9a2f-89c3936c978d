(function(e,t){'object'==typeof exports&&'object'==typeof module?module.exports=t():'function'==typeof define&&define.amd?define([],t):'object'==typeof exports?exports.keen=t():e.keen=t()})(this,function(){return function(e){function t(o){if(n[o])return n[o].exports;var A=n[o]={i:o,l:!1,exports:{}};return e[o].call(A.exports,A,A.exports,t),A.l=!0,A.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var n=e&&e.__esModule?function(){return e['default']}:function(){return e};return t.d(n,'a',n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p='',t(t.s=2)}([function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(6),A=function(e){return e&&e.__esModule?e:{default:e}}(o);t.default={name:'wang-editor',props:{value:{type:String},action:{type:String},width:{type:String,default:''},height:{type:String,default:''}},data:function(){return{editor:'',inputtime:0}},watch:{value:function(e){50<Date.now()-this.inputtime&&this.editor.txt.text(e)}},mounted:function(){var e=this;this.editor=new A.default(this.$refs.toolbar,this.$refs.content);var t=this.editor;t.customConfig.uploadImgServer=this.action,t.customConfig.uploadFileName='file',t.customConfig.uploadImgHooks={before:function(){},success:function(){},fail:function(){},error:function(){},timeout:function(){},customInsert:function(e,t){e(t.data.url)}},t.customConfig.menus=['head','bold','italic','underline','strikeThrough','foreColor','backColor','link','list','justify','quote','emoticon','image','table','video','code','undo','redo'],t.customConfig.zIndex=0,t.customConfig.onchange=function(t){e.inputtime=Date.now(),e.$emit('input',t)},t.create(),t.txt.text(this.value)}}},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0});var o=n(0),A=n.n(o);for(var i in o)0>['default','default'].indexOf(i)&&function(e){n.d(t,e,function(){return o[e]})}(i);var a=n(5),l=n(4),c=n.i(l.a)(A.a,a.a,a.b,!1,function(){n(3)},null,null);t['default']=c.exports},function(e,t,n){'use strict';Object.defineProperty(t,'__esModule',{value:!0}),t.WangEditor=void 0;var o=n(1),A=function(e){return e&&e.__esModule?e:{default:e}}(o),i={WangEditor:A.default,install:function(e){e.component('wang-editor',A.default)}};'undefined'!=typeof window&&window.Vue&&window.Vue.use(i),t.default=i,t.WangEditor=A.default},function(){},function(e,t){'use strict';t.a=function(e,t,n,o,A,i,a,l){e=e||{};var c=typeof e.default;('object'==c||'function'==c)&&(e=e.default);var r='function'==typeof e?e.options:e;t&&(r.render=t,r.staticRenderFns=n,r._compiled=!0),o&&(r.functional=!0),i&&(r._scopeId=i);var s;if(a?(s=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||'undefined'==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),A&&A.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(a)},r._ssrRegister=s):A&&(s=l?function(){A.call(this,this.$root.$options.shadowRoot)}:A),s)if(r.functional){r._injectStyles=s;var d=r.render;r.render=function(e,t){return s.call(t),d(e,t)}}else{var p=r.beforeCreate;r.beforeCreate=p?[].concat(p,s):[s]}return{exports:e,options:r}}},function(e,t,n){'use strict';n.d(t,'a',function(){return o}),n.d(t,'b',function(){return A});var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n('div',{style:'width:'+e.width},[n('div',{ref:'toolbar',staticClass:'toolbar'}),e._v(' '),n('div',{ref:'content',staticClass:'content',style:'height:'+e.height})])},A=[]},function(e){(function(t,n){e.exports=n()})(this,function(){'use strict';function e(e){var t;return t=document.createElement('div'),t.innerHTML=e,t.children}function t(e){return!!e&&(e instanceof HTMLCollection||e instanceof NodeList)}function n(e){var n=document.querySelectorAll(e);return t(n)?n:[n]}function o(A){if(A){if(A instanceof o)return A;this.selector=A;var a=A.nodeType,l=[];9===a?l=[A]:1===a?l=[A]:t(A)||A instanceof Array?l=A:'string'==typeof A&&(A=A.replace('/\n/mg','').trim(),l=0===A.indexOf('<')?e(A):n(A));var c=l.length;if(!c)return this;var r;for(r=0;r<c;r++)this[r]=l[r];this.length=c}}function A(e){return new o(e)}function i(e,t){var n,o;for(n in e)if(e.hasOwnProperty(n)&&(o=t.call(e,n,e[n]),!1===o))break}function a(e,t){var n,o,A,i=e.length||0;for(n=0;n<i&&(o=e[n],A=t.call(e,o,n),!1!==A);n++);}function l(e){return e+Math.random().toString().slice(2)}function c(e){return null==e?'':e.replace(/</gm,'&lt;').replace(/>/gm,'&gt;').replace(/"/gm,'&quot;')}function r(e){return'function'==typeof e}function s(e){this.editor=e,this.$elem=A('<div class="w-e-menu">\n            <i class="w-e-icon-bold"><i/>\n        </div>'),this.type='click',this._active=!1}function d(e,t){var n=this,o=e.editor;this.menu=e,this.opt=t;var i,a=A('<div class="w-e-droplist"></div>'),l=t.$title;l&&(i=l.html(),i=$(o,i),l.html(i),l.addClass('w-e-dp-title'),a.append(l));var c=t.list||[],r=t.type||'list',s=t.onClick||O,d=A('<ul class="'+('list'===r?'w-e-list':'w-e-block')+'"></ul>');a.append(d),c.forEach(function(e){var t=e.$elem,i=t.html();i=$(o,i),t.html(i);var a=e.value,l=A('<li class="w-e-item"></li>');t&&(l.append(t),d.append(l),l.on('click',function(){s(a),n.hideTimeoutId=setTimeout(function(){n.hide()},0)}))}),a.on('mouseleave',function(){n.hideTimeoutId=setTimeout(function(){n.hide()},0)}),this.$container=a,this._rendered=!1,this._show=!1}function p(e){var t=this;this.editor=e,this.$elem=A('<div class="w-e-menu"><i class="w-e-icon-header"><i/></div>'),this.type='droplist',this._active=!1,this.droplist=new d(this,{width:100,$title:A('<p>\u8BBE\u7F6E\u6807\u9898</p>'),type:'list',list:[{$elem:A('<h1>H1</h1>'),value:'<h1>'},{$elem:A('<h2>H2</h2>'),value:'<h2>'},{$elem:A('<h3>H3</h3>'),value:'<h3>'},{$elem:A('<h4>H4</h4>'),value:'<h4>'},{$elem:A('<h5>H5</h5>'),value:'<h5>'},{$elem:A('<p>\u6B63\u6587</p>'),value:'<p>'}],onClick:function(e){t._command(e)}})}function g(e){var t=this;this.editor=e,this.$elem=A('<div class="w-e-menu"><i class="w-e-icon-text-heigh"><i/></div>'),this.type='droplist',this._active=!1,this.droplist=new d(this,{width:160,$title:A('<p>\u5B57\u53F7</p>'),type:'list',list:[{$elem:A('<span style="font-size: x-small;">x-small</span>'),value:'1'},{$elem:A('<span style="font-size: small;">small</span>'),value:'2'},{$elem:A('<span>normal</span>'),value:'3'},{$elem:A('<span style="font-size: large;">large</span>'),value:'4'},{$elem:A('<span style="font-size: x-large;">x-large</span>'),value:'5'},{$elem:A('<span style="font-size: xx-large;">xx-large</span>'),value:'6'}],onClick:function(e){t._command(e)}})}function m(e){var t=this;this.editor=e,this.$elem=A('<div class="w-e-menu"><i class="w-e-icon-font"><i/></div>'),this.type='droplist',this._active=!1;var n=e.config,o=n.fontNames||[];this.droplist=new d(this,{width:100,$title:A('<p>\u5B57\u4F53</p>'),type:'list',list:o.map(function(e){return{$elem:A('<span style="font-family: '+e+';">'+e+'</span>'),value:e}}),onClick:function(e){t._command(e)}})}function u(e,t){this.menu=e,this.opt=t}function w(e){this.editor=e,this.$elem=A('<div class="w-e-menu"><i class="w-e-icon-link"><i/></div>'),this.type='panel',this._active=!1}function h(e){this.editor=e,this.$elem=A('<div class="w-e-menu">\n            <i class="w-e-icon-italic"><i/>\n        </div>'),this.type='click',this._active=!1}function f(e){this.editor=e,this.$elem=A('<div class="w-e-menu">\n            <i class="w-e-icon-redo"><i/>\n        </div>'),this.type='click',this._active=!1}function E(e){this.editor=e,this.$elem=A('<div class="w-e-menu">\n            <i class="w-e-icon-strikethrough"><i/>\n        </div>'),this.type='click',this._active=!1}function b(e){this.editor=e,this.$elem=A('<div class="w-e-menu">\n            <i class="w-e-icon-underline"><i/>\n        </div>'),this.type='click',this._active=!1}function C(e){this.editor=e,this.$elem=A('<div class="w-e-menu">\n            <i class="w-e-icon-undo"><i/>\n        </div>'),this.type='click',this._active=!1}function B(e){var t=this;this.editor=e,this.$elem=A('<div class="w-e-menu"><i class="w-e-icon-list2"><i/></div>'),this.type='droplist',this._active=!1,this.droplist=new d(this,{width:120,$title:A('<p>\u8BBE\u7F6E\u5217\u8868</p>'),type:'list',list:[{$elem:A('<span><i class="w-e-icon-list-numbered"></i> \u6709\u5E8F\u5217\u8868</span>'),value:'insertOrderedList'},{$elem:A('<span><i class="w-e-icon-list2"></i> \u65E0\u5E8F\u5217\u8868</span>'),value:'insertUnorderedList'}],onClick:function(e){t._command(e)}})}function x(e){var t=this;this.editor=e,this.$elem=A('<div class="w-e-menu"><i class="w-e-icon-paragraph-left"><i/></div>'),this.type='droplist',this._active=!1,this.droplist=new d(this,{width:100,$title:A('<p>\u5BF9\u9F50\u65B9\u5F0F</p>'),type:'list',list:[{$elem:A('<span><i class="w-e-icon-paragraph-left"></i> \u9760\u5DE6</span>'),value:'justifyLeft'},{$elem:A('<span><i class="w-e-icon-paragraph-center"></i> \u5C45\u4E2D</span>'),value:'justifyCenter'},{$elem:A('<span><i class="w-e-icon-paragraph-right"></i> \u9760\u53F3</span>'),value:'justifyRight'}],onClick:function(e){t._command(e)}})}function y(e){var t=this;this.editor=e,this.$elem=A('<div class="w-e-menu"><i class="w-e-icon-pencil2"><i/></div>'),this.type='droplist';var n=e.config,o=n.colors||[];this._active=!1,this.droplist=new d(this,{width:120,$title:A('<p>\u6587\u5B57\u989C\u8272</p>'),type:'inline-block',list:o.map(function(e){return{$elem:A('<i style="color:'+e+';" class="w-e-icon-pencil2"></i>'),value:e}}),onClick:function(e){t._command(e)}})}function v(e){var t=this;this.editor=e,this.$elem=A('<div class="w-e-menu"><i class="w-e-icon-paint-brush"><i/></div>'),this.type='droplist';var n=e.config,o=n.colors||[];this._active=!1,this.droplist=new d(this,{width:120,$title:A('<p>\u80CC\u666F\u8272</p>'),type:'inline-block',list:o.map(function(e){return{$elem:A('<i style="color:'+e+';" class="w-e-icon-paint-brush"></i>'),value:e}}),onClick:function(e){t._command(e)}})}function I(e){this.editor=e,this.$elem=A('<div class="w-e-menu">\n            <i class="w-e-icon-quotes-left"><i/>\n        </div>'),this.type='click',this._active=!1}function Q(e){this.editor=e,this.$elem=A('<div class="w-e-menu">\n            <i class="w-e-icon-terminal"><i/>\n        </div>'),this.type='panel',this._active=!1}function M(e){this.editor=e,this.$elem=A('<div class="w-e-menu">\n            <i class="w-e-icon-happy"><i/>\n        </div>'),this.type='panel',this._active=!1}function _(e){this.editor=e,this.$elem=A('<div class="w-e-menu"><i class="w-e-icon-table2"><i/></div>'),this.type='panel',this._active=!1}function k(e){this.editor=e,this.$elem=A('<div class="w-e-menu"><i class="w-e-icon-play"><i/></div>'),this.type='panel',this._active=!1}function S(e){this.editor=e;var t=l('w-e-img');this.$elem=A('<div class="w-e-menu" id="'+t+'"><i class="w-e-icon-image"><i/></div>'),e.imgMenuId=t,this.type='panel',this._active=!1}function D(e){this.editor=e,this.menus={}}function F(t){var e,n=t.clipboardData||t.originalEvent&&t.originalEvent.clipboardData;return e=null==n?window.clipboardData&&window.clipboardData.getData('text'):n.getData('text/plain'),c(e)}function N(t,e,n){var o,A,i=t.clipboardData||t.originalEvent&&t.originalEvent.clipboardData;if(null==i?o=window.clipboardData&&window.clipboardData.getData('text'):(o=i.getData('text/plain'),A=i.getData('text/html')),!A&&o&&(A='<p>'+c(o)+'</p>'),!!A){var a=A.split('</html>');return 2===a.length&&(A=a[0]),A=A.replace(/<(meta|script|link).+?>/igm,''),A=A.replace(/<!--.*?-->/mg,''),A=A.replace(/\s?data-.+?=('|").+?('|")/igm,''),n&&(A=A.replace(/<img.+?>/igm,'')),A=e?A.replace(/\s?(class|style)=('|").+?('|")/igm,''):A.replace(/\s?class=('|").+?('|")/igm,''),A}}function R(t){var e=[],n=F(t);if(n)return e;var o=t.clipboardData||t.originalEvent&&t.originalEvent.clipboardData||{},A=o.items;return A?(i(A,function(t,n){var o=n.type;/image/i.test(o)&&e.push(n.getAsFile())}),e):e}function T(e){var t=[],n=e.childNodes()||[];return n.forEach(function(e){var n,o=e.nodeType;if(3===o&&(n=e.textContent),1===o){n={},n.tag=e.nodeName.toLowerCase();for(var a,l=[],c=e.attributes||{},r=c.length||0,s=0;s<r;s++)a=c[s],l.push({name:a.name,value:a.value});n.attrs=l,n.children=T(A(e))}t.push(n)}),t}function U(e){this.editor=e}function Y(e){this.editor=e}function P(e){this.editor=e,this._currentRange=null}function H(e){this.editor=e,this._time=0,this._isShow=!1,this._isRender=!1,this._timeoutId=0,this.$textContainer=e.$textContainerElem,this.$bar=A('<div class="w-e-progress"></div>')}function L(e){this.editor=e}function G(e,t){if(null==e)throw new Error('\u9519\u8BEF\uFF1A\u521D\u59CB\u5316\u7F16\u8F91\u5668\u65F6\u5019\u672A\u4F20\u5165\u4EFB\u4F55\u53C2\u6570\uFF0C\u8BF7\u67E5\u9605\u6587\u6863');this.id='wangEditor-'+X++,this.toolbarSelector=e,this.textSelector=t,this.customConfig={}}var j=[];o.prototype={constructor:o,forEach:function(e){var t;for(t=0;t<this.length;t++){var n=this[t],o=e.call(n,n,t);if(!1===o)break}return this},clone:function(e){var t=[];return this.forEach(function(n){t.push(n.cloneNode(!!e))}),A(t)},get:function(e){var t=this.length;return e>=t&&(e%=t),A(this[e])},first:function(){return this.get(0)},last:function(){var e=this.length;return this.get(e-1)},on:function(e,t,n){n||(n=t,t=null);var o=[];return o=e.split(/\s+/),this.forEach(function(e){o.forEach(function(o){if(o)return j.push({elem:e,type:o,fn:n}),t?void e.addEventListener(o,function(o){var e=o.target;e.matches(t)&&n.call(e,o)}):void e.addEventListener(o,n)})})},off:function(e,t){return this.forEach(function(n){n.removeEventListener(e,t)})},attr:function(e,t){return null==t?this[0].getAttribute(e):this.forEach(function(n){n.setAttribute(e,t)})},addClass:function(e){return e?this.forEach(function(t){var n;t.className?(n=t.className.split(/\s/),n=n.filter(function(e){return!!e.trim()}),0>n.indexOf(e)&&n.push(e),t.className=n.join(' ')):t.className=e}):this},removeClass:function(e){return e?this.forEach(function(t){var n;t.className&&(n=t.className.split(/\s/),n=n.filter(function(t){return t=t.trim(),t&&t!==e}),t.className=n.join(' '))}):this},css:function(e,t){var n=e+':'+t+';';return this.forEach(function(t){var o,A=(t.getAttribute('style')||'').trim(),i=[];A?(o=A.split(';'),o.forEach(function(e){var t=e.split(':').map(function(e){return e.trim()});2===t.length&&i.push(t[0]+':'+t[1])}),i=i.map(function(t){return 0===t.indexOf(e)?n:t}),0>i.indexOf(n)&&i.push(n),t.setAttribute('style',i.join('; '))):t.setAttribute('style',n)})},show:function(){return this.css('display','block')},hide:function(){return this.css('display','none')},children:function(){var e=this[0];return e?A(e.children):null},childNodes:function(){var e=this[0];return e?A(e.childNodes):null},append:function(e){return this.forEach(function(t){e.forEach(function(e){t.appendChild(e)})})},remove:function(){return this.forEach(function(e){if(e.remove)e.remove();else{var t=e.parentElement;t&&t.removeChild(e)}})},isContain:function(e){var t=this[0],n=e[0];return t.contains(n)},getSizeData:function(){var e=this[0];return e.getBoundingClientRect()},getNodeName:function(){var e=this[0];return e.nodeName},find:function(e){var t=this[0];return A(t.querySelectorAll(e))},text:function(e){if(!e){var t=this[0];return t.innerHTML.replace(/<.*?>/g,function(){return''})}return this.forEach(function(t){t.innerHTML=e})},html:function(e){var t=this[0];return null==e?t.innerHTML:(t.innerHTML=e,this)},val:function(){var e=this[0];return e.value.trim()},focus:function(){return this.forEach(function(e){e.focus()})},parent:function(){var e=this[0];return A(e.parentElement)},parentUntil:function(e,t){var n=document.querySelectorAll(e),o=n.length;if(!o)return null;var a=t||this[0];if('BODY'===a.nodeName)return null;var l,i=a.parentElement;for(l=0;l<o;l++)if(i===n[l])return A(i);return this.parentUntil(e,i)},equal:function(e){return 1===e.nodeType?this[0]===e:this[0]===e[0]},insertBefore:function(e){var t=A(e),n=t[0];return n?this.forEach(function(e){var t=n.parentNode;t.insertBefore(e,n)}):this},insertAfter:function(e){var t=A(e),n=t[0];return n?this.forEach(function(e){var t=n.parentNode;t.lastChild===n?t.appendChild(e):t.insertBefore(e,n.nextSibling)}):this}},A.offAll=function(){j.forEach(function(e){var t=e.elem,n=e.type,o=e.fn;t.removeEventListener(n,o)})};var z={menus:['head','bold','fontSize','fontName','italic','underline','strikeThrough','foreColor','backColor','link','list','justify','quote','emoticon','image','table','video','code','undo','redo'],fontNames:['\u5B8B\u4F53','\u5FAE\u8F6F\u96C5\u9ED1','Arial','Tahoma','Verdana'],colors:['#000000','#eeece0','#1c487f','#4d80bf','#c24f4a','#8baa4a','#7b5ba1','#46acc8','#f9963b','#ffffff'],emotions:[{title:'\u9ED8\u8BA4',type:'image',content:[{alt:'[\u574F\u7B11]',src:'http://img.t.sinajs.cn/t4/appstyle/expression/ext/normal/50/pcmoren_huaixiao_org.png'},{alt:'[\u8214\u5C4F]',src:'http://img.t.sinajs.cn/t4/appstyle/expression/ext/normal/40/pcmoren_tian_org.png'},{alt:'[\u6C61]',src:'http://img.t.sinajs.cn/t4/appstyle/expression/ext/normal/3c/pcmoren_wu_org.png'}]},{title:'\u65B0\u6D6A',type:'image',content:[{src:'http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/7a/shenshou_thumb.gif',alt:'[\u8349\u6CE5\u9A6C]'},{src:'http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/60/horse2_thumb.gif',alt:'[\u795E\u9A6C]'},{src:'http://img.t.sinajs.cn/t35/style/images/common/face/ext/normal/bc/fuyun_thumb.gif',alt:'[\u6D6E\u4E91]'}]},{title:'emoji',type:'emoji',content:'\uD83D\uDE00 \uD83D\uDE03 \uD83D\uDE04 \uD83D\uDE01 \uD83D\uDE06 \uD83D\uDE05 \uD83D\uDE02 \uD83D\uDE0A \uD83D\uDE07 \uD83D\uDE42 \uD83D\uDE43 \uD83D\uDE09 \uD83D\uDE13 \uD83D\uDE2A \uD83D\uDE34 \uD83D\uDE44 \uD83E\uDD14 \uD83D\uDE2C \uD83E\uDD10'.split(/\s/)}],zIndex:1e4,debug:!1,linkCheck:function(){return!0},linkImgCheck:function(){return!0},pasteFilterStyle:!0,pasteIgnoreImg:!1,pasteTextHandle:function(e){return e},showLinkImg:!0,linkImgCallback:function(){},uploadImgMaxSize:5242880,uploadImgShowBase64:!1,uploadFileName:'',uploadImgParams:{},uploadImgHeaders:{},withCredentials:!1,uploadImgTimeout:1e4,uploadImgHooks:{before:function(){},success:function(){},fail:function(){},error:function(){},timeout:function(){}},qiniu:!1},J={_ua:navigator.userAgent,isWebkit:function(){var e=/webkit/i;return e.test(this._ua)},isIE:function(){return'ActiveXObject'in window}};s.prototype={constructor:s,onClick:function(){var e=this.editor,t=e.selection.isSelectionEmpty();t&&e.selection.createEmptyRange(),e.cmd.do('bold'),t&&(e.selection.collapseRange(),e.selection.restoreSelection())},tryChangeActive:function(){var e=this.editor,t=this.$elem;e.cmd.queryCommandState('bold')?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active'))}};var $=function(e,t){var n=e.config.langArgs||[],o=t;return n.forEach(function(e){var t=e.reg,n=e.val;t.test(o)&&(o=o.replace(t,function(){return n}))}),o},O=function(){};d.prototype={constructor:d,show:function(){this.hideTimeoutId&&clearTimeout(this.hideTimeoutId);var e=this.menu,t=e.$elem,n=this.$container;if(!this._show){if(this._rendered)n.show();else{var o=t.getSizeData().height||0,A=this.opt.width||100;n.css('margin-top',o+'px').css('width',A+'px'),t.append(n),this._rendered=!0}this._show=!0}},hide:function(){this.showTimeoutId&&clearTimeout(this.showTimeoutId);var e=this.$container;this._show&&(e.hide(),this._show=!1)}},p.prototype={constructor:p,_command:function(e){var t=this.editor,n=t.selection.getSelectionContainerElem();t.$textElem.equal(n)||t.cmd.do('formatBlock',e)},tryChangeActive:function(){var e=this.editor,t=this.$elem,n=/^h/i,o=e.cmd.queryCommandValue('formatBlock');n.test(o)?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active'))}},g.prototype={constructor:g,_command:function(e){var t=this.editor;t.cmd.do('fontSize',e)}},m.prototype={constructor:m,_command:function(e){var t=this.editor;t.cmd.do('fontName',e)}};var V=function(){},K=[];u.prototype={constructor:u,show:function(){var t=this,e=this.menu;if(!(0<=K.indexOf(e))){var n=e.editor,o=A('body'),i=n.$textContainerElem,a=this.opt,l=A('<div class="w-e-panel-container"></div>'),c=a.width||300;l.css('width',c+'px').css('margin-left',(0-c)/2+'px');var r=A('<i class="w-e-icon-close w-e-panel-close"></i>');l.append(r),r.on('click',function(){t.hide()});var s=A('<ul class="w-e-panel-tab-title"></ul>'),d=A('<div class="w-e-panel-tab-content"></div>');l.append(s).append(d);var p=a.height;p&&d.css('height',p+'px').css('overflow-y','auto');var g=a.tabs||[],m=[],u=[];g.forEach(function(e,t){if(e){var o=e.title||'',i=e.tpl||'';o=$(n,o),i=$(n,i);var a=A('<li class="w-e-item">'+o+'</li>');s.append(a);var l=A(i);d.append(l),a._index=t,m.push(a),u.push(l),0===t?(a._active=!0,a.addClass('w-e-active')):l.hide(),a.on('click',function(){a._active||(m.forEach(function(e){e._active=!1,e.removeClass('w-e-active')}),u.forEach(function(e){e.hide()}),a._active=!0,a.addClass('w-e-active'),l.show())})}}),l.on('click',function(t){t.stopPropagation()}),o.on('click',function(){t.hide()}),i.append(l),g.forEach(function(e,n){if(e){var o=e.events||[];o.forEach(function(e){var o=e.selector,A=e.type,i=e.fn||V,a=u[n];a.find(o).on(A,function(n){n.stopPropagation();var e=i(n);e&&t.hide()})})}});var w=l.find('input[type=text],textarea');w.length&&w.get(0).focus(),this.$container=l,this._hideOtherPanels(),K.push(e)}},hide:function(){var e=this.menu,t=this.$container;t&&t.remove(),K=K.filter(function(t){return t!==e})},_hideOtherPanels:function(){K.length&&K.forEach(function(e){var t=e.panel||{};t.hide&&t.hide()})}},w.prototype={constructor:w,onClick:function(){var e,t=this.editor;if(this._active){if(e=t.selection.getSelectionContainerElem(),!e)return;t.selection.createRangeByElem(e),t.selection.restoreSelection(),this._createPanel(e.text(),e.attr('href'))}else t.selection.isSelectionEmpty()?this._createPanel('',''):this._createPanel(t.selection.getSelectionText(),'')},_createPanel:function(e,t){var n=this,o=l('input-link'),i=l('input-text'),a=l('btn-ok'),c=l('btn-del'),r=this._active?'inline-block':'none',s=new u(this,{width:300,tabs:[{title:'\u94FE\u63A5',tpl:'<div>\n                            <input id="'+i+'" type="text" class="block" value="'+e+'" placeholder="\u94FE\u63A5\u6587\u5B57"/></td>\n                            <input id="'+o+'" type="text" class="block" value="'+t+'" placeholder="http://..."/></td>\n                            <div class="w-e-button-container">\n                                <button id="'+a+'" class="right">\u63D2\u5165</button>\n                                <button id="'+c+'" class="gray right" style="display:'+r+'">\u5220\u9664\u94FE\u63A5</button>\n                            </div>\n                        </div>',events:[{selector:'#'+a,type:'click',fn:function(){var e=A('#'+o),t=A('#'+i),a=e.val(),l=t.val();return n._insertLink(l,a),!0}},{selector:'#'+c,type:'click',fn:function(){return n._delLink(),!0}}]}]});s.show(),this.panel=s},_delLink:function(){if(this._active){var e=this.editor,t=e.selection.getSelectionContainerElem();if(t){var n=e.selection.getSelectionText();e.cmd.do('insertHTML','<span>'+n+'</span>')}}},_insertLink:function(e,t){var n=this.editor,o=n.config,A=o.linkCheck,i=!0;A&&'function'==typeof A&&(i=A(e,t)),!0===i?n.cmd.do('insertHTML','<a href="'+t+'" target="_blank">'+e+'</a>'):alert(i)},tryChangeActive:function(){var e=this.editor,t=this.$elem,n=e.selection.getSelectionContainerElem();n&&('A'===n.getNodeName()?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active')))}},h.prototype={constructor:h,onClick:function(){var e=this.editor,t=e.selection.isSelectionEmpty();t&&e.selection.createEmptyRange(),e.cmd.do('italic'),t&&(e.selection.collapseRange(),e.selection.restoreSelection())},tryChangeActive:function(){var e=this.editor,t=this.$elem;e.cmd.queryCommandState('italic')?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active'))}},f.prototype={constructor:f,onClick:function(){var e=this.editor;e.cmd.do('redo')}},E.prototype={constructor:E,onClick:function(){var e=this.editor,t=e.selection.isSelectionEmpty();t&&e.selection.createEmptyRange(),e.cmd.do('strikeThrough'),t&&(e.selection.collapseRange(),e.selection.restoreSelection())},tryChangeActive:function(){var e=this.editor,t=this.$elem;e.cmd.queryCommandState('strikeThrough')?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active'))}},b.prototype={constructor:b,onClick:function(){var e=this.editor,t=e.selection.isSelectionEmpty();t&&e.selection.createEmptyRange(),e.cmd.do('underline'),t&&(e.selection.collapseRange(),e.selection.restoreSelection())},tryChangeActive:function(){var e=this.editor,t=this.$elem;e.cmd.queryCommandState('underline')?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active'))}},C.prototype={constructor:C,onClick:function(){var e=this.editor;e.cmd.do('undo')}},B.prototype={constructor:B,_command:function(e){var t=this.editor,n=t.$textElem;if(t.selection.restoreSelection(),!t.cmd.queryCommandState(e)){t.cmd.do(e);var o=t.selection.getSelectionContainerElem();if(('LI'===o.getNodeName()&&(o=o.parent()),!1!==/^ol|ul$/i.test(o.getNodeName()))&&!o.equal(n)){var A=o.parent();A.equal(n)||(o.insertAfter(A),A.remove())}}},tryChangeActive:function(){var e=this.editor,t=this.$elem;e.cmd.queryCommandState('insertUnOrderedList')||e.cmd.queryCommandState('insertOrderedList')?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active'))}},x.prototype={constructor:x,_command:function(e){var t=this.editor;t.cmd.do(e)}},y.prototype={constructor:y,_command:function(e){var t=this.editor;t.cmd.do('foreColor',e)}},v.prototype={constructor:v,_command:function(e){var t=this.editor;t.cmd.do('backColor',e)}},I.prototype={constructor:I,onClick:function(){var e=this.editor,t=e.selection.getSelectionContainerElem(),n=t.getNodeName();if(!J.isIE())return void('BLOCKQUOTE'===n?e.cmd.do('formatBlock','<P>'):e.cmd.do('formatBlock','<BLOCKQUOTE>'));var o,i;return'P'===n?(o=t.text(),i=A('<blockquote>'+o+'</blockquote>'),i.insertAfter(t),void t.remove()):void('BLOCKQUOTE'===n&&(o=t.text(),i=A('<p>'+o+'</p>'),i.insertAfter(t),t.remove()))},tryChangeActive:function(){var e=this.editor,t=this.$elem,n=/^BLOCKQUOTE$/i,o=e.cmd.queryCommandValue('formatBlock');n.test(o)?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active'))}},Q.prototype={constructor:Q,onClick:function(){var e,t=this.editor,n=t.selection.getSelectionStartElem(),o=t.selection.getSelectionEndElem(),i=t.selection.isSelectionEmpty(),a=t.selection.getSelectionText();return n.equal(o)?i?void(this._active?this._createPanel(n.html()):this._createPanel()):(e=A('<code>'+a+'</code>'),t.cmd.do('insertElem',e),t.selection.createRangeByElem(e,!1),void t.selection.restoreSelection()):void t.selection.restoreSelection()},_createPanel:function(e){var t=this;e=e||'';var n=e?'edit':'new',o=l('texxt'),i=l('btn'),a=new u(this,{width:500,tabs:[{title:'\u63D2\u5165\u4EE3\u7801',tpl:'<div>\n                        <textarea id="'+o+'" style="height:145px;;">'+e+'</textarea>\n                        <div class="w-e-button-container">\n                            <button id="'+i+'" class="right">\u63D2\u5165</button>\n                        </div>\n                    <div>',events:[{selector:'#'+i,type:'click',fn:function(){var e=A('#'+o),i=e.val()||e.html();return i=c(i),'new'==n?t._insertCode(i):t._updateCode(i),!0}}]}]});a.show(),this.panel=a},_insertCode:function(e){var t=this.editor;t.cmd.do('insertHTML','<pre><code>'+e+'</code></pre><p><br></p>')},_updateCode:function(e){var t=this.editor,n=t.selection.getSelectionContainerElem();n&&(n.html(e),t.selection.restoreSelection())},tryChangeActive:function(){var e=this.editor,t=this.$elem,n=e.selection.getSelectionContainerElem();if(n){var o=n.parent();'CODE'===n.getNodeName()&&'PRE'===o.getNodeName()?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active'))}}},M.prototype={constructor:M,onClick:function(){this._createPanel()},_createPanel:function(){var t=this,e=this.editor,n=e.config,o=n.emotions||[],i=[];o.forEach(function(e){var n=e.type,o=e.content||[],a='';'emoji'===n&&o.forEach(function(e){e&&(a+='<span class="w-e-item">'+e+'</span>')}),'image'===n&&o.forEach(function(e){var t=e.src,n=e.alt;t&&(a+='<span class="w-e-item"><img src="'+t+'" alt="'+n+'" data-w-e="1"/></span>')}),i.push({title:e.title,tpl:'<div class="w-e-emoticon-container">'+a+'</div>',events:[{selector:'span.w-e-item',type:'click',fn:function(n){var e,o=n.target,i=A(o),a=i.getNodeName();return e='IMG'===a?i.parent().html():'<span>'+i.html()+'</span>',t._insert(e),!0}}]})});var a=new u(this,{width:300,height:200,tabs:i});a.show(),this.panel=a},_insert:function(e){var t=this.editor;t.cmd.do('insertHTML',e)}},_.prototype={constructor:_,onClick:function(){this._active?this._createEditPanel():this._createInsertPanel()},_createInsertPanel:function(){var e=this,t=l('btn'),n=l('row'),o=l('col'),i=new u(this,{width:250,tabs:[{title:'\u63D2\u5165\u8868\u683C',tpl:'<div>\n                        <p style="text-align:left; padding:5px 0;">\n                            \u521B\u5EFA\n                            <input id="'+n+'" type="text" value="5" style="width:40px;text-align:center;"/>\n                            \u884C\n                            <input id="'+o+'" type="text" value="5" style="width:40px;text-align:center;"/>\n                            \u5217\u7684\u8868\u683C\n                        </p>\n                        <div class="w-e-button-container">\n                            <button id="'+t+'" class="right">\u63D2\u5165</button>\n                        </div>\n                    </div>',events:[{selector:'#'+t,type:'click',fn:function(){var t=parseInt(A('#'+n).val()),i=parseInt(A('#'+o).val());return t&&i&&0<t&&0<i&&e._insert(t,i),!0}}]}]});i.show(),this.panel=i},_insert:function(e,t){var n,o,A='<table border="0" width="100%" cellpadding="0" cellspacing="0">';for(n=0;n<e;n++){if(A+='<tr>',0===n)for(o=0;o<t;o++)A+='<th>&nbsp;</th>';else for(o=0;o<t;o++)A+='<td>&nbsp;</td>';A+='</tr>'}A+='</table><p><br></p>';var i=this.editor;i.cmd.do('insertHTML',A),i.cmd.do('enableObjectResizing',!1),i.cmd.do('enableInlineTableEditing',!1)},_createEditPanel:function(){var e=this,t=l('add-row'),n=l('add-col'),o=l('del-row'),A=l('del-col'),i=l('del-table'),a=new u(this,{width:320,tabs:[{title:'\u7F16\u8F91\u8868\u683C',tpl:'<div>\n                        <div class="w-e-button-container" style="border-bottom:1px solid #f1f1f1;padding-bottom:5px;margin-bottom:5px;">\n                            <button id="'+t+'" class="left">\u589E\u52A0\u884C</button>\n                            <button id="'+o+'" class="red left">\u5220\u9664\u884C</button>\n                            <button id="'+n+'" class="left">\u589E\u52A0\u5217</button>\n                            <button id="'+A+'" class="red left">\u5220\u9664\u5217</button>\n                        </div>\n                        <div class="w-e-button-container">\n                            <button id="'+i+'" class="gray left">\u5220\u9664\u8868\u683C</button>\n                        </dv>\n                    </div>',events:[{selector:'#'+t,type:'click',fn:function(){return e._addRow(),!0}},{selector:'#'+n,type:'click',fn:function(){return e._addCol(),!0}},{selector:'#'+o,type:'click',fn:function(){return e._delRow(),!0}},{selector:'#'+A,type:'click',fn:function(){return e._delCol(),!0}},{selector:'#'+i,type:'click',fn:function(){return e._delTable(),!0}}]}]});a.show()},_getLocationData:function(){var e={},t=this.editor,n=t.selection.getSelectionContainerElem();if(n){var o=n.getNodeName();if('TD'===o||'TH'===o){var A=n.parent(),i=A.children(),a=i.length;i.forEach(function(t,o){if(t===n[0])return e.td={index:o,elem:t,length:a},!1});var l=A.parent(),c=l.children(),r=c.length;return c.forEach(function(t,n){if(t===A[0])return e.tr={index:n,elem:t,length:r},!1}),e}}},_addRow:function(){var e=this._getLocationData();if(e){var t,n=e.tr,o=A(n.elem),i=e.td,a=i.length,l=document.createElement('tr'),c='';for(t=0;t<a;t++)c+='<td>&nbsp;</td>';l.innerHTML=c,A(l).insertAfter(o)}},_addCol:function(){var e=this._getLocationData();if(e){var t=e.tr,n=e.td,o=n.index,i=A(t.elem),a=i.parent(),l=a.children();l.forEach(function(e){var t=A(e),n=t.children(),i=n.get(o),a=i.getNodeName().toLowerCase(),l=document.createElement(a);A(l).insertAfter(i)})}},_delRow:function(){var e=this._getLocationData();if(e){var t=e.tr,n=A(t.elem);n.remove()}},_delCol:function(){var e=this._getLocationData();if(e){var t=e.tr,n=e.td,o=n.index,i=A(t.elem),a=i.parent(),l=a.children();l.forEach(function(e){var t=A(e),n=t.children(),i=n.get(o);i.remove()})}},_delTable:function(){var e=this.editor,t=e.selection.getSelectionContainerElem();if(t){var n=t.parentUntil('table');n&&n.remove()}},tryChangeActive:function(){var e=this.editor,t=this.$elem,n=e.selection.getSelectionContainerElem();if(n){var o=n.getNodeName();'TD'===o||'TH'===o?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active'))}}},k.prototype={constructor:k,onClick:function(){this._createPanel()},_createPanel:function(){var e=this,t=l('text-val'),n=l('btn'),o=new u(this,{width:350,tabs:[{title:'\u63D2\u5165\u89C6\u9891',tpl:'<div>\n                        <input id="'+t+'" type="text" class="block" placeholder="\u683C\u5F0F\u5982\uFF1A<iframe src=... ></iframe>"/>\n                        <div class="w-e-button-container">\n                            <button id="'+n+'" class="right">\u63D2\u5165</button>\n                        </div>\n                    </div>',events:[{selector:'#'+n,type:'click',fn:function(){var n=A('#'+t),o=n.val().trim();return o&&e._insert(o),!0}}]}]});o.show(),this.panel=o},_insert:function(e){var t=this.editor;t.cmd.do('insertHTML',e+'<p><br></p>')}},S.prototype={constructor:S,onClick:function(){var e=this.editor,t=e.config;t.qiniu||(this._active?this._createEditPanel():this._createInsertPanel())},_createEditPanel:function(){var e=this.editor,t=l('width-30'),n=l('width-50'),o=l('width-100'),A=l('del-btn'),i=new u(this,{width:300,tabs:[{title:'\u7F16\u8F91\u56FE\u7247',tpl:'<div>\n                    <div class="w-e-button-container" style="border-bottom:1px solid #f1f1f1;padding-bottom:5px;margin-bottom:5px;">\n                        <span style="float:left;font-size:14px;margin:4px 5px 0 5px;color:#333;">\u6700\u5927\u5BBD\u5EA6\uFF1A</span>\n                        <button id="'+t+'" class="left">30%</button>\n                        <button id="'+n+'" class="left">50%</button>\n                        <button id="'+o+'" class="left">100%</button>\n                    </div>\n                    <div class="w-e-button-container">\n                        <button id="'+A+'" class="gray left">\u5220\u9664\u56FE\u7247</button>\n                    </dv>\n                </div>',events:[{selector:'#'+t,type:'click',fn:function(){var t=e._selectedImg;return t&&t.css('max-width','30%'),!0}},{selector:'#'+n,type:'click',fn:function(){var t=e._selectedImg;return t&&t.css('max-width','50%'),!0}},{selector:'#'+o,type:'click',fn:function(){var t=e._selectedImg;return t&&t.css('max-width','100%'),!0}},{selector:'#'+A,type:'click',fn:function(){var t=e._selectedImg;return t&&t.remove(),!0}}]}]});i.show(),this.panel=i},_createInsertPanel:function(){var e=this.editor,t=e.uploadImg,n=e.config,o=l('up-trigger'),i=l('up-file'),a=l('link-url'),c=l('link-btn'),r=[{title:'\u4E0A\u4F20\u56FE\u7247',tpl:'<div class="w-e-up-img-container">\n                    <div id="'+o+'" class="w-e-up-btn">\n                        <i class="w-e-icon-upload2"></i>\n                    </div>\n                    <div style="display:none;">\n                        <input id="'+i+'" type="file" multiple="multiple" accept="image/jpg,image/jpeg,image/png,image/gif,image/bmp"/>\n                    </div>\n                </div>',events:[{selector:'#'+o,type:'click',fn:function(){var e=A('#'+i),t=e[0];return!t||void t.click()}},{selector:'#'+i,type:'change',fn:function(){var e=A('#'+i),n=e[0];if(!n)return!0;var o=n.files;return o.length&&t.uploadImg(o),!0}}]},{title:'\u7F51\u7EDC\u56FE\u7247',tpl:'<div>\n                    <input id="'+a+'" type="text" class="block" placeholder="\u56FE\u7247\u94FE\u63A5"/></td>\n                    <div class="w-e-button-container">\n                        <button id="'+c+'" class="right">\u63D2\u5165</button>\n                    </div>\n                </div>',events:[{selector:'#'+c,type:'click',fn:function(){var e=A('#'+a),n=e.val().trim();return n&&t.insertLinkImg(n),!0}}]}],s=[];(n.uploadImgShowBase64||n.uploadImgServer||n.customUploadImg)&&window.FileReader&&s.push(r[0]),n.showLinkImg&&s.push(r[1]);var d=new u(this,{width:300,tabs:s});d.show(),this.panel=d},tryChangeActive:function(){var e=this.editor,t=this.$elem;e._selectedImg?(this._active=!0,t.addClass('w-e-active')):(this._active=!1,t.removeClass('w-e-active'))}};var q={};q.bold=s,q.head=p,q.fontSize=g,q.fontName=m,q.link=w,q.italic=h,q.redo=f,q.strikeThrough=E,q.underline=b,q.undo=C,q.list=B,q.justify=x,q.foreColor=y,q.backColor=v,q.quote=I,q.code=Q,q.emoticon=M,q.table=_,q.video=k,q.image=S,D.prototype={constructor:D,init:function(){var e=this,t=this.editor,n=t.config||{},o=n.menus||[];o.forEach(function(n){var o=q[n];o&&'function'==typeof o&&(e.menus[n]=new o(t))}),this._addToToolbar(),this._bindEvent()},_addToToolbar:function(){var e=this.editor,t=e.$toolbarElem,n=this.menus,o=e.config,A=o.zIndex+1;i(n,function(e,n){var o=n.$elem;o&&(o.css('z-index',A),t.append(o))})},_bindEvent:function(){var e=this.menus,t=this.editor;i(e,function(e,n){var o=n.type;if(o){var A=n.$elem,i=n.droplist,a=n.panel;'click'===o&&n.onClick&&A.on('click',function(o){null==t.selection.getRange()||n.onClick(o)}),'droplist'===o&&i&&A.on('mouseenter',function(){null==t.selection.getRange()||(i.showTimeoutId=setTimeout(function(){i.show()},200))}).on('mouseleave',function(){i.hideTimeoutId=setTimeout(function(){i.hide()},0)}),'panel'===o&&n.onClick&&A.on('click',function(o){o.stopPropagation(),null==t.selection.getRange()||n.onClick(o)})}})},changeActive:function(){var e=this.menus;i(e,function(e,t){t.tryChangeActive&&setTimeout(function(){t.tryChangeActive()},100)})}},U.prototype={constructor:U,init:function(){this._bindEvent()},clear:function(){this.html('<p><br></p>')},html:function(e){var t=this.editor,n=t.$textElem,o=void 0;return null==e?(o=n.html(),o=o.replace(/\u200b/gm,''),o):void(n.html(e),t.initSelection())},getJSON:function(){var e=this.editor,t=e.$textElem;return T(t)},text:function(e){var t=this.editor,n=t.$textElem,o=void 0;return null==e?(o=n.text(),o=o.replace(/\u200b/gm,''),o):void(n.text('<p>'+e+'</p>'),t.initSelection())},append:function(e){var t=this.editor,n=t.$textElem;n.append(A(e)),t.initSelection()},_bindEvent:function(){this._saveRangeRealTime(),this._enterKeyHandle(),this._clearHandle(),this._pasteHandle(),this._tabHandle(),this._imgHandle(),this._dragHandle()},_saveRangeRealTime:function(){function e(){t.selection.saveRange(),t.menus.changeActive()}var t=this.editor,n=t.$textElem;n.on('keyup',e),n.on('mousedown',function(){n.on('mouseleave',e)}),n.on('mouseup',function(){e(),n.off('mouseleave',e)})},_enterKeyHandle:function(){function e(e){var t=A('<p><br></p>');t.insertBefore(e),o.selection.createRangeByElem(t,!0),o.selection.restoreSelection(),e.remove()}function t(){var t=o.selection.getSelectionContainerElem(),n=t.parent();if('<code><br></code>'===n.html())return void e(t);if(n.equal(i)){var A=t.getNodeName();'P'===A||t.text()||e(t)}}function n(t){var e=o.selection.getSelectionContainerElem();if(e){var n=e.parent(),i=e.getNodeName(),a=n.getNodeName();if('CODE'===i&&'PRE'===a&&o.cmd.queryCommandSupported('insertHTML')){if(!0===o._willBreakCode){var l=A('<p><br></p>');return l.insertAfter(n),o.selection.createRangeByElem(l,!0),o.selection.restoreSelection(),o._willBreakCode=!1,void t.preventDefault()}var c=o.selection.getRange().startOffset;o.cmd.do('insertHTML','\n'),o.selection.saveRange(),o.selection.getRange().startOffset===c&&o.cmd.do('insertHTML','\n');var r=e.html().length;o.selection.getRange().startOffset+1===r&&(o._willBreakCode=!0),t.preventDefault()}}}var o=this.editor,i=o.$textElem;i.on('keyup',function(n){13!==n.keyCode||t(n)}),i.on('keydown',function(t){return 13===t.keyCode?void n(t):void(o._willBreakCode=!1)})},_clearHandle:function(){var e=this.editor,t=e.$textElem;t.on('keydown',function(n){if(8===n.keyCode){var e=t.html().toLowerCase().trim();if('<p><br></p>'===e)return void n.preventDefault()}}),t.on('keyup',function(n){if(8===n.keyCode){var o,i=t.html().toLowerCase().trim();i&&'<br>'!==i||(o=A('<p><br/></p>'),t.html(''),t.append(o),e.selection.createRangeByElem(o,!1,!0),e.selection.restoreSelection())}})},_pasteHandle:function(){function t(){var e=Date.now(),t=!1;return 500<=e-c&&(t=!0),c=e,t}function e(){c=0}var n=this.editor,o=n.config,A=o.pasteFilterStyle,i=o.pasteTextHandle,a=o.pasteIgnoreImg,l=n.$textElem,c=0;l.on('paste',function(o){if(!J.isIE()&&(o.preventDefault(),!!t())){var l=N(o,A,a),c=F(o);c=c.replace(/\n/gm,'<br>');var s=n.selection.getSelectionContainerElem();if(s){var d=s.getNodeName();if('CODE'===d||'PRE'===d)return i&&r(i)&&(c=''+(i(c)||'')),void n.cmd.do('insertHTML','<p>'+c+'</p>');if(!l)return void e();try{i&&r(i)&&(l=''+(i(l)||'')),n.cmd.do('insertHTML',l)}catch(e){i&&r(i)&&(c=''+(i(c)||'')),n.cmd.do('insertHTML','<p>'+c+'</p>')}}}}),l.on('paste',function(o){if(!J.isIE()&&(o.preventDefault(),!!t())){var e=R(o);if(e&&e.length){var A=n.selection.getSelectionContainerElem();if(A){var i=A.getNodeName();if('CODE'!==i&&'PRE'!==i){var a=n.uploadImg;a.uploadImg(e)}}}}})},_tabHandle:function(){var t=this.editor,e=t.$textElem;e.on('keydown',function(n){if(9===n.keyCode&&t.cmd.queryCommandSupported('insertHTML')){var e=t.selection.getSelectionContainerElem();if(e){var o=e.parent(),A=e.getNodeName(),i=o.getNodeName();'CODE'===A&&'PRE'===i?t.cmd.do('insertHTML','    '):t.cmd.do('insertHTML','&nbsp;&nbsp;&nbsp;&nbsp;'),n.preventDefault()}}})},_imgHandle:function(){var t=this.editor,e=t.$textElem;e.on('click','img',function(){var e=this,n=A(e);'1'===n.attr('data-w-e')||(t._selectedImg=n,t.selection.createRangeByElem(n),t.selection.restoreSelection())}),e.on('click  keyup',function(n){n.target.matches('img')||(t._selectedImg=null)})},_dragHandle:function(){var e=this.editor,t=A(document);t.on('dragleave drop dragenter dragover',function(t){t.preventDefault()});var n=e.$textElem;n.on('drop',function(t){t.preventDefault();var n=t.dataTransfer&&t.dataTransfer.files;if(n&&n.length){var o=e.uploadImg;o.uploadImg(n)}})}},Y.prototype={constructor:Y,do:function(e,t){var n=this.editor;if(n._useStyleWithCSS||(document.execCommand('styleWithCSS',null,!0),n._useStyleWithCSS=!0),!!n.selection.getRange()){n.selection.restoreSelection();var o='_'+e;this[o]?this[o](t):this._execCommand(e,t),n.menus.changeActive(),n.selection.saveRange(),n.selection.restoreSelection(),n.change&&n.change()}},_insertHTML:function(e){var t=this.editor,n=t.selection.getRange();this.queryCommandSupported('insertHTML')?this._execCommand('insertHTML',e):n.insertNode?(n.deleteContents(),n.insertNode(A(e)[0])):n.pasteHTML&&n.pasteHTML(e)},_insertElem:function(e){var t=this.editor,n=t.selection.getRange();n.insertNode&&(n.deleteContents(),n.insertNode(e[0]))},_execCommand:function(e,t){document.execCommand(e,!1,t)},queryCommandValue:function(e){return document.queryCommandValue(e)},queryCommandState:function(e){return document.queryCommandState(e)},queryCommandSupported:function(e){return document.queryCommandSupported(e)}},P.prototype={constructor:P,getRange:function(){return this._currentRange},saveRange:function(e){if(e)return void(this._currentRange=e);var t=window.getSelection();if(0!==t.rangeCount){var n=t.getRangeAt(0),o=this.getSelectionContainerElem(n);if(o&&!('false'===o.attr('contenteditable')||o.parentUntil('[contenteditable=false]'))){var A=this.editor,i=A.$textElem;i.isContain(o)&&(this._currentRange=n)}}},collapseRange:function(e){null==e&&(e=!1);var t=this._currentRange;t&&t.collapse(e)},getSelectionText:function(){var e=this._currentRange;return e?this._currentRange.toString():''},getSelectionContainerElem:function(e){e=e||this._currentRange;var t;if(e)return t=e.commonAncestorContainer,A(1===t.nodeType?t:t.parentNode)},getSelectionStartElem:function(e){e=e||this._currentRange;var t;if(e)return t=e.startContainer,A(1===t.nodeType?t:t.parentNode)},getSelectionEndElem:function(e){e=e||this._currentRange;var t;if(e)return t=e.endContainer,A(1===t.nodeType?t:t.parentNode)},isSelectionEmpty:function(){var e=this._currentRange;return e&&e.startContainer&&e.startContainer===e.endContainer&&e.startOffset===e.endOffset},restoreSelection:function(){var e=window.getSelection();e.removeAllRanges(),e.addRange(this._currentRange)},createEmptyRange:function(){var e,t=this.editor,n=this.getRange();if(n&&this.isSelectionEmpty())try{J.isWebkit()?(t.cmd.do('insertHTML','&#8203;'),n.setEnd(n.endContainer,n.endOffset+1),this.saveRange(n)):(e=A('<strong>&#8203;</strong>'),t.cmd.do('insertElem',e),this.createRangeByElem(e,!0))}catch(e){}},createRangeByElem:function(e,t,n){if(e.length){var o=e[0],A=document.createRange();n?A.selectNodeContents(o):A.selectNode(o),'boolean'==typeof t&&A.collapse(t),this.saveRange(A)}}},H.prototype={constructor:H,show:function(e){var t=this;if(!this._isShow){this._isShow=!0;var n=this.$bar;if(!this._isRender){var o=this.$textContainer;o.append(n)}else this._isRender=!0;100<Date.now()-this._time&&1>=e&&(n.css('width',100*e+'%'),this._time=Date.now());var A=this._timeoutId;A&&clearTimeout(A),A=setTimeout(function(){t._hide()},500)}},_hide:function(){var e=this.$bar;e.remove(),this._time=0,this._isShow=!1,this._isRender=!1}};var W='function'==typeof Symbol&&'symbol'==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&'function'==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?'symbol':typeof e};L.prototype={constructor:L,_alert:function(e,t){var n=this.editor,o=n.config.debug,A=n.config.customAlert;if(o)throw new Error('wangEditor: '+(t||e));else A&&'function'==typeof A?A(e):alert(e)},insertLinkImg:function(e){var t=this;if(e){var n,o=this.editor,A=o.config,i=A.linkImgCheck;if(i&&'function'==typeof i&&(n=i(e),'string'==typeof n))return void alert(n);o.cmd.do('insertHTML','<img src="'+e+'" style="max-width:100%;"/>');var a=document.createElement('img');a.onload=function(){var t=A.linkImgCallback;t&&'function'==typeof t&&t(e),a=null},a.onerror=function(){return a=null,void t._alert('\u63D2\u5165\u56FE\u7247\u9519\u8BEF','wangEditor: \u63D2\u5165\u56FE\u7247\u51FA\u9519\uFF0C\u56FE\u7247\u94FE\u63A5\u662F "'+e+'"\uFF0C\u4E0B\u8F7D\u8BE5\u94FE\u63A5\u5931\u8D25')},a.onabort=function(){a=null},a.src=e}},uploadImg:function(e){var t=this;if(e&&e.length){var n=this.editor,o=n.config,A=o.uploadImgServer,l=o.uploadImgShowBase64,c=o.uploadImgMaxSize,r=o.uploadImgMaxLength||1e4,s=o.uploadFileName||'',d=o.uploadImgParams||{},p=o.uploadImgParamsWithUrl,g=o.uploadImgHeaders||{},m=o.uploadImgHooks||{},u=o.uploadImgTimeout||3e3,w=o.withCredentials;null==w&&(w=!1);var h=o.customUploadImg;if(h||A||l){var f=[],E=[];if(a(e,function(e){var t=e.name,n=e.size;return t&&n?!1===/\.(jpg|jpeg|png|bmp|gif)$/i.test(t)?void E.push('\u3010'+t+'\u3011\u4E0D\u662F\u56FE\u7247'):c<n?void E.push('\u3010'+t+'\u3011\u5927\u4E8E '+c/1024/1024+'M'):void f.push(e):void 0}),E.length)return void this._alert('\u56FE\u7247\u9A8C\u8BC1\u672A\u901A\u8FC7: \n'+E.join('\n'));if(f.length>r)return void this._alert('\u4E00\u6B21\u6700\u591A\u4E0A\u4F20'+r+'\u5F20\u56FE\u7247');if(h&&'function'==typeof h)return void h(f,this.insertLinkImg.bind(this));var b=new FormData;if(a(f,function(e){var t=s||e.name;b.append(t,e)}),A&&'string'==typeof A){var C=A.split('#');A=C[0];var B=C[1]||'';i(d,function(e,t){t=encodeURIComponent(t),p&&(A+=0<A.indexOf('?')?'&':'?',A=A+e+'='+t),b.append(e,t)}),B&&(A+='#'+B);var x=new XMLHttpRequest;if(x.open('POST',A),x.timeout=u,x.ontimeout=function(){m.timeout&&'function'==typeof m.timeout&&m.timeout(x,n),t._alert('\u4E0A\u4F20\u56FE\u7247\u8D85\u65F6')},x.upload&&(x.upload.onprogress=function(t){var e,o=new H(n);t.lengthComputable&&(e=t.loaded/t.total,o.show(e))}),x.onreadystatechange=function(){var e;if(4===x.readyState){if(200>x.status||300<=x.status)return m.error&&'function'==typeof m.error&&m.error(x,n),void t._alert('\u4E0A\u4F20\u56FE\u7247\u53D1\u751F\u9519\u8BEF','\u4E0A\u4F20\u56FE\u7247\u53D1\u751F\u9519\u8BEF\uFF0C\u670D\u52A1\u5668\u8FD4\u56DE\u72B6\u6001\u662F '+x.status);if(e=x.responseText,'object'!==('undefined'==typeof e?'undefined':W(e)))try{e=JSON.parse(e)}catch(o){return m.fail&&'function'==typeof m.fail&&m.fail(x,n,e),void t._alert('\u4E0A\u4F20\u56FE\u7247\u5931\u8D25','\u4E0A\u4F20\u56FE\u7247\u8FD4\u56DE\u7ED3\u679C\u9519\u8BEF\uFF0C\u8FD4\u56DE\u7ED3\u679C\u662F: '+e)}if(!m.customInsert&&'0'!=e.errno)m.fail&&'function'==typeof m.fail&&m.fail(x,n,e),t._alert('\u4E0A\u4F20\u56FE\u7247\u5931\u8D25','\u4E0A\u4F20\u56FE\u7247\u8FD4\u56DE\u7ED3\u679C\u9519\u8BEF\uFF0C\u8FD4\u56DE\u7ED3\u679C errno='+e.errno);else{if(m.customInsert&&'function'==typeof m.customInsert)m.customInsert(t.insertLinkImg.bind(t),e,n);else{var o=e.data||[];o.forEach(function(e){t.insertLinkImg(e)})}m.success&&'function'==typeof m.success&&m.success(x,n,e)}}},m.before&&'function'==typeof m.before){var y=m.before(x,n,f);if(y&&'object'===('undefined'==typeof y?'undefined':W(y))&&y.prevent)return void this._alert(y.msg)}return i(g,function(e,t){x.setRequestHeader(e,t)}),x.withCredentials=w,void x.send(b)}l&&a(e,function(e){var n=new FileReader;n.readAsDataURL(e),n.onload=function(){t.insertLinkImg(this.result)}})}}}};var X=1;G.prototype={constructor:G,_initConfig:function(){this.config=Object.assign({},z,this.customConfig);var e=this.config.lang||{},t=[];i(e,function(e,n){t.push({reg:new RegExp(e,'img'),val:n})}),this.config.langArgs=t},_initDom:function(){var t,e,n,o,i=this,a=this.toolbarSelector,c=A(a),r=this.textSelector,s=this.config,d=s.zIndex;null==r?(t=A('<div></div>'),e=A('<div></div>'),o=c.children(),c.append(t).append(e),t.css('background-color','#f1f1f1').css('border','1px solid #ccc'),e.css('border','1px solid #ccc').css('border-top','none').css('height','300px')):(t=c,e=A(r),o=e.children()),n=A('<div></div>'),n.attr('contenteditable','true').css('width','100%').css('height','100%'),o&&o.length?n.append(o):n.append(A('<p><br></p>')),e.append(n),t.addClass('w-e-toolbar'),e.addClass('w-e-text-container'),e.css('z-index',d),n.addClass('w-e-text');var p=l('toolbar-elem');t.attr('id',p);var g=l('text-elem');n.attr('id',g),this.$toolbarElem=t,this.$textContainerElem=e,this.$textElem=n,this.toolbarElemId=p,this.textElemId=g;var m=!0;e.on('compositionstart',function(){m=!1}),e.on('compositionend',function(){m=!0}),e.on('click keyup',function(){m&&i.change&&i.change()}),t.on('click',function(){this.change&&this.change()}),(s.onfocus||s.onblur)&&(this.isFocus=!1,A(document).on('click',function(o){var e=n.isContain(A(o.target)),a=t.isContain(A(o.target)),l=!(t[0]!=o.target);if(!e){if(a&&!l)return;i.isFocus&&i.onblur&&i.onblur(),i.isFocus=!1}else!i.isFocus&&i.onfocus&&i.onfocus(),i.isFocus=!0}))},_initCommand:function(){this.cmd=new Y(this)},_initSelectionAPI:function(){this.selection=new P(this)},_initUploadImg:function(){this.uploadImg=new L(this)},_initMenus:function(){this.menus=new D(this),this.menus.init()},_initText:function(){this.txt=new U(this),this.txt.init()},initSelection:function(e){var t=this.$textElem,n=t.children();if(!n.length)return t.append(A('<p><br></p>')),void this.initSelection();var o=n.last();if(e){var i=o.html().toLowerCase(),a=o.getNodeName();if('<br>'!==i&&'<br/>'!==i||'P'!==a)return t.append(A('<p><br></p>')),void this.initSelection()}this.selection.createRangeByElem(o,!1,!0),this.selection.restoreSelection()},_bindEvent:function(){var e=0,t=this.txt.html(),n=this.config,o=n.onchangeTimeout;o=parseInt(o,10),(!o||0>=o)&&(o=200);var A=n.onchange;A&&'function'==typeof A&&(this.change=function(){var n=this.txt.html();n.length===t.length&&n===t||(e&&clearTimeout(e),e=setTimeout(function(){A(n),t=n},o))});var i=n.onblur;i&&'function'==typeof i&&(this.onblur=function(){var e=this.txt.html();i(e)});var a=n.onfocus;a&&'function'==typeof a&&(this.onfocus=function(){a()})},create:function(){this._initConfig(),this._initDom(),this._initCommand(),this._initSelectionAPI(),this._initText(),this._initMenus(),this._initUploadImg(),this.initSelection(!0),this._bindEvent()},_offAllEvent:function(){A.offAll()}};try{document}catch(e){throw new Error('\u8BF7\u5728\u6D4F\u89C8\u5668\u73AF\u5883\u4E0B\u8FD0\u884C')}(function(){'function'!=typeof Object.assign&&(Object.assign=function(e){if(null==e)throw new TypeError('Cannot convert undefined or null to object');for(var t,n=Object(e),o=1;o<arguments.length;o++)if(t=arguments[o],null!=t)for(var A in t)Object.prototype.hasOwnProperty.call(t,A)&&(n[A]=t[A]);return n}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),n=t.length;0<=--n&&t.item(n)!==this;);return-1<n})})();var Z=document.createElement('style');Z.type='text/css',Z.innerHTML='.w-e-toolbar,.w-e-text-container,.w-e-menu-panel {  padding: 0;  margin: 0;  box-sizing: border-box;}.w-e-toolbar *,.w-e-text-container *,.w-e-menu-panel * {  padding: 0;  margin: 0;  box-sizing: border-box;}.w-e-clear-fix:after {  content: "";  display: table;  clear: both;}.w-e-toolbar .w-e-droplist {  position: absolute;  left: 0;  top: 0;  background-color: #fff;  border: 1px solid #f1f1f1;  border-right-color: #ccc;  border-bottom-color: #ccc;}.w-e-toolbar .w-e-droplist .w-e-dp-title {  text-align: center;  color: #999;  line-height: 2;  border-bottom: 1px solid #f1f1f1;  font-size: 13px;}.w-e-toolbar .w-e-droplist ul.w-e-list {  list-style: none;  line-height: 1;}.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item {  color: #333;  padding: 5px 0;}.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item:hover {  background-color: #f1f1f1;}.w-e-toolbar .w-e-droplist ul.w-e-block {  list-style: none;  text-align: left;  padding: 5px;}.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item {  display: inline-block;  *display: inline;  *zoom: 1;  padding: 3px 5px;}.w-e-toolbar .w-e-droplist ul.w-e-block li.w-e-item:hover {  background-color: #f1f1f1;}@font-face {  font-family: \'w-e-icon\';  src: url(data:application/x-font-woff;charset=utf-8;base64,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) format(\'truetype\');  font-weight: normal;  font-style: normal;}[class^="w-e-icon-"],[class*=" w-e-icon-"] {  /* use !important to prevent issues with browser extensions that change fonts */  font-family: \'w-e-icon\' !important;  speak: none;  font-style: normal;  font-weight: normal;  font-variant: normal;  text-transform: none;  line-height: 1;  /* Better Font Rendering =========== */  -webkit-font-smoothing: antialiased;  -moz-osx-font-smoothing: grayscale;}.w-e-icon-close:before {  content: "\\f00d";}.w-e-icon-upload2:before {  content: "\\e9c6";}.w-e-icon-trash-o:before {  content: "\\f014";}.w-e-icon-header:before {  content: "\\f1dc";}.w-e-icon-pencil2:before {  content: "\\e906";}.w-e-icon-paint-brush:before {  content: "\\f1fc";}.w-e-icon-image:before {  content: "\\e90d";}.w-e-icon-play:before {  content: "\\e912";}.w-e-icon-location:before {  content: "\\e947";}.w-e-icon-undo:before {  content: "\\e965";}.w-e-icon-redo:before {  content: "\\e966";}.w-e-icon-quotes-left:before {  content: "\\e977";}.w-e-icon-list-numbered:before {  content: "\\e9b9";}.w-e-icon-list2:before {  content: "\\e9bb";}.w-e-icon-link:before {  content: "\\e9cb";}.w-e-icon-happy:before {  content: "\\e9df";}.w-e-icon-bold:before {  content: "\\ea62";}.w-e-icon-underline:before {  content: "\\ea63";}.w-e-icon-italic:before {  content: "\\ea64";}.w-e-icon-strikethrough:before {  content: "\\ea65";}.w-e-icon-table2:before {  content: "\\ea71";}.w-e-icon-paragraph-left:before {  content: "\\ea77";}.w-e-icon-paragraph-center:before {  content: "\\ea78";}.w-e-icon-paragraph-right:before {  content: "\\ea79";}.w-e-icon-terminal:before {  content: "\\f120";}.w-e-icon-page-break:before {  content: "\\ea68";}.w-e-icon-cancel-circle:before {  content: "\\ea0d";}.w-e-icon-font:before {  content: "\\ea5c";}.w-e-icon-text-heigh:before {  content: "\\ea5f";}.w-e-toolbar {  display: -webkit-box;  display: -ms-flexbox;  display: flex;  padding: 0 5px;  /* flex-wrap: wrap; */  /* \u5355\u4E2A\u83DC\u5355 */}.w-e-toolbar .w-e-menu {  position: relative;  text-align: center;  padding: 5px 10px;  cursor: pointer;}.w-e-toolbar .w-e-menu i {  color: #999;}.w-e-toolbar .w-e-menu:hover i {  color: #333;}.w-e-toolbar .w-e-active i {  color: #1e88e5;}.w-e-toolbar .w-e-active:hover i {  color: #1e88e5;}.w-e-text-container .w-e-panel-container {  position: absolute;  top: 0;  left: 50%;  border: 1px solid #ccc;  border-top: 0;  box-shadow: 1px 1px 2px #ccc;  color: #333;  background-color: #fff;  /* \u4E3A emotion panel \u5B9A\u5236\u7684\u6837\u5F0F */  /* \u4E0A\u4F20\u56FE\u7247\u7684 panel \u5B9A\u5236\u6837\u5F0F */}.w-e-text-container .w-e-panel-container .w-e-panel-close {  position: absolute;  right: 0;  top: 0;  padding: 5px;  margin: 2px 5px 0 0;  cursor: pointer;  color: #999;}.w-e-text-container .w-e-panel-container .w-e-panel-close:hover {  color: #333;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-title {  list-style: none;  display: -webkit-box;  display: -ms-flexbox;  display: flex;  font-size: 14px;  margin: 2px 10px 0 10px;  border-bottom: 1px solid #f1f1f1;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-title .w-e-item {  padding: 3px 5px;  color: #999;  cursor: pointer;  margin: 0 3px;  position: relative;  top: 1px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-title .w-e-active {  color: #333;  border-bottom: 1px solid #333;  cursor: default;  font-weight: 700;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content {  padding: 10px 15px 10px 15px;  font-size: 16px;  /* \u8F93\u5165\u6846\u7684\u6837\u5F0F */  /* \u6309\u94AE\u7684\u6837\u5F0F */}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input:focus,.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea:focus,.w-e-text-container .w-e-panel-container .w-e-panel-tab-content button:focus {  outline: none;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea {  width: 100%;  border: 1px solid #ccc;  padding: 5px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content textarea:focus {  border-color: #1e88e5;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text] {  border: none;  border-bottom: 1px solid #ccc;  font-size: 14px;  height: 20px;  color: #333;  text-align: left;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text].small {  width: 30px;  text-align: center;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text].block {  display: block;  width: 100%;  margin: 10px 0;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content input[type=text]:focus {  border-bottom: 2px solid #1e88e5;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button {  font-size: 14px;  color: #1e88e5;  border: none;  padding: 5px 10px;  background-color: #fff;  cursor: pointer;  border-radius: 3px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.left {  float: left;  margin-right: 10px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.right {  float: right;  margin-left: 10px;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.gray {  color: #999;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button.red {  color: #c24f4a;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button:hover {  background-color: #f1f1f1;}.w-e-text-container .w-e-panel-container .w-e-panel-tab-content .w-e-button-container:after {  content: "";  display: table;  clear: both;}.w-e-text-container .w-e-panel-container .w-e-emoticon-container .w-e-item {  cursor: pointer;  font-size: 18px;  padding: 0 3px;  display: inline-block;  *display: inline;  *zoom: 1;}.w-e-text-container .w-e-panel-container .w-e-up-img-container {  text-align: center;}.w-e-text-container .w-e-panel-container .w-e-up-img-container .w-e-up-btn {  display: inline-block;  *display: inline;  *zoom: 1;  color: #999;  cursor: pointer;  font-size: 60px;  line-height: 1;}.w-e-text-container .w-e-panel-container .w-e-up-img-container .w-e-up-btn:hover {  color: #333;}.w-e-text-container {  position: relative;}.w-e-text-container .w-e-progress {  position: absolute;  background-color: #1e88e5;  bottom: 0;  left: 0;  height: 1px;}.w-e-text {  padding: 0 10px;  overflow-y: scroll;}.w-e-text p,.w-e-text h1,.w-e-text h2,.w-e-text h3,.w-e-text h4,.w-e-text h5,.w-e-text table,.w-e-text pre {  margin: 10px 0;  line-height: 1.5;}.w-e-text ul,.w-e-text ol {  margin: 10px 0 10px 20px;}.w-e-text blockquote {  display: block;  border-left: 8px solid #d0e5f2;  padding: 5px 10px;  margin: 10px 0;  line-height: 1.4;  font-size: 100%;  background-color: #f1f1f1;}.w-e-text code {  display: inline-block;  *display: inline;  *zoom: 1;  background-color: #f1f1f1;  border-radius: 3px;  padding: 3px 5px;  margin: 0 3px;}.w-e-text pre code {  display: block;}.w-e-text table {  border-top: 1px solid #ccc;  border-left: 1px solid #ccc;}.w-e-text table td,.w-e-text table th {  border-bottom: 1px solid #ccc;  border-right: 1px solid #ccc;  padding: 3px 5px;}.w-e-text table th {  border-bottom: 2px solid #ccc;  text-align: center;}.w-e-text:focus {  outline: none;}.w-e-text img {  cursor: pointer;}.w-e-text img:hover {  box-shadow: 0 0 5px #333;}',document.getElementsByTagName('HEAD').item(0).appendChild(Z);var ee=window.wangEditor||G;return ee})}])});