
<div id="app">
  <el-button @click="loc">显示当前url</el-button>
  <span>param参数: {{param}}</span>
  <a href="./test_dump?param=1">跳转到param=1</a>
  <p>
     <el-button @click="dump">跳转到param=2</el-button>
  </p>
  <p>
     <el-button @click="push">push url</el-button>
  </p>
</div>

<script>
    var vm = new Vue({
      el: '#app',
      data: {
        param: Resource.getUrlParam('param')
      },
      methods: {
        loc: function(){
          alert(location.href)
        },
        dump: function() {
          location.href = './test_dump?param=2'
        },
        push: function() {
          history.pushState('', '', './test_dump?param=3')
        }
      }
    })
</script>