<style>
 h2 {margin-top: 20px}
 p {margin-top: 20px;}
 pre {margin-top: 20px; background-color: #eee;}
</style>

<div id="app" v-cloak>
<h1>3. 安全无小事，处处为企业考虑</h1>

<h2>3.1 最完备的URL权限控制</h2>

<p>我们采用白名单URL权限控制机制，要求每一个URL都纳入管理，都需要明确配置其权限。
这样的做法势必会增加配置工作量，为了辅助解决这个问题。
我们利用Spring MVC的特性，自动扫描出系统所有的url（包括业务方的url），
通过拖拽的页面操作方式，尽可能辅助管理员简单高效地进行配置，
同时支持正则表达式匹配。
这杜绝控制了页面访问权限却遗漏了api权限控制，造成跨环境提权。</p>

<img src="/admin-demo/img/url_scan.jpg" height="50%"/>

<h2>3.2 完整的系统监控</h2>

<p>系统全局自动记录下所有的异常并保存到数据库，
同时记录下超过时限的慢URL请求和慢SQL请求。</p>

<img src="/admin-demo/img/exception_example.jpg" height="80%"/>

<p>慢SQL请求示例：</p>

<img src="/admin-demo/img/slow_sql.jpg" height="50%"/>

<h2>3.3 数据库基本安全规范</h2>

<p>基于我们的nimble-orm，系统内置支持软删除、自动设置创建修改时间、创建人，
甚至不需要业务方开发者指定软删除标记位。
此外还提供了表关联查询、表join查询的VO描述工具，
快速查询多层次关联、递归关联的数据。
</p>

<img src="/admin-demo/img/db_softdelete_autoset.jpg" height="25%"/>

</div>

<script>
  var vm = new Vue({
    el: '#app',
    data: {
    }
  })
</script>