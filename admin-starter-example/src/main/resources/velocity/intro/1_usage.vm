<style>
 h2 {margin-top: 20px}
 p {margin-top: 20px;}
 pre {margin-top: 20px; background-color: #eee;}
</style>

<div id="app" v-cloak>
<h1>1. 最简单的使用方式</h1>

<h2>1.1 使用maven jar包依赖，方便持续升级</h2>
<p>
     目前市面上多数admin管理后台框架，例如人人权限、AdminEAP等，
     都需要将框架源码下载下来，然后在源码基础上修改新增自己的业务代码。
     这样做的主要问题是：当框架有更新或修复bug时，需要手动下载最新源码并覆盖到自己项目中，
     如果业务团队不小心修改了框架的源码，那么升级时原来被改的代码就容易被覆盖，也容易代码冲突。
</p>
<p>
   为了解决这个问题，通过摸索和尝试，本框架实现了将所有框架的前后端资源，打包成jar包。
   并使用maven的依赖管理，做到只需要引入maven依赖，即可在常规的Spring MVC项目中使用本框架。
   后续框架修bug、加功能升级，都只需要升级maven依赖版本即可。
</p>
<pre>
		&lt;!-- 此处仅示例，暂未发布到maven中央仓库 -->
		&lt;dependency>
			&lt;groupId>com.pugwoo&lt;/groupId>
			&lt;artifactId>admin&lt;/artifactId>
			&lt;version>0.0.1&lt;/version>
		&lt;/dependency>
</pre>
<p>在标准的Spring MVC项目的web.xml配置文件的dispatcherServlet中，
指定上本框架提供的Spring配置文件admin-spring-conf.xml即可。
</p>
<p>
本框架使用Spring Boot + Spring MVC(velocity) + Spring JDBC(nimble-orm) + redis实现，
三层结构清晰、支持分布式水平扩展；
最小程度依赖组件，最大程度减少与用户组件冲突；追求零入侵设计，Spring老系统更容易迁移至本框架。
</p>

<h2>1.2 使用Vue + Element-UI</h2>
<p>
    Vue是目前最流行的前端框架，它是渐进式的MVVM模型框架，
    对Java后台开发人员友好，极易上手，无需学习nodejs或在开发过程中依赖nodejs。
    Element-UI则是基于Vue的一款全面好看的开源前端框架，
    是目前最流行的基于Vue且适合于后台管理系统的前端框架。
</p>
<p>目前多数管理后台都基于JQuery+Bootstrap，体验一下Vue前端技术，让前端开发效率提升远超过一倍。</p>
<p>
    得益于Vue，我们可以将任意使用nodejs/npm的vue开源组件，
    转换成对后台友好的前端组件，例如封装目前主流的编辑器wangeditor，
    如果不使用vue，那需要自行建dom、设置值、取值、配置等，如果使用vue，那只要这样写：
</p>
<pre>
   &lt;wang-editor v-model="content" height="300px">&lt;/wang-editor>
</pre>
<p>
   就得到一个富文本编辑器，在里面输入内容，很容易将输入内容源码同步显示在这里：
   <pre>{{content}}</pre>
  <wang-editor v-model="content" height="100px"></wang-editor>
</p>

<p>还有很多基于vue的组件，拿来即用：
<a href="https://v-charts.js.org/" target="_blank">Vue+Echart</a>、
<a href="https://dafrok.github.io/vue-baidu-map/" target="_blank">Vue+地图</a>
更有<a href="https://github.com/vuejs/awesome-vue" target="_blank">Vue组件大全</a>
</p>

<h2>1.3 使用Spring，需求满足情况下，不引入过多组件框架</h2>

<p>项目使用Spring+Spring MVC+Spring jdbc实现，化繁为简。
我们尽量少引入组件，但不限制客户引入其它组件，让组件配置冲突降到最低。
在这方面我们有自己的思考和追求。</p>

<p><b>* 为什么没有使用MyBatis?</b></p>

<p>我们使用了nimble-orm，一款基于Spring jdbcTemplate的极薄封装。
因此，我们不限定用户在项目中使用MyBatis或Hibernate等其它ORM。</p>

<p>nimble-orm它类似于开源中国用的DbUtils，但在处理软删除、关联查询、拦截器方面更加自动化。
在中大型项目中代替MyBatis和Hibernate是完全没有问题的。
</p>

<p><b>* 为什么不使用代码生成器？</b></p>

<p>如果项目使用了MyBatis，多数情况会使用代码生成器生成复杂的sql xml文件。
但这样做其实挺麻烦，当需要增加自定义sql时就会修改这个xml文件，
而当表结构增加字段时，又会重新生成sql xml文件（不生成就得xml文件一个一个地方加字段），
这样新生成的xml文件和旧的被修改的xml文件合并时就容易丢失代码或冲突。</p>

<p>所以，但凡使用了代码生成器，就会有这样的问题，包括前端CRUD的代码。
新生成的代码和老的被修改的生成代码之间的冲突是不可避免的，除非生成的代码后不要去改它了。
我们从技术的角度，追求不需要依赖代码生成器的效率提升，
例如vue、nimble-orm等技术，都是达成这样的一个目标。</p>

<br/>
</div>

<script>
  var vm = new Vue({
    el: '#app',
    data: {
    	content: '初始化内容，<span style="font-weight: bold; color: rgb(194, 79, 74);">你可以试试编辑这里的内容</span>'
    }
  })
</script>