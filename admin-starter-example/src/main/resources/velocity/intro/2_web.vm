<style>
 h2 {margin-top: 20px}
 p {margin-top: 20px;}
 pre {margin-top: 20px; background-color: #eee;}
</style>

<div id="app" v-cloak>
  <h1>2. 追求便利直观的操作体验</h1>

  <h2>2.1 树形拖拽管理系统URL权限</h2>

  <p>
    得益于Element-UI的树形插件，我们可以拖拽URL改变URL的目录、顺序，非常直观。
    URL的层次本身也是没有限制的。
  </p>
  <img src="/admin-demo/img/url_drag.gif" height="250px"/>

  <h2>2.2 无需繁琐的权限配置</h2>
  <p>
    系统以URL为粒度，自动扫描项目中注解了RequestMapping的方法，在URL管理页面展现出来。
    无需在各个接口方法上注解权限标识，然后再添加一个菜单权限列表，最后在角色管理中设置角色所拥有的权限。
  </p>
  <img src="/admin-demo/img/url_show1.gif" height="450px"/>

  <h2>2.3 随处可控</h2>
  <p>
    各个权限管理页面相互关联，清晰的操作界面，不必因为需要修改一个配置，到处切换页面。
    页面采用树形结构，复选框等进行操作，无需填写复杂的标识。
  </p>
  <img src="/admin-demo/img/role_show1.gif" height="450px"/>

</div>

<script>
  var vm = new Vue({
    el: '#app',
    data: {
    }
  })
</script>