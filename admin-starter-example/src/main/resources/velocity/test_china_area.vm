#parse('./components/china_area_cascader.vm')

<style>
  .long {  width: 350px;  margin-bottom: 50px}
</style>

<div id="app">
  <div>----test 1 基本----</div>
  <div>绑定值：{{componentVmodel1}}</div>
##  <div>区域码转汉字：{{CodeToText[componentVmodel[0]]}},{{CodeToText[componentVmodel[1]]}},{{CodeToText[componentVmodel[2]]}}</div>
##  <div>汉字转区域码：{{convertTextToCode(CodeToText[componentVmodel[0]], CodeToText[componentVmodel[1]], CodeToText[componentVmodel[2]])}}</div>
  <div>testGetText：{{testGetText1}}</div>
  <china-area-cascader v-model="componentVmodel1" class="long" ref="area1"></china-area-cascader>

  <div>----test 2 可擦除----</div>
  <div>绑定值：{{componentVmodel2}}</div>
  <div>testGetText：{{testGetText2}}</div>
  <china-area-cascader v-model="componentVmodel2" class="long" ref="area2" clearable>
  </china-area-cascader>

  <div>----test 3 hover弹出下级选项，可直接搜索----</div>
  <div>绑定值：{{componentVmodel3}}</div>
  <div>testGetText：{{testGetText3}}</div>
  <china-area-cascader v-model="componentVmodel3" class="long" ref="area3"
                       expand-trigger="hover" filterable placeholder="placeholder提示信息">
  </china-area-cascader>

  <div>----test 4 仅显示最后一级  width="700"----</div>
  <div>绑定值：{{componentVmodel4}}</div>
  <div>testGetText：{{testGetText4}}</div>
  <china-area-cascader v-model="componentVmodel4" width="700" ref="area4"
                       :show-all-levels="false"></china-area-cascader>

</div>

<script>
  var vm = new Vue({
    el: '#app',
    data:  {
      componentVmodel1: [],
      testGetText1: [],

      componentVmodel2: ["440000", "440300", "440307"],
      testGetText2: [],

      componentVmodel3: [],
      testGetText3: [],

      componentVmodel4: [],
      testGetText4: [],
    },
    watch: {
      componentVmodel1: function () {
        this.testGetText1 = this.$refs.area1.getText()
      },
      componentVmodel2: function () {
        this.testGetText2 = this.$refs.area2.getText()
      },
      componentVmodel3: function () {
        this.testGetText3 = this.$refs.area3.getText()
      },
      componentVmodel4: function () {
        this.testGetText4 = this.$refs.area4.getText()
      }

    },
    methods: {
    }
  });
</script>