#parse('admin_components/user_select.vm')

<div id="app">
    <p>{{msg}}</p>

    <elx-upload action="${_contextPath_}/admin_upload/upload" v-model="uploadFiles" :num="5"
          :thumbnails="thumbnails">
        <template slot-scope="props">
            <input v-model="props.data.name" placeholder="文件名" style="width: 100px;"/>
        </template>
    </elx-upload>
    
    <p>数据: {{uploadFiles}}</p>
    
    <div>
        <user-select v-model="userId"></user-select>
        <span>选择的userId: {{userId}}</span>
    </div>
    <div>
        <user-select v-model="userId2"></user-select>
        <span>选择的userId2: {{userId2}}</span>
    </div>
    <div>
        <user-select v-model="userIds" :multiple="true"></user-select>
        <span>选择的userIds: {{userIds}}</span>
    </div>
    <div>
        <user-select v-model="userIds2" :multiple="true"></user-select>
        <span>选择的userIds2: {{userIds2}}</span>
    </div>
</div>

<script>
    var vm = new Vue({
        el: '#app',
        data: {
        	userId: 2,
        	userId2: '',
        	userIds: [ "117", 116],
        	userIds2: '',
            thumbnails : {
                'doc': 'https://pugwoo-1251050007.cos.ap-guangzhou.myqcloud.com/word-icon.png',
                'docx': 'https://pugwoo-1251050007.cos.ap-guangzhou.myqcloud.com/word-icon.png'
            },
            msg: 'hello world!',
            uploadFiles:[],
        }
    });
</script>