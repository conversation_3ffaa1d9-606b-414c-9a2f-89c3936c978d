<div id="app">
    <p>请逐个体验一下功能并观察请求打印是否正常</p>

    <el-button @click="testGet">1. 测试Get，将发出4个请求</el-button>
    <br/>
    <el-button @click="testPost">2. 测试Post QueryString，将发出6个请求</el-button>
    <br/>
    <el-button @click="testPost2">3. 测试Post Json，将发出2个请求</el-button>
    <br/>
    <p>4. 测试上传文件，这个要找个图片或二进制文件上传下载并比较md5</p>
    <elx-upload action="${_contextPath_}/admin_upload/upload" v-model="uploadFiles" :num="5"
                :thumbnails="thumbnails">
        <template slot-scope="props">
            <input v-model="props.data.name" placeholder="文件名" style="width: 100px;"/>
        </template>
    </elx-upload>
    <br/>
    <el-button @click="download">5. 下载用户列表</el-button>
</div>

<script>
    var vm = new Vue({
        el: '#app',
        data: {
            thumbnails : {
                'doc': 'https://pugwoo-1251050007.cos.ap-guangzhou.myqcloud.com/word-icon.png',
                'docx': 'https://pugwoo-1251050007.cos.ap-guangzhou.myqcloud.com/word-icon.png'
            },
            uploadFiles:[],
        },
        methods: {
            testGet: function() {
                Resource.get('${_contextPath_}/log_get_test_1')
                Resource.get('${_contextPath_}/log_get_test_1?name=nick')
                Resource.get('${_contextPath_}/log_get_test_2')
                Resource.get('${_contextPath_}/log_get_test_2?name=nick')
                Message.success("已发出4次请求，请查看后台日志")
            },
            testPost: function() {
                Resource.post('${_contextPath_}/log_post_test_1')
                Resource.post('${_contextPath_}/log_post_test_1?name=nick')
                Resource.post('${_contextPath_}/log_post_test_1', {name: "nick"})
                Resource.post('${_contextPath_}/log_post_test_2')
                Resource.post('${_contextPath_}/log_post_test_2?name=nick')
                Resource.post('${_contextPath_}/log_post_test_2', {name: "nick"})
                Message.success("已发出6次请求，请查看后台日志")
            },
            testPost2: function() {
                Resource.postJson('${_contextPath_}/log_post_test_json')
                Resource.postJson('${_contextPath_}/log_post_test_json','{"name":"nick"}')
                Message.success("已发出2次请求，请查看后台日志")
            },
            download: function() {
                Resource.download('${_contextPath_}/admin_user/download',
                        {page: 1, pageSize:100})
            }
        }
    })
</script>