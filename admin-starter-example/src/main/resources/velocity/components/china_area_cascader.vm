<script type="text/x-template" id="fa5fe4a601dc">
  <el-cascader v-model="myvalue" :options="options" :placeholder="placeholder" :size="size"
    :clearable="clearable" :expand-trigger="expandTrigger" :show-all-levels="showAllLevels"
    :filterable="filterable" :style="style">
  </el-cascader>
</script>

<script src="${_contextPath_}/js/china-area-utils.js?v=0.3.1"></script>
<script>
  Vue.component('china-area-cascader', {
    template: '#fa5fe4a601dc',
    props: {
      value: '', // v-model
      size: {type: String, default: "medium"},
      options: {type: Array, default: function() {
        return RegionData.regionDataPlus
      }},
      placeholder: {type: String, default: "请选择地址"},
      clearable: {type: Boolean, default: false},
      expandTrigger: {type: String, default: "click"},
      showAllLevels: {type: <PERSON><PERSON><PERSON>, default: true},
      filterable: {type: Boolean, default: false},
      width: {type: String, default: ''}
    },
    data: function() {
      return {
        myvalue: [],
        CodeToText: RegionData.CodeToText,
        style: {}
      }
    },
    created: function () {
      this.myvalue = this.value
      if (this.width && (this.width + '').trim()) {
        this.style.width = this.width
      }
    },
    watch: {
      myvalue: function(val) {
        this.$emit("input", val)
      },
      value: function(val) {
        this.myvalue = val ? val : ''
      }
    },
    methods: {
      getText: function () {
        var result = []
        var that = this
        for (var i = 0, iLen = that.myvalue.length; i < iLen; i++) {
          var temp = that.myvalue[i]
          if (temp && (temp + '').trim()) {
            result.push(that.CodeToText[temp])
          } else {
            result.push("全部")
          }
        }
        return result
      }
    }
  })
</script>