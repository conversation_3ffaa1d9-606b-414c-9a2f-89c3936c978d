spring:
  profiles:
    active: dev
  servlet:
    multipart:
      max-request-size: "**********" # 4G
      max-file-size: "**********" # 4G
server:
  servlet:
    context-path: /admin-demo

management:
  endpoints:
    web:
      exposure:
        include: health, info, beans, prometheus

logging:
  level:
    root: info
    com.pugwoo: debug

admin:
  jdbc:
    disableAdminDBHelperAsPrimary: false # 默认false，即adminDBHelper会作为primary DBHelper
  systemName: admin管理系统
  isActuallySendMsg: false # 测试环境，不实际发送消息
  securityLevel: STRICT
  slowWebTimeMs: 3000
  schedulePoolSize: 10
  enableWebLog: true
  enableWebLogCallback: false
  logWebSlowToDB: true
  enableCSRFCheck: false
  enableWebLogToDB: true
  useCDNResource: true
  requestLimit:
    enableConcurrentLimit: true
    maxConcurrent: 70
  token:
    expireSecond: 259200
  txcos:
    secretId: AKID75uUs2Kf7KHzra7NSHrDKCoBJBoEJdYF
    secretKey: hBYlngRyHMCnAJEuMaYHk25HKGC1g9QX
    region: ap-guangzhou
    bucketName: dev-**********
  mail:
    sender: <EMAIL>
    password: omdinrszxjyucbbg
    smtphost: smtp.qq.com
  dingding:
    robots:
      - accessToken: 75eaa49e339db7923a351c2cb518e621ba6e0d8329b828d9299aa27bd0a10e45
        secret: SECb065dd816741918d79621a611f97f7e58089*************************
