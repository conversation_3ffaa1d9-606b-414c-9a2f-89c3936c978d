package com.example.admin.web;

import com.example.admin.web.form.DateInForm;
import com.pugwoo.admin.utils.NotRequireLogin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 测试下日期转换工具正不正常
 */
@NotRequireLogin
@RestController
public class TestDateConverterController {


    @GetMapping("/date_converter1")
    public Object dateConverter1(Date myDate) {
        return myDate;
    }

    @GetMapping("/date_converter2")
    public Object dateConverter2(DateInForm form) {
        if(form == null) {
            return "form is null";
        } else if(form.getMyDate() == null){
            return "form.myDate is null";
        } else {
            return form.getMyDate();
        }
    }

}
