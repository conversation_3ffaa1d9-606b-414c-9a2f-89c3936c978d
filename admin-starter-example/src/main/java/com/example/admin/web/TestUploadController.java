package com.example.admin.web;

import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.utils.NotRequireLogin;
import com.pugwoo.dbhelper.DBHelper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 上传文件，约定前端上传文件的<input type="file" name="file"> name为file
 * <AUTHOR>
 */
@Controller
public class TestUploadController {
	
	@Autowired
	private AdminUserService adminUserService;
	@Autowired
	private DBHelper dbHelper;
	
	@RequestMapping("/test_upload")
	public String testUpload() {
		return "test_upload";
	}
	
	@NotRequireLogin
	@RequestMapping("/hello_world")
	public String hello() throws BindException {
		System.out.println(adminUserService);
		System.out.println(dbHelper);
		throw new BindException(db<PERSON><PERSON>per, "test");
		//return "hello_world";
	}

}
