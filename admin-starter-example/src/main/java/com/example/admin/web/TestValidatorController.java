package com.example.admin.web;

import com.example.admin.enums.ColorEnum;
import com.example.admin.web.form.User;
import com.pugwoo.admin.bean.AdminErrorCode;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.NotRequireLogin;
import com.pugwoo.admin.web.validator.CannotHaveBlank;
import com.pugwoo.admin.web.validator.EnumValid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date 2018-08-01
 *
 * 说明，有两个注解 @Valid和@Validated，一般没啥差异，都能用
 *
 * 小差异是：https://www.baeldung.com/spring-valid-vs-validated
 *
 * @Validated支持分组groups，而@Valid不支持
 * @Valid支持注解在成员属性上，而@Validation不支持
 *
 */
@RestController
@RequestMapping("/validator")
@NotRequireLogin
@Validated // 需要加上这个注解，才可以对Controller中普通的参数列表进行校验
public class TestValidatorController {

	/**
	 * 测试类字段的校验
	 * http://127.0.0.1:8080/admin-demo/validator/test_object_field_1?name=ac&password=000
	 * 校验正常，正确输出错误信息
	 */
	@RequestMapping("test_object_field_1")
	public WebJsonBean testObjectField1(@Validated User user,
	       BindingResult bindingResult /*这个注入相当于由应用自行处理参数校验异常*/) {
		if (bindingResult.hasErrors()) {
			return WebJsonBean.fail(AdminErrorCode.ILLEGAL_PARAMETERS,
					bindingResult.getAllErrors());

		}
		return WebJsonBean.ok(user);
	}

	/**
	 * 测试类字段的校验
	 * http://127.0.0.1:8080/admin-demo/validator/test_object_field_2?name=ac&password=000
	 * 与上面测试接口少BindingResult bindingResult 报400错误
	 *    抛出的异常 BindException 被spring默认的处理器处理了
	 *    采用注解方式的异常处理，指定捕获的异常类型
	 */
	@RequestMapping("test_object_field_2")
	public WebJsonBean testObjectField2(@Validated User user) {

		return WebJsonBean.ok(user);
	}

	/**
	 * 测试方法参数的校验
	 *     需要在类上面添加 @Validated 注解
	 * http://127.0.0.1:8080/admin-demo/validator/test_param_1
	 * 校验正常，校验不通过进入全局异常处理
	 *
	 */
	@RequestMapping("test_param_1")
	public WebJsonBean testParam1(
			@NotBlank(message = "参数string不能为空") String string
	) {

		return WebJsonBean.ok(string);
	}

	/**
	 * 自定义校验
	 *    添加注解
	 *    添加校验类 implements ConstraintValidator
	 * http://127.0.0.1:8080/admin-demo/validator/test_customize?name=111%2033
	 */
	@RequestMapping("test_customize")
	public WebJsonBean customize(
			@CannotHaveBlank(message = "名字不能包含空格") String name
	) {
		return WebJsonBean.ok(name);
	}

	@GetMapping("/test_enum_validator")
	public WebJsonBean testEnumValidator(
			@EnumValid(enumClass = ColorEnum.class) String colorEnum
	) {
		return WebJsonBean.ok(colorEnum);
	}

}
