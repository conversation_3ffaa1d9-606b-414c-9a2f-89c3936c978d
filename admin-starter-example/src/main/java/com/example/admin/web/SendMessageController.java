package com.example.admin.web;

import com.pugwoo.admin.utils.NotRequireLogin;
import com.pugwoo.admin.utils.dingding.DingDingMsgDTO;
import com.pugwoo.admin.utils.dingding.DingDingUtils;
import com.pugwoo.admin.utils.qiyeweixin.QiyeWeixinUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@NotRequireLogin
public class SendMessageController {

    @Autowired
    private DingDingUtils dingDingUtils;

    /**
     * 说明：sec的配置信息在application.yml中，但是被*****模糊了，请修改才能真正发送
     */
    @GetMapping("/dingding")
    public String hello(String msg) {
        dingDingUtils.send("75eaa49e339db7923a351c2cb518e621ba6e0d8329b828d9299aa27bd0a10e45",
                DingDingMsgDTO.of(msg));
        return "hello";
    }

    /**
     * 请修改robotKey才能真正发送
     */
    @GetMapping("/qiyeweixin")
    public String qiyeWeixin(String msg) {
        QiyeWeixinUtils.send("352d8350-8fc5-421c-a479-**************", msg);
        return "hello";
    }

}
