package com.example.admin.web;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.service.AdminUserService;
import com.pugwoo.admin.utils.PageUtils;
import com.pugwoo.admin.vo.AdminUserRelatedDepartmentVO;
import com.pugwoo.bootwebext.JsonParam;
import com.pugwoo.bootwebext.bean.DownloadBean;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.json.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller @RequestMapping("/admin_user")
public class TestDownloadController {

	@Autowired
	private AdminUserService userService;
	
	@RequestMapping("/test_download")
	public String download() {
		return "test_download";
	}
	
	// 仅作下载演示用，实际项目中，应该会有导出excel的场景
	// 这里是一个直接下载的页面，所以不要用 @ResponseBody，不然会被CSRF拦下来
	@RequestMapping("download")
	public DownloadBean downloadJson(int page, int pageSize, String keyword,
	                                 @JsonParam("departmentIds") List<Long> departmentIds,
	                                 @JsonParam("dateRange") List<Date> dateRange) throws Exception {
		WebJsonBean webJsonBean = listData(page, pageSize, keyword, departmentIds, dateRange);
		return new DownloadBean("userlist.txt", JSON.toJson(webJsonBean.getData()));
	}
	
	private WebJsonBean listData(int page, int pageSize, String keyword, 
			@JsonParam("departmentIds") List<Long> departmentIds,
			@JsonParam("dateRange") List<Date> dateRange) throws Exception {
		Date createStarttime = dateRange != null && dateRange.size() > 0 ? dateRange.get(0) : null;
		Date createEndTime = dateRange != null && dateRange.size() > 1 ? dateRange.get(1) : null;

		PageData<AdminUserRelatedDepartmentVO> pageData = userService.getUserWithDepartments(
				page, pageSize, keyword, departmentIds, createStarttime, createEndTime);
		
		Map<String, Object> data = PageUtils.trans(pageData, o -> {
				Map<String, Object> map = new HashMap<>(); // 这是最规范的写法
				map.put("id", o.getId());
				map.put("createTime", o.getCreateTime());
				map.put("userName", o.getUserName());
				map.put("realName", o.getRealName());
				map.put("phone", o.getPhone());
				map.put("departmentId", o.getDepartmentId());
				map.put("position", o.getPosition());
				map.put("remark", o.getRemark());
				map.put("departmentName", o.getDepartmentName());
				map.put("company", o.getCompany());
				map.put("isAdmin", o.getIsAdmin() == null ? false : o.getIsAdmin());
				map.put("disabled", o.getDisabled() == null ? false : o.getDisabled());
				return map;
		});
		return WebJsonBean.ok(data);
	}
}
