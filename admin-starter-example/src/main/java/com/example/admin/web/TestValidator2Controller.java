package com.example.admin.web;

import com.example.admin.web.form.User;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.NotRequireLogin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import jakarta.validation.constraints.Size;
import java.util.*;

/**
 * @date 2018-08-01
 */
@RestController
@RequestMapping("/validator2")
@NotRequireLogin
@Validated
public class TestValidator2Controller {

	/**
	 * 手动调用用 使用 jakarta.validation.Validator
	 */
	@Autowired
	Validator globalValidator;

	/**
	 *
	 * 测试方法参数的校验
	 * http://127.0.0.1:8080/admin-demo/validator2/test_param_1?code=11111
	 *
	 */
	@RequestMapping("test_param_1")
	public WebJsonBean testParam1(@Size(min = 4, max = 10) String code) {

		return WebJsonBean.ok(code);
	}


	/**
	 * 手动调用
	 *    1. 注入 jakarta.validation.Validator
	 *    2. 方法内使用
	 * http://127.0.0.1:8080/admin-demo/validator2/test_manual?name=ac&password=000
	 */
	@RequestMapping("test_manual")
	public WebJsonBean testManual(User user) {

		// 此处校验接口最终的实现类便是LocalValidatorFactoryBean
		Set<ConstraintViolation<User>> set = globalValidator.validate(user);
		List<String> validateMessage = new ArrayList<>();
		for (ConstraintViolation<User> constraintViolation : set) {
			validateMessage.add(constraintViolation.getMessage());
		}
		Map<String, Object> map = new HashMap<>();
		map.put("user", user);
		map.put("validateMessage", validateMessage);
		return WebJsonBean.ok(map);
	}


}
