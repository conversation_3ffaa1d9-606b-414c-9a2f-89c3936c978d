package com.example.admin.web;

import com.pugwoo.admin.utils.NotRequireLogin;
import com.pugwoo.bootwebext.bean.StreamDownloadBean;
import com.pugwoo.wooutils.compress.ZipUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.io.MyPipe;
import com.pugwoo.wooutils.thread.ThreadPoolUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;

@Controller
@RequestMapping("/")
public class TestZipDownloanController {

    private static ExecutorService pool = ThreadPoolUtils.createThreadPool(3, 10, 3, "zip");

    @NotRequireLogin
    @GetMapping("/zip_download")
    public StreamDownloadBean download() throws IOException {
        String path = "D:\\software\\goland-2024.2.3.win"; // 要压缩的文件夹，请根据实际机器上目录，找一个1G左右的目录作为测试

        MyPipe pipe = IOUtils.getPipe();
        pool.submit((Callable<Object>) () -> {
            ZipUtils.zip(new File(path), pipe.getOutputStream());
            pipe.getOutputStream().close();
            return "ok";
        });

        return new StreamDownloadBean("data.zip", pipe.getInputStream());
    }

}
