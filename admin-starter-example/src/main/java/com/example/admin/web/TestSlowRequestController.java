package com.example.admin.web;

import com.pugwoo.admin.utils.NotRequireLogin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@NotRequireLogin
@RestController
public class TestSlowRequestController {

    @GetMapping("/slow_request")
    public String testSlowRequest() {
        try {
            log.info(Thread.currentThread().getName() + ": now sleep");
            Thread.sleep(20000);
            log.info(Thread.currentThread().getName() + ": sleep end");
        } catch (InterruptedException e) {
        }
        return "Hello World";
    }

}
