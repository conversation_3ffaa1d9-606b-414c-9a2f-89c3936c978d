package com.example.admin.web;

import com.example.admin.web.form.SimpleDTO;
import com.pugwoo.admin.utils.NotRequireLogin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 这个接口不需要登录，主要用于测试post json和上次文件是否会导致被攻击的场景
 */
@NotRequireLogin
@RestController
public class TestPostAttackController {

    @PostMapping("/test_post_json")
    public String testPostJson(@RequestBody SimpleDTO simpleDTO) {
        return simpleDTO.getName();
    }

    @PostMapping(value="/test_upload_file")
    public String testUploadFile(@RequestParam MultipartFile[] file) throws IOException {
        return "upload files: " + file.length;
    }

}
