package com.example.admin.web;

import com.pugwoo.wooutils.log.MDCUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@RestController
public class TestTraceUuidController {

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    private final ExecutorService executorService1 = Executors.newVirtualThreadPerTaskExecutor();

    @GetMapping("/testTraceUuid")
    public String testTraceUuid() {
        final String key = "requestUuid";
        log.info("异步前的 traceId: {}", MDC.get(key));

        // 说明：CompletableFuture这种不支持，同时也不建议用CompletableFuture，因为它是整个jvm全局的，影响不可控
        // 如果实在要用，就要手工套一层MDCUtils.withMdc
        CompletableFuture.runAsync(MDCUtils.withMdc(() -> log.info("future后的 traceId: {}", MDC.get(key))));

        executorService.submit(() -> {
            log.info("线程池后的 traceId: {}", MDC.get(key));
        });

        new Thread(() -> {log.info("新线程后的 traceId: {}", MDC.get(key));}).start();

        Thread.ofVirtual().start(() -> {log.info("新协程后的 traceId: {}", MDC.get(key));});

        executorService1.submit(() -> {
            log.info("虚拟线程池后的 traceId: {}", MDC.get(key));
        });

        return "ok";
    }

}
