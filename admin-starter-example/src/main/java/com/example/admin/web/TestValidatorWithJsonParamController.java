package com.example.admin.web;

import com.example.admin.web.form.User;
import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.NotRequireLogin;
import com.pugwoo.bootwebext.JsonParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date 2018-08-01
 */
@RestController
@RequestMapping("/validator_jsonparam")
@NotRequireLogin
public class TestValidatorWithJsonParamController {

	/**
	 * 类字段校验 + JsonParam
	 */
	@RequestMapping("test_object_field_2")
	public WebJsonBean testObjectField2( @JsonParam("u") @Validated User user) {

		return WebJsonBean.ok(user);
	}

}
