package com.example.admin.web;

import com.pugwoo.admin.utils.NoPermission;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/intro")
public class SystemIntroduceController {

	@NoPermission(name = "1. 简单的使用方式")
	@GetMapping("1_usage")
	public String a1_usage() {
		return "intro/1_usage";
	}

	@NoPermission(name = "2. 便利的页面操作体验")
	@GetMapping("2_web")
	public String a2_web() {
		return "intro/2_web";
	}

	@NoPermission(name = "3. 安全无小事")
	@GetMapping("3_security")
	public String a3_security() {
		return "intro/3_security";
	}
	
}
