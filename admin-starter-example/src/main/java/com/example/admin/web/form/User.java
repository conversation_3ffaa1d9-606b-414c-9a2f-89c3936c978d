package com.example.admin.web.form;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * @date 2018-08-01
 */
@Data
public class User {

	private Long id;

	@NotBlank(message = "名字不能为空")
	@Size(min = 3, max = 16, message = "名字必须为{min}-{max}个字符")
	private String name;

	@NotBlank(message = "密码不能为空")
	@Size(min = 6, message = "密码必须大于{min}个字符")
	private String password;

}
