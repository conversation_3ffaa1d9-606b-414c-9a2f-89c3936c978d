package com.example.admin.web;

import com.pugwoo.admin.bean.WebJsonBean;
import com.pugwoo.admin.utils.Permission;
import com.pugwoo.admin.web.interceptor.AdminUserLoginContext;
import com.pugwoo.admin.web.interceptor.AdminUserLoginInterceptor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date 2018-08-02
 *
 * 测试获取用户登录态
 */
@RestController
@RequestMapping("/test_context")
@Permission(value = "testContext", name = "获取当前用户登录上下文") // 等价于下面所有的接口都应该有这个权限，除非被覆盖
public class TestAdminUserLoginContextController {

	//@Permission(value = "testContext", name = "获取当前用户登录上下文")
	@RequestMapping("test")
	public WebJsonBean test() {
		AdminUserLoginContext adminUserLoginContext = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
		return WebJsonBean.ok(adminUserLoginContext);
	}

	//@Permission(value = "testContext", name = "获取当前用户登录上下文2")
	@RequestMapping("test2")
	public WebJsonBean test2(AdminUserLoginContext adminUserLoginContext) {
		return WebJsonBean.ok(adminUserLoginContext);
	}
}
