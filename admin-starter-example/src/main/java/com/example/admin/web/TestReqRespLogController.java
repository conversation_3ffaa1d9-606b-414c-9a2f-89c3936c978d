package com.example.admin.web;

import com.pugwoo.admin.bean.WebJsonBean;
import lombok.Data;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 测试打印Request和Response内容是否正确
 */
@Controller
public class TestReqRespLogController {

    @RequestMapping("/test_req_resp_log")
    public String reqRespLog() {
        return "test_req_resp_log";
    }

    /**测试GET，带和不带参数*/
    @ResponseBody
    @GetMapping("/log_get_test_1")
    public WebJsonBean get(String name) {
        if (name == null) {
            return WebJsonBean.ok("没有传参数");
        }
        return WebJsonBean.ok("后台传递的参数 name=" + name);
    }

    @Data
    public static class Student {
        private String name;
    }

    /**测试GET，带和不带参数*/
    @ResponseBody
    @GetMapping("/log_get_test_2")
    public WebJsonBean get2(Student student) {
        if (student == null) {
            return WebJsonBean.ok("没有传参数");
        }
        return WebJsonBean.ok("后台传递的参数 name=" + student.getName());
    }

    @ResponseBody
    @PostMapping("/log_post_test_1")
    public WebJsonBean post(String name) {
        if (name == null) {
            return WebJsonBean.ok("没有传参数");
        }
        return WebJsonBean.ok("后台传递的参数 name=" + name);
    }

    @ResponseBody
    @PostMapping("/log_post_test_2")
    public WebJsonBean post2(Student student) {
        if (student == null) {
            return WebJsonBean.ok("没有传参数");
        }
        return WebJsonBean.ok("后台传递的参数 name=" + student.getName());
    }

    @ResponseBody
    @PostMapping("/log_post_test_json")
    public WebJsonBean postJson(@RequestBody(required = false) Student student) {
        if (student == null) {
            return WebJsonBean.ok("没有传参数");
        }
        return WebJsonBean.ok("后台传递的参数 name=" + student.getName());
    }

}
