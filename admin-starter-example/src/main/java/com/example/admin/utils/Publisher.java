package com.example.admin.utils;

import com.example.admin.service.ISubscriber;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 仅用于spock测试
 */
public class Publisher {

    List<ISubscriber> subscribers;
    ISubscriber subscriber;

    void send(String message){
		for (ISubscriber subscriber : subscribers) {
			subscriber.receive(message);
		}
    }

	void send2(String message){
		if (StringUtils.isNotBlank(message)) {
			if (message.startsWith("00") || message.startsWith("30")) {
				subscriber.receive("sz", message);
				return;
			}
			if (message.startsWith("60")) {
				subscriber.receive("sh", message);
				return;
			}
			subscriber.receive(message);
		}
	}
}