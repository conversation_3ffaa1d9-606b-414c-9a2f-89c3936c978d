package com.example.admin.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum ColorEnum {

    BLUE("BLUE", "蓝色"),

    RED("RED", "红色"),

    GREEN("GRE<PERSON>", "绿色")

    ;

    final private String code;
    final private String name;

    ColorEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ColorEnum getByCode(String code) {
        for (ColorEnum e : ColorEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        ColorEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}