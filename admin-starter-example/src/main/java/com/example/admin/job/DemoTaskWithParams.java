package com.example.admin.job;

import com.pugwoo.admin.web.tasklog.TaskLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 带参数的任务示例，用于测试运行功能
 */
@Slf4j
@Component
public class DemoTaskWithParams {

    /**
     * 无参数的任务
     */
    @TaskLog(taskName = "无参数示例任务", taskCode = "DEMO_NO_PARAMS")
    public void taskWithoutParams() {
        log.info("执行无参数任务开始");
        try {
            Thread.sleep(2000); // 模拟任务执行
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("执行无参数任务结束");
    }

    /**
     * 带字符串参数的任务
     */
    @TaskLog(taskName = "带字符串参数示例任务", taskCode = "DEMO_STRING_PARAM")
    public void taskWithStringParam(String message) {
        log.info("执行带字符串参数任务开始，参数: {}", message);
        try {
            Thread.sleep(1000); // 模拟任务执行
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("执行带字符串参数任务结束");
    }

    /**
     * 带多个参数的任务
     */
    @TaskLog(taskName = "带多个参数示例任务", taskCode = "DEMO_MULTI_PARAMS")
    public void taskWithMultipleParams(String name, Integer age, Boolean active) {
        log.info("执行带多个参数任务开始，参数: name={}, age={}, active={}", name, age, active);
        try {
            Thread.sleep(1500); // 模拟任务执行
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("执行带多个参数任务结束");
    }

    /**
     * 带复杂对象参数的任务
     */
    @TaskLog(taskName = "带复杂对象参数示例任务", taskCode = "DEMO_OBJECT_PARAM")
    public void taskWithObjectParam(java.util.Map<String, Object> data) {
        log.info("执行带复杂对象参数任务开始，参数: {}", data);
        try {
            Thread.sleep(1000); // 模拟任务执行
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("执行带复杂对象参数任务结束");
    }

    /**
     * 会抛出异常的任务，用于测试错误处理
     */
    @TaskLog(taskName = "异常示例任务", taskCode = "DEMO_ERROR_TASK")
    public void taskWithError(String errorMessage) {
        log.info("执行异常任务开始，参数: {}", errorMessage);
        if ("error".equals(errorMessage)) {
            throw new RuntimeException("模拟任务执行异常: " + errorMessage);
        }
        log.info("执行异常任务正常结束");
    }
}
