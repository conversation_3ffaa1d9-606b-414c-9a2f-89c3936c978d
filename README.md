## GetStarted

## starter配置说明

暂时详见admin-starter-example的application.yaml说明。

## 常见问题

**1. 关于MySQL多数据源**

当只有一个数据源时，admin-tpl会拿到唯一的JdbcTemplate实例化一个名为adminDBHelper的DBHelper，并使用该对象操作数据库。

当有多个数据源时，admin-tpl会拿到名称为adminJdbcTemplate的对象并实例化一个名为adminDBHelper的DBHelper，并使用该对象操作数据库。

因此，当有多个数据源时，请提供好adminJdbcTemplate。

另外，也是最重要的，admin使用了@Transactional来管理事务，当有多个数据源且存在多个事务管理器时，请确保为admin数据源创建事务管理器并设置为@Primary。否则，事务将失效或应用将起不来。

**2. 关于Redis多数据源**

当只有一个redis数据源时，可以配置在admin.redis下，也可以配置在spring.redis下（前者优先），参数都是host、port、password、database，会实例化一个名为adminRedisHelper的RedisHelper，并使用该对象操作redis。

当有多个redis数据源时，admin-tpl以admin.redis为准，由它实例化名为adminRedisHelper的RedisHelper，并使用该对象操作redis。

**3. 如果项目使用了velocity模板渲染**

那么velocity模板文件需要放在`src/main/resources/velocity`目录下，同时必须在当前目录下创建一个文件`layout_ex.vm`。该文件是项目的额外模板文件，可以在这里全局导入js和css。

## 初始化说明

1. 先执行数据库sql创表语句create_table.sql，执行创建用户语句init_data.sql。
2. 使用用户admin密码admin登录，浏览器打开/admin_url/list和/admin_role/list管理角色和权限。
3. 代码生成工具：https://rainbow.pugwoo.com/rainbow/nimble-orm-gen

## admin管理员权限说明

在admin-template的权限设计中，如果一个账号被设置为管理员（t_admin_user的is_admin为true），那么它将有权限访问com.pugwoo.admin包下的所有页面和接口；但是它没有权限访问其它包的页面和接口，这样设计的目的是限制管理员的权限范围，让管理员只能操作和admin用户、权限设置、角色相关的事情但不能越界。

admin-template左侧的菜单页面则和是否是管理员无关，它由用户-角色-权限的关系来设置。

## 附: 其他问题

详见[FAQ.md](docs/FAQ.md)
