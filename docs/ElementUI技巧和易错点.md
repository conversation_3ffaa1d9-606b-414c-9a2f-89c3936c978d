1. 分页等其它组件，里面绑定的父组件data变量都是单向绑定的：子组件的数据变化只能通过监听事件回调函数的方式告诉父组件。

2. 表单el-form的model和el-form-item的prop属性是为了校验而存在，如果表单不需要校验，就不需要。(2018年5月12日 11:38:25) 

3. 对于datepicker组件，例如对日期的预设值，要指定其格式，否则组件无法编辑。
   `<el-date-picker>`加上`value-format="yyyy-MM-dd"`属性。另外，如果是由js修改了datepicker组件对应的属性值，不可以用`a.b.c='2018-12-12'`的修改方式，应该用`this.$set(a.b, 'c', '2018-12-12')`的形式。

4. 对于新增Add，如果需要由代码去改变addEditForm，那么defaultAddForm中需要声明该变量。

5. ElementDialog的点击模板关闭有一个问题：当我的鼠标点击在弹框内，然后滑动出到模板地方，松开，此时模板被关闭了，期望是不关闭的。需要在el-dialog上加上`:close-on-click-modal="false"` 现在还没找到通用的设置方法，只能手工加。还好这个数量不多。

6. el-input搜索框，当只有一个时，会使得按回车键无法搜索。此时要加一个隐藏的el-input就好了：`<el-input style="display: none"></el-input>`

7. 关于下拉按钮command支持更复杂的数据结构：https://www.jianshu.com/p/b86e79ca9382

8. 2023年4月22日08:57:32 将页面里的模块转换成vue组件，是维护代码的一种很好的形式，避免单个vue的代码过长，vue代码的相关性不高。

9. 一个在线通过拖拽的形式创建表单的工具：https://form.making.link/basic-version/#/zh-CN/

10. 表格的表头在滚动时悬浮在顶部，这个功能在branch项目中有用到。

11. 如果要在表头中加el-input之类的输入框，记得`<template slot="header" slot-scope="scope">`的`slot-scope="scope""`要写上，不然el-input每次修改后，值又变回去了。