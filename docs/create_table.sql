/*Table structure for table `t_admin_department` */

CREATE TABLE `t_admin_department` (
                                      `id` bigint NOT NULL AUTO_INCREMENT,
                                      `deleted` tinyint(1) DEFAULT '0' COMMENT '软删除，0未删，1已删',
                                      `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                      `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                      `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门名称',
                                      `parent_id` int DEFAULT NULL COMMENT '父级id，0为最顶层',
                                      `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
                                      `seq` int DEFAULT NULL COMMENT '排序，从小到大',
                                      `webkey` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '由前端生成的key',
                                      `create_user_id` bigint DEFAULT NULL COMMENT '创建人id',
                                      `update_user_id` bigint DEFAULT NULL COMMENT '修改人id',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户所属部门';

/*Table structure for table `t_admin_dict` */

CREATE TABLE `t_admin_dict` (
                                `id` int NOT NULL AUTO_INCREMENT,
                                `deleted` tinyint DEFAULT '0',
                                `create_time` datetime DEFAULT NULL,
                                `update_time` datetime DEFAULT NULL,
                                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字典名称',
                                `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字典代号，会被程序引用，修改小心',
                                `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述信息',
                                `create_user_id` bigint DEFAULT NULL,
                                `update_user_id` bigint DEFAULT NULL,
                                PRIMARY KEY (`id`),
                                KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据字典';

/*Table structure for table `t_admin_dict_value` */

CREATE TABLE `t_admin_dict_value` (
                                      `id` bigint NOT NULL AUTO_INCREMENT,
                                      `deleted` tinyint DEFAULT '0',
                                      `create_time` datetime DEFAULT NULL,
                                      `update_time` datetime DEFAULT NULL,
                                      `dict_id` bigint DEFAULT NULL COMMENT '字典id',
                                      `dict_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字典代号，冗余',
                                      `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '值名称',
                                      `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '值代号，会被程序引用，小心修改',
                                      `seq` int DEFAULT NULL COMMENT '排序',
                                      `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
                                      `extra` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '额外信息，这里用json存',
                                      `create_user_id` bigint DEFAULT NULL,
                                      `update_user_id` bigint DEFAULT NULL,
                                      PRIMARY KEY (`id`),
                                      KEY `dict_code` (`dict_code`),
                                      KEY `dict_id` (`dict_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Table structure for table `t_admin_log` */

CREATE TABLE `t_admin_log` (
                               `id` bigint NOT NULL AUTO_INCREMENT,
                               `deleted` tinyint DEFAULT '0',
                               `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                               `update_time` datetime DEFAULT NULL,
                               `user_id` bigint DEFAULT NULL COMMENT '操作者id',
                               `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
                               `security_level` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '安全级别',
                               `operate_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作类型',
                               `ip` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ip地址',
                               `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作内容',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1026 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Table structure for table `t_admin_log_exception` */

CREATE TABLE `t_admin_log_exception` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                         `deleted` tinyint(1) DEFAULT NULL COMMENT '软删除',
                                         `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                         `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                         `user_id` bigint DEFAULT NULL COMMENT '用户id',
                                         `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
                                         `class_method` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '类名+方法名',
                                         `request_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求方法 post/get/...',
                                         `type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '异常类型',
                                         `is_read` tinyint(1) DEFAULT NULL COMMENT '是否已经查看',
                                         `url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求url',
                                         `referer` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'referer',
                                         `params` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求参数',
                                         `ip` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户访问时ip',
                                         `msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '异常信息',
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1661 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='异常信息日志表';

/*Table structure for table `t_admin_log_slow_sql` */

CREATE TABLE `t_admin_log_slow_sql` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `deleted` tinyint(1) DEFAULT NULL COMMENT '软删除',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        `user_id` bigint DEFAULT NULL COMMENT '用户id',
                                        `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
                                        `is_read` tinyint(1) DEFAULT NULL COMMENT '是否已经查看',
                                        `sql` varchar(4096) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'sql查询语句',
                                        `sql_param` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'sql查询参数,仅保留前1024个字符',
                                        `sql_time` bigint DEFAULT NULL COMMENT 'sql查询耗时，毫秒',
                                        `url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求url，含参数',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5618 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='慢SQL日志表';

/*Table structure for table `t_admin_log_slow_web` */

CREATE TABLE `t_admin_log_slow_web` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                        `deleted` tinyint(1) DEFAULT NULL COMMENT '软删除',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        `user_id` bigint DEFAULT NULL COMMENT '用户id',
                                        `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
                                        `is_read` tinyint(1) DEFAULT NULL COMMENT '是否已经查看',
                                        `request_time` bigint DEFAULT NULL COMMENT '请求-响应时间',
                                        `request_method` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求方法 post/get/...',
                                        `url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求url，含参数',
                                        `referer` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'referer',
                                        `ip` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户访问时ip',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1901 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='慢web日志表';

/*Table structure for table `t_admin_task_log` */

CREATE TABLE `t_admin_task_log` (
                                    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增 id',
                                    `create_time` datetime DEFAULT NULL COMMENT '任务开始时的时间',
                                    `update_time` datetime DEFAULT NULL COMMENT '任务结束时的时间',
                                    `cost_ms` int DEFAULT '0' COMMENT '耗时(毫秒)',
                                    `task_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '任务名称',
                                    `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '任务执行状态',
                                    `run_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '运行的机器ip',
                                    `args` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '任务参数',
                                    `timeout_second` int DEFAULT NULL COMMENT '任务执行超时时间（秒）',
                                    `error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '任务报错信息',
                                    PRIMARY KEY (`id`),
                                    KEY `create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='记录任务的执行情况表';

/*Table structure for table `t_admin_log_web` */

CREATE TABLE `t_admin_log_web` (
                                   `id` int NOT NULL AUTO_INCREMENT COMMENT '自增 id',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建的时间，这个相当于请求时的时间',
                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间，这个相当于第二次插入的时间，处理结束的时间',
                                   `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求的url',
                                   `request_uuid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求uuid',
                                   `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求方法',
                                   `username` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求人，不一定有',
                                   `client_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '客户端ip',
                                   `cost_ms` int DEFAULT NULL COMMENT '耗时(毫秒)',
                                   `request_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求体',
                                   `response_body` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '返回体',
                                   PRIMARY KEY (`id`),
                                   KEY `create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=53792 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='记录 java 服务请求的数据';

/*Table structure for table `t_admin_role` */

CREATE TABLE `t_admin_role` (
                                `id` bigint NOT NULL AUTO_INCREMENT,
                                `deleted` tinyint DEFAULT '0',
                                `create_time` datetime DEFAULT NULL,
                                `update_time` datetime DEFAULT NULL,
                                `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色编码，用于系统代码层标识',
                                `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色名称',
                                `is_independent` tinyint NOT NULL DEFAULT '0' COMMENT '是否独立权限，默认否',
                                `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色描述',
                                `role_group` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色分组名称，可空',
                                `create_user_id` bigint DEFAULT NULL,
                                `update_user_id` bigint DEFAULT NULL,
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色表';

/*Table structure for table `t_admin_role_url` */

CREATE TABLE `t_admin_role_url` (
                                    `id` int NOT NULL AUTO_INCREMENT,
                                    `deleted` tinyint DEFAULT '0',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `update_time` datetime DEFAULT NULL,
                                    `role_id` bigint DEFAULT NULL COMMENT '角色id',
                                    `url_id` bigint DEFAULT NULL COMMENT 'url id',
                                    `create_user_id` bigint DEFAULT NULL,
                                    `update_user_id` bigint DEFAULT NULL,
                                    PRIMARY KEY (`id`),
                                    KEY `role_id` (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=287 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='url对应的角色控制表';

/*Table structure for table `t_admin_url` */

CREATE TABLE `t_admin_url` (
                               `id` bigint NOT NULL AUTO_INCREMENT,
                               `deleted` tinyint DEFAULT '0',
                               `create_time` datetime DEFAULT NULL,
                               `update_time` datetime DEFAULT NULL,
                               `parent_id` bigint DEFAULT NULL COMMENT '父级，目录和菜单的父级一定是目录',
                               `type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限类型：文件夹、菜单、其它',
                               `url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'url，当目录时为空，菜单时必须是明确url，其它可以是url正则表达式',
                               `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标题',
                               `icon` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图标 css class',
                               `description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
                               `seq` int DEFAULT NULL COMMENT '排序，从小到大',
                               `webkey` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '由前端生成的key',
                               `create_user_id` bigint DEFAULT NULL,
                               `update_user_id` bigint DEFAULT NULL,
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=148 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Table structure for table `t_admin_url_ignore` */

CREATE TABLE `t_admin_url_ignore` (
                                      `id` int NOT NULL AUTO_INCREMENT,
                                      `deleted` tinyint DEFAULT '0',
                                      `create_time` datetime DEFAULT NULL,
                                      `update_time` datetime DEFAULT NULL,
                                      `url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '忽略的url',
                                      `create_user_id` bigint DEFAULT NULL,
                                      `update_user_id` bigint DEFAULT NULL,
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

/*Table structure for table `t_admin_user` */

CREATE TABLE `t_admin_user` (
                                `id` int NOT NULL AUTO_INCREMENT,
                                `deleted` tinyint DEFAULT '0' COMMENT '软删除，0未删，1已删',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                `disabled` tinyint DEFAULT '0' COMMENT '是否已禁用',
                                `user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户名',
                                `real_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '真实姓名',
                                `password` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码',
                                `is_admin` tinyint DEFAULT '0' COMMENT '是否管理员',
                                `phone` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机号',
                                `email` varchar(128) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
                                `company` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '公司名称',
                                `department_id` int DEFAULT NULL COMMENT '部门id',
                                `position` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职务名称',
                                `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
                                `signature` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '个性签名',
                                `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源：LOCAL（本地），LDAP',
                                `create_user_id` bigint DEFAULT NULL,
                                `update_user_id` bigint DEFAULT NULL,
                                PRIMARY KEY (`id`),
                                KEY `dept_id` (`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=146 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='后台用户';

/*Table structure for table `t_admin_user_role` */

CREATE TABLE `t_admin_user_role` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `deleted` tinyint DEFAULT '0',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `update_time` datetime DEFAULT NULL,
                                     `user_id` int DEFAULT NULL COMMENT 'admin userId',
                                     `role_id` int DEFAULT NULL COMMENT '角色id',
                                     `expire_time` datetime DEFAULT NULL COMMENT '过期时间，空则表示用不过期',
                                     `create_user_id` bigint DEFAULT NULL,
                                     `update_user_id` bigint DEFAULT NULL,
                                     PRIMARY KEY (`id`),
                                     KEY `userId_role` (`user_id`,`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=77 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户拥有的角色表';
