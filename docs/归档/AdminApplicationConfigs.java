package com.pugwoo.admin.utils;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 拿应用自身定义的参数值，也即admin-env-${env}.properties这个配置文件
 * <AUTHOR> 2016年10月27日 15:52:44
 * 
 * 2018年4月8日 11:30:57 仅用于admin项目
 */
public class AdminApplicationConfigs {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(AdminApplicationConfigs.class);

	private static Properties prop = new Properties();
	
	static {
		String env = System.getProperty("env");
		if(env == null || env.trim().isEmpty()) {
			env = "${env}";
		}
		String fileName = "/admin-env-" + env.trim() + ".properties";

		LOGGER.info("load application config file:{}", fileName);
		InputStream in = AdminApplicationConfigs.class.getResourceAsStream(fileName);
		if(in != null) {
			try {
				prop.load(in);
			} catch (IOException e) {
				LOGGER.error("prop load exception", e);
			}
		} else {
			LOGGER.error("cannot find properties:{}", fileName);
		}
	}
	
	/**
	 * 拿系统配置，会自动根据环境来拿；配置文件在admin-env-${env}.properties文件中
	 * @param key
	 * @return null if not found
	 */
	public static String getConfig(String key) {
		return prop.getProperty(key);
	}

}
