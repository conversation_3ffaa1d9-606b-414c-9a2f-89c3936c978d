## 1. 关于循环依赖的问题

如果starter中的bean注入出现了循环依赖，将导致使用starter的程序启动不起来。有一种做法是明确指定@ComponentScan的包，可以解决问题。但还是推荐使用懒加载的方式来解决循环依赖，甚至从设计上完全避免循环依赖。

## 2. 异常处理器顺序

使用`@validated`对对象进行校验时采用异常捕获处理方式，会抛出`BindException`异常，采用实现接口`HandlerExceptionResolver`方式无法正常捕获，因为在`
org.springframework.web.servlet.DispatcherServlet#processHandlerException`中是按照这些解析器的顺序来处理异常的，一旦前面的解析器处理产生结果，后面的将不再执行。
而SpringMVC默认有多个异常解析器，`DefaultHandlerExceptionResolver`就是其中一个，而且它处理了BindException。且在我们自定义的异常处理之前执行并产生结果。

解决方法：
1. 在我们自定义的异常处理器上注解 @Order(level:num)，解析器顺序，越小越先执行，spring默认的有三个，order为0,1,2

2. 采用springboot的注解方式
   ```java
   @ControllerAdvice
   @Component
   @ResponseBody
   public class BindExceptionHandler {
   
   	@ExceptionHandler(BindException.class)   // 指定要捕获的异常
   	public String bindExceptionhandler(HttpServletRequest request,
   	                                   BindException ex) {
   		//处理代码
   	}
   }

   ```


