package com.pugwoo.admin_demo.utils;

import java.io.IOException;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.pugwoo.admin.utils.AdminApplicationConfigs;
import com.pugwoo.wooutils.json.JSON;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;

/**
 * 上传文件到七牛
 * http://developer.qiniu.com/code/v7/sdk/java.html
 * <AUTHOR> 简化代码和注释说明
 */
public class UploadUtils {
	
	private static final Logger log = LoggerFactory.getLogger(UploadUtils.class);
	
	private static String ACCESS_KEY = AdminApplicationConfigs.getConfig("admin.qiniu.accessKey");
	private static String SECRET_KEY = AdminApplicationConfigs.getConfig("admin.qiniu.secretKey");
	private static String BUCKETNAME = AdminApplicationConfigs.getConfig("admin.qiniu.bucketName");
	private static String HTTP_PREFIX = AdminApplicationConfigs.getConfig("admin.qiniu.httpPrefix");

	// 密钥配置
	private static Auth auth = Auth.create(ACCESS_KEY, SECRET_KEY);
	// 创建上传对象
	private static UploadManager uploadManager = new UploadManager();

	// 简单上传，使用默认策略，只需要设置上传的空间名就可以了
	public static String getUpToken() {
		return auth.uploadToken(BUCKETNAME);
	}
	
	/**
	 * 上传文件到七牛，返回的上传文件的文件名，文件名是UUID随机的
	 * 
	 * @param file
	 * @param prefix 前缀
	 * @param suffix 文件后缀，例如.jpg
	 * @return 返回null表示上传失败，否则返回完整的http url
	 * @throws IOException 
	 * @throws JsonMappingException 
	 * @throws JsonParseException 
	 */
	public static String upload(byte[] file, String prefix, String suffix) throws JsonParseException, JsonMappingException, IOException {
		try {
			String key = (prefix == null ? "" : prefix) + 
					UUID.randomUUID().toString() + (suffix == null ? "" : suffix);
			Response res = uploadManager.put(file, key, getUpToken());

			Map<String, Object> object = JSON.parseToMap(res.bodyString());
			if(object == null) {
				return null;
			}
			// key就是前面我们自己指定的上传后文件名
			return HTTP_PREFIX + object.get("key"); 
		} catch (QiniuException e) {
			log.error("qiniu upload fail", e);
			return null;
		}
	}
	
}
