package com.pugwoo.admin.web.interceptor;

import com.pugwoo.admin.bean.AdminBaseDO;
import com.pugwoo.dbhelper.DBHelperInterceptor;

import java.util.List;

/**
 * description:
 *      对于继承自com.pugwoo.admin.bean.AdminBaseDO的类
 *          1.在其创建时
 *              自动设置 createUserId
 *          2.在其更新时
 *              自动设置 updateUserId
 *          3.在其软删除时
 *              自动设置 updateUserId
 * <AUTHOR>
 * @date 2018-05-04
 */
public class AdminDOAutoSetUserInfoInterceptor extends DBHelperInterceptor{

    @Override
    public boolean beforeInsert(List<Object> list) {
        AdminUserLoginContext context = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
        if (context != null) {
            for (Object t : list) {
                setCreateUserInfo(t, context);
            }
        }
        return true;
    }

    @Override
    public boolean beforeUpdate(List<Object> tList, String setSql, List<Object> setSqlArgs) {
        AdminUserLoginContext context = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
        if (context != null) {
        	for(Object t : tList) {
        		setUpdateUserInfo(t, context);
        	}
        }
        return true;
    }

    @Override
    public boolean beforeUpdateAll(Class<?> clazz, String sql,
    		List<String> customsSets, List<Object> customsParams, List<Object> args) {
        AdminUserLoginContext context = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
        if (context != null) {
            if (AdminBaseDO.class.isAssignableFrom(clazz)) {
                customsSets.add("update_user_id = ?");
                customsParams.add(context.getUserId());
            }
        }
        return true;
    }

    @Override
    public boolean beforeDelete(List<Object> tList) {
        AdminUserLoginContext context = AdminUserLoginInterceptor.getPossibleAdminUserLoginContext();
        if (context != null) {
        	for(Object t : tList) {
        		setUpdateUserInfo(t, context);
        	}
        }
        return true;
    }

    /**
     * 判断对象是否继承自 AdminBaseDO
     *      是则设置创建用户id，创建用户名
     */
    private void setCreateUserInfo(Object t, AdminUserLoginContext context) {
        if (t instanceof AdminBaseDO) {
            ((AdminBaseDO) t).setCreateUserId(context.getUserId());
            ((AdminBaseDO) t).setUpdateUserId(null); // 不允许用户自己指定，下同
            ((AdminBaseDO) t).setDeleted(false);
        }
    }

    /**
     * 判断对象是否继承自 AdminBaseDO
     *      是则设置更新用户id，更新用户名
     */
    private void setUpdateUserInfo(Object t, AdminUserLoginContext context) {
        if (t instanceof AdminBaseDO) {
            ((AdminBaseDO) t).setUpdateUserId(context.getUserId());
            // 更新这里不能设置其它值为null，会在updateWithNull时覆盖数据导致丢失数据
        }
    }
}
