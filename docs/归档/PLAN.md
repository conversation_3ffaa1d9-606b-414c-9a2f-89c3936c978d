# 开发计划

## 2018-06

1. 增加通用访问记录，支持开启关闭
2. [done] 动态字典设计及支持
3. [done] 个人资料编辑页
4. [done] 当配置的url在扫描的url中没有时，将其显示为红色
5. [done] 提供展开和收起所有url的按钮(使用一个按钮)，当保存url树结构时，不要全量刷新树结构
6. [done] 所有页面去掉.json的后缀
7. [done] 用户信息增加email和个人签名，个人资料编辑页也加上
8. [done] 角色编号提示不可随意修改
9. 增加用户操作日志、页面访问日志、数据库修改日志的查询页面
14.系统参数页面可配置
16.[done] 部门树形结构后面显示该部门的人数
26.增加admin_mid code，代表机器唯一识别，用于记录用户访问列表

## 2018-05

1. [done]菜单强制刷新，无需重新登录 
2. [done]列出系统的所有未纳入url管理的url
3. [done]redis新增模糊查询key
4. [done]权限控制,在拦截器中做
5. [done]bug: 目录修改后，不会直接同步更新左侧
6. [done]url节点增加webkey
7. [done]去掉required每次都弹出
8. [done]异常日志 未读按钮hover时提示【标记为已读】
9. [done]异常requestbody改成parameters
10.[done] 去掉url的目录拥有角色
11.[done] 增加慢sql记录，记录时，记录下关联的web url
12.[done] 部门点击时，显示该部门成员，不支持修改
13.[done] redis非必须，登陆态存内存中，重启则失效；该方式适用于本地demo演示
15.[done] 增加管理员标记字段，可操作com.pugwoo.admin下页面
17.[done] 菜单放到根目录放不了的问题
18.[done] 对于本地环境，即env值为空的环境，不限制url权限，不校验csrf
19.[done]角色管理的用户列表增加一列：真实姓名
20.[done] common-utils Form提供手机号、正则等校验工具
21.[done]监控3个列表支持一键全部已读
22.[done] 将7牛云上传从admin中移除，由实际项目方去实现，提供admin-demo示例
23.[done] 优化Resouces工具，对于请求超过2秒的，提示浮层；没有超过2秒则不提示，如果是网络慢提示网络问题
24.[done] 修复权限url判断bug,当url是空时
25.[done] 异常记录器不记录AdminException，把AdminException改名为AdminInnerException
