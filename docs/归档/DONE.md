1. WebResponseWrapper对undertow并不兼容，但是tomcat没问题，这个以后再试试能不能统一，同时看看它依赖的common-io能不能去掉。https://my.oschina.net/mifans/blog/879668 2020年6月22日 09:48:08 看了下代码后，这个common-io主要用了TeeOutputStream，估计不好去，也不好自己重复写一个，所以还是用common-io这个依赖不变。只是不兼容undertow，这个也不处理。

2. RepeatedlyReadRequestWrapper缓存的input stream有爆内存的问题，设计方案是超过10M的请求，存到磁盘上。由于这个项目主要是面向管理后台，所以被此类攻击的可能性较低；即便被攻击，等待应用重启即可。另外存磁盘也是有问题的，磁盘的文件没办法在合适的时间点去清理，另外1T的磁盘也存不了多少次请求（10万次），何况一般也没有1T的磁盘。



