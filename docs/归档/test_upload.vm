#parse('/velocity/admin_components/user_select.vm')

<style>
/*这个是对element单个图片上传的框的样式，如果是图片墙，则不需要着4个*/
    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
        border-color: #20a0ff;
    }
    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 178px;
        height: 178px;
        line-height: 178px;
        text-align: center;
    }
    .avatar {
        width: 178px;
        height: 178px;
        display: block;
    }
    
 /*对于图片墙，遇见过+不居中的问题，然后用style="line-height: inherit;"就可以了，加在+图标上*/
</style>

<div id="app" v-cloak>

<user-select v-model="userId"></user-select>
<span>选择的userId: {{userId}}</span>

<!-- vue方式 -->
<el-upload class="avatar-uploader"
        action="${_contextPath_}/admin_upload/upload"
        :show-file-list="false"
        :on-success="handleAvatarSuccess"
        :before-upload="beforeAvatarUpload">
    <img v-if="imageUrl" :src="imageUrl" class="avatar">
    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
</el-upload>

<!-- 下面这个是原生的方式 -->
<form action="${_contextPath_}/admin_upload/upload" method="post" enctype="multipart/form-data">
    <input type="file" name="file" />
    附带信息:<input name="info"/>
    <input type="submit" value="上传文件" />
</form>

</div>

<script>
var vm = new Vue({
    el: '#app',
    data: {
        imageUrl: '',
        userId: ''
    },
    methods: {
        handleAvatarSuccess: function(res, file) {
            this.imageUrl = URL.createObjectURL(file.raw);
        },
        beforeAvatarUpload: function(file) {
            const isJPG = file.type === 'image/jpeg';
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isJPG) {
                this.$message.error('上传头像图片只能是 JPG 格式!');
            }
            if (!isLt2M) {
                this.$message.error('上传头像图片大小不能超过 2MB!');
            }
            return isJPG && isLt2M;
        }
    }
});
</script>

