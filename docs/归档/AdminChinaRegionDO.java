package com.pugwoo.admin.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * 中国行政区域数据 来自官方数据: http://www.stats.gov.cn/tjsj/tjbz/tjyqhdmhcxhfdm/
 * 目前最新是2016年版本:
 * http://www.stats.gov.cn/tjsj/tjbz/tjyqhdmhcxhfdm/2016/index.html
 */
@Data
@Table("t_admin_china_region")
public class AdminChinaRegionDO {

	@Column(value = "id", isKey = true)
	private Integer id;

	@Column(value = "area_name")
	private String areaName;

	@Column(value = "parent_id")
	private Integer parentId;

	@Column(value = "short_name")
	private String shortName;

	@Column(value = "area_code")
	private Integer areaCode;

	@Column(value = "zip_code")
	private Integer zipCode;

	@Column(value = "pinyin")
	private String pinyin;

	@Column(value = "lng")
	private String lng;

	@Column(value = "lat")
	private String lat;

	@Column(value = "level")
	private Integer level;

	@Column(value = "position")
	private String position;

	@Column(value = "sort")
	private Integer sort;

}
