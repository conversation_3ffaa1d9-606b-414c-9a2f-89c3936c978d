# 项目相关约定

## Admin自身一些命名约定

所有admin相关java代码都在`com.pugwoo.admin`包下。数据库表以`t_admin`开头。

为避免和用户项目冲突，所有admin的链接，均以`admin_`开个头，目前admin自带的链接如下：

   URL                         | 功能
------------------------------ | -------------------
/{contextPath}/                | admin首页
/{contextPath}/admin_user/**   | admin后台用户相关
/{contextPath}/admin_login/**  | admin登录相关  
/{contextPath}/admin_static/** | admin自带静态文件，如js css img
/{contextPath}/admin_log/**    | admin日志记录

但如果项目有多个拦截器，建议按java包来判断是否走该拦截器，而非按照url来。

## 关于数据库字段的一些约定

1. 主键使用int或bigint自增，对应Java使用Long类型
2. 用户登录名为varchar(32)类型
3. 日期时间使用datetime类型
4. 枚举类型使用字母描述，数据库类型为varchar(16)或char(16)，不建议超过16个字符
5. Java中的Boolean类型在数据库中使用tinyint类型

## 关于web层的一些约定

1. url使用(小写+下划线)或(驼峰)的形式，ajax不加后缀.json