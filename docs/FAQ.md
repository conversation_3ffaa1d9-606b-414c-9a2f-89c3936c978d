#### 1. 打印请求日志与上传文件CommonsMultipartResolver

目前打印请求日志的功能不支持CommonsMultipartResolver，只支持SpringBoot官方默认实现StandardMultipartFile。所以如果要开启打印日志请求，则不要在项目中指定使用CommonsMultipartResolver，请直接用SpringBoot的上传组件即可。

根据网上教程，一般用CommonsMultipartResolver的原因是好拿到上传后临时保存在磁盘的文件，使用StandardMultipartFile可以这样获取到，这里还没有加强类型校验：

```java
    private void log(MultipartFile file) throws Exception {
        Class<?> clazz = file.getClass();
        Field field = clazz.getDeclaredField("part");
        field.setAccessible(true);

        ApplicationPart part = (ApplicationPart) field.get(file);
        Field field2 = part.getClass().getDeclaredField("fileItem");
        field2.setAccessible(true);
        FileItem fileItem = (FileItem) field2.get(part);

        if(!(fileItem instanceof DiskFileItem)) {
            return;
        }
        DiskFileItem dfi = (DiskFileItem) fileItem;

        File tempFile = dfi.getStoreLocation();
        // tempFile就是保存在磁盘的文件了
   }
```

#### 2. web请求打印功能，返回值的打印部分，不支持undertow，支持tomcat。

因此，如果使用undertow，将无法正常打印出web请求的返回数据。由于目前tomcat是spring boot的默认web server，同时undertow的市场占用率并不高，我也比较过undertow在内存、性能方面和tomcat的差异，优势并不算明显，因此决定暂不解决不支持undertow的问题。