如果要使用admin封装的Resource工具往后台post提交数据，那么就必须按admin接收方式的形式来。（当然，admin并没有限定用json POST的方式提交到后台，只是那样的话，就不能用admin封装的Resource工具了）

目前admin采取的POST方式是queryString的形式，也即POST和GET的数据格式是一样的，都长这样：

```
a=123&b=456&c=789
```

唯一的不同是GET的这段参数是放在url中，而POST是放在post body中。

现在有一种比较流行的是用Json的格式提交到后台，后台是这样接收的：

```
public void func(@RequestBody XxxDTO xxxDTO)
```

但是这样有一个缺点，就是只能用一个DTO去接收。最麻烦的情况下，需要为每个前端url的接收参数创建一个DTO，没办法自由组合。而admin的做法是提供一个@JsonParam的注解，可以指定将某一个参数从json字符串转成DTO。

例如上面的参数b是json，对应于BbbDTO；参数c是json，对应于CccDTO，而参数a还是字符串，则前端传参这样传：

```
a=123&b={"name":"b"}&c={"code":"FHK"}
```

后台这样接收参数，GET和POST都可以：

```
public void func(String a,
                 @JsonParam("b") BbbDTO bbbDTO,
                 @JsonParam("c") CccDTO cccDTO)
```

所以，上面的json post用admin这种方式也可以实现，就是admin只有一个参数的一个特例。只是前端给过来的参数必须是queryString的形式。